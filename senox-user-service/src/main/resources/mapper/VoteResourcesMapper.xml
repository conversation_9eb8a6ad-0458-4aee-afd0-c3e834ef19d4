<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.user.mapper.VoteResourcesMapper">

    <select id="findMaxSerial" parameterType="java.lang.Long" resultType="java.lang.Integer">
        select max(serial) from u_vote_resources where activity_id = #{activityId} and is_disabled = 0
    </select>

    <select id="count" parameterType="com.senox.user.vo.VoteResourcesSearchVo" resultType="int">
        select count(1) from u_vote_resources
        <where>
            <if test="keywords != null and keywords != ''">
                and (name like concat('%', #{keywords}, '%') or serial like concat('%', #{keywords}, '%'))
            </if>
            <if test="category != null">
                and category = #{category}
            </if>
            <if test="activityId != null">
                and activity_id = #{activityId}
            </if>
            and is_disabled = 0
        </where>
    </select>

    <select id="list" parameterType="com.senox.user.vo.VoteResourcesSearchVo"  resultType="com.senox.user.vo.VoteResourcesVo">
        select id, activity_id, name, serial, category, description, numbers, thumbnail, original
        from u_vote_resources
        <where>
            <if test="keywords != null and keywords != ''">
                and (name like concat('%', #{keywords}, '%') or serial like concat('%', #{keywords}, '%'))
            </if>
            <if test="category != null">
                and category = #{category}
            </if>
            <if test="activityId != null">
                and activity_id = #{activityId}
            </if>
            and is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY id DESC
            </otherwise>
        </choose>
        <if test="page">
            limit ${offset}, ${pageSize}
        </if>
    </select>
</mapper>
