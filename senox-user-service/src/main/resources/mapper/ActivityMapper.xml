<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.user.mapper.ActivityMapper">


    <select id="count" parameterType="com.senox.user.vo.ActivitySearchVo" resultType="int">
        select count(1) from u_activity
        <where>
            <if test="name != null and name != ''">
                and name like concat('%', #{name}, '%')
            </if>
            <if test="startTime != null">
                and start_time >= #{startTime}
            </if>
            <if test="endTime != null">
                and end_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="category != null">
                and category = #{category}
            </if>
            and is_disabled = 0
        </where>
    </select>

    <select id="list" parameterType="com.senox.user.vo.ActivitySearchVo" resultType="com.senox.user.vo.ActivityVo">
        select id, name, start_time, end_time, uuid, url, bg_url, bg_color, font_color
             , remark, share_title, share_blurb, share_url, blurb, stop_time, limit_num, status, category
        from u_activity
        <where>
            <if test="name != null and name != ''">
                and name like concat('%', #{name}, '%')
            </if>
            <if test="startTime != null">
                and start_time >= #{startTime}
            </if>
            <if test="endTime != null">
                and end_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="category != null">
                and category = #{category}
            </if>
            and is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY id DESC
            </otherwise>
        </choose>
        <if test="page">
            limit ${offset}, ${pageSize}
        </if>
    </select>

</mapper>
