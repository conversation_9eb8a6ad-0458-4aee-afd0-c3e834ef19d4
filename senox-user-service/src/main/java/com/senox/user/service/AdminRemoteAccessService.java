package com.senox.user.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.domain.AdminRemoteAccess;
import com.senox.user.mapper.AdminRemoteAccessMapper;
import com.senox.user.vo.AdminRemoteAccessSearchVo;
import com.senox.user.vo.AdminRemoteAccessVo;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/26 8:39
 */
@Service
public class AdminRemoteAccessService extends ServiceImpl<AdminRemoteAccessMapper, AdminRemoteAccess> {

    /**
     * 添加远程开门权限
     *
     * @param remoteAccess
     */
    public Long addRemoteAccess(AdminRemoteAccess remoteAccess) {
        AdminRemoteAccess dbItem = getOne(new QueryWrapper<AdminRemoteAccess>().lambda()
                .eq(AdminRemoteAccess::getAdminUserId, remoteAccess.getAdminUserId())
                .eq(AdminRemoteAccess::getDeviceId, remoteAccess.getDeviceId())
                .eq(AdminRemoteAccess::getDisabled, Boolean.FALSE));
        if (dbItem != null) {
            return dbItem.getId();
        }
        if (remoteAccess.getCreateTime() == null) {
            remoteAccess.setCreateTime(LocalDateTime.now());
        }
        if (remoteAccess.getModifiedTime() == null) {
            remoteAccess.setModifiedTime(LocalDateTime.now());
        }
        save(remoteAccess);
        return remoteAccess.getId();
    }

    /**
     * 删除远程开门权限
     *
     * @param id
     */
    public void deleteRemoteAccess(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        removeById(id);
    }

    /**
     * 根据id获取远程开门权限
     *
     * @param id
     * @return
     */
    public AdminRemoteAccessVo findRemoteAccessById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? getBaseMapper().findRemoteAccessById(id) : null;
    }

    /**
     * 远程开门权限列表
     *
     * @param search
     * @return
     */
    public PageResult<AdminRemoteAccessVo> list(AdminRemoteAccessSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }

        // 总记录数
        int totalSize = getBaseMapper().count(search);
        search.prepare();

        if (totalSize <= search.getOffset()) {
            return PageResult.emptyPage();
        }

        // list
        List<AdminRemoteAccessVo> resultList = getBaseMapper().list(search);
        return PageUtils.resultPage(search, totalSize, resultList);
    }

}
