package com.senox.user.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.*;
import com.senox.common.vo.PageResult;
import com.senox.user.constant.PrizeDrawNumberType;
import com.senox.user.constant.UserConst;
import com.senox.user.domain.Activity;
import com.senox.user.domain.Prize;
import com.senox.user.domain.PrizeDrawNumber;
import com.senox.user.domain.PrizeRecords;
import com.senox.user.mapper.PrizeRecordsMapper;
import com.senox.user.utils.ContextUtils;
import com.senox.user.vo.PrizeRecordsSearchVo;
import com.senox.user.vo.PrizeRecordsVo;
import com.senox.user.vo.PrizeSearchVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

/**
 * <AUTHOR>
 * @date 2025/4/11 11:17
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PrizeRecordsService extends ServiceImpl<PrizeRecordsMapper, PrizeRecords> {

    private final ActivityService activityService;
    private final PrizeService prizeService;
    private final PrizeDrawNumberService drawNumberService;

    /**
     * 抽奖
     * @param activityId
     * @param openid
     * @return
     */
    public PrizeRecords drawPrize(Long activityId, String openid) {
        //校验活动
        Activity activity = activityService.findById(activityId);
        activityService.checkStatus(activity);
        if (StringUtils.isBlank(openid)) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER, "未知的参与人！");
        }
        String key = String.format(UserConst.Cache.PRIZE_DRAW_NUMBERS, activityId, openid);
        Long val = RedisUtils.incr(key, -1);
        log.info("{}用户抽奖次数-1, 剩余次数：{}", openid, val);
        if (val < 0) {
            RedisUtils.incr(key, 1);
            log.info("{}用户抽奖次数+1, 剩余次数：{}", openid, val);
            throw new BusinessException("您的抽奖次数不够了！");
        }
        PrizeRecords records = new PrizeRecords();
        records.setOpenid(openid);
        records.setPrizeId(null);
        records.setIsWin(Boolean.FALSE);
        records.setIsVerify(Boolean.FALSE);
        records.setActivityId(activityId);
        ContextUtils.initEntityCreator(records);
        ContextUtils.initEntityModifier(records);
        records.setCreateTime(LocalDateTime.now());
        records.setModifiedTime(LocalDateTime.now());
        int recordsCount = getRecordsCount(activityId);
        log.info("总抽奖人数：{}, 抽奖人数限制：{}", recordsCount, activity.getLimitNum());
        if (recordsCount < activity.getLimitNum()) {
            int num = ThreadLocalRandom.current().nextInt(1, activity.getLimitNum() - recordsCount + 1); // [1, 101) → 即 [1, 100]
            List<Prize> availablePrizes = getPrizes(activityId);
            int remainingNum = availablePrizes.stream().mapToInt(Prize::getRemainingNum).sum();
            log.info("剩余奖品数量：{}", remainingNum);
            if (num <= remainingNum) {
                int index = ThreadLocalRandom.current().nextInt(1, availablePrizes.size() + 1);
                Prize prize = availablePrizes.get(index - 1);
                log.info("{} -- 用户中奖了,奖品是：{}", openid, JsonUtils.object2Json(prize));

                if (prizeService.reduceRemainingNum(prize.getId())) {
                    log.info("扣减成功！");
                    records.setPrizeId(prize.getId());
                    records.setIsWin(true);
                    records.setPrizeName(prize.getName());
                    records.setUuid(StringUtils.randStr(8));
                }
            }
        }
        save(records);
        return records;
    }

    /**
     * 增加抽奖次数
     * @param drawNumber
     */
    public void increaseDrawNumber(PrizeDrawNumber drawNumber) {
        boolean saved = drawNumberService.saveDrawNumber(drawNumber);
        if (saved) {
            String key = String.format(UserConst.Cache.PRIZE_DRAW_NUMBERS, drawNumber.getActivityId(), drawNumber.getOpenid());
            Long val = RedisUtils.incr(key, 1);
            log.info("{}用户通过{}方式抽奖次数+1, 剩余次数：{}", drawNumber.getOpenid(), PrizeDrawNumberType.fromValue(drawNumber.getType()).getName(), val);
        }
    }

    /**
     * 抽奖剩余可以次数
     * @param activityId
     * @param openid
     * @return
     */
    public Integer availableNumber(Long activityId, String openid) {
        String key = String.format(UserConst.Cache.PRIZE_DRAW_NUMBERS, activityId, openid);
        RedisUtils.incr(key, 1);
        Long val = RedisUtils.incr(key, -1);
        log.info("{}：用户抽奖次数加一再减一，剩余次数：{}", openid, val);
        return val.intValue();
    }

    /**
     * 根据id获取记录
     * @param id
     * @return
     */
    public PrizeRecordsVo findPrizeRecordsVoById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return getBaseMapper().findPrizeRecordsVoById(id);
    }

    /**
     * 根据uuid查询获奖记录
     * @param uuid
     * @return
     */
    public PrizeRecords findByUuid(String uuid) {
        if (StringUtils.isBlank(uuid)) {
            return null;
        }
        return getOne(new QueryWrapper<PrizeRecords>().lambda().eq(PrizeRecords::getUuid, uuid));
    }

    /**
     * 根据uuid查询获奖记录
     * @param uuid
     * @return
     */
    public PrizeRecordsVo findPrizeRecordsVoByUuid(String uuid) {
        if (StringUtils.isBlank(uuid)) {
            return null;
        }
        return getBaseMapper().findPrizeRecordsVoByUuid(uuid);
    }

    /**
     * 抽奖记录总数
     * @param searchVo
     * @return
     */
    public int countRecords(PrizeRecordsSearchVo searchVo) {
        return getBaseMapper().countRecords(searchVo);
    }


    /**
     * 抽奖记录分页
     * @param searchVo
     * @return
     */
    public List<PrizeRecordsVo> listRecords(PrizeRecordsSearchVo searchVo) {
        return getBaseMapper().listRecords(searchVo);
    }

    /**
     * 抽奖记录分页
     * @param searchVo
     * @return
     */
    public PageResult<PrizeRecordsVo> pageRecords(PrizeRecordsSearchVo searchVo) {
        return PageUtils.commonPageResult(searchVo, () -> countRecords(searchVo), () -> listRecords(searchVo));
    }

    /**
     * 兑奖
     * @param uuid
     */
    public void verifyPrize(String uuid) {
        PrizeRecords records = findByUuid(uuid);
        if (records == null || !BooleanUtils.isTrue(records.getIsWin())) {
            throw new BusinessException("未中奖！");
        }
        if (BooleanUtils.isTrue(records.getIsVerify())) {
            throw new BusinessException("已兑奖！");
        }
        records.setIsVerify(Boolean.TRUE);
        records.setModifiedTime(LocalDateTime.now());
        ContextUtils.initEntityModifier(records);
        updateById(records);
    }

    /**
     * 获取活动剩余奖品
     * @param activityId
     * @return
     */
    private List<Prize> getPrizes(Long activityId) {
        PrizeSearchVo searchVo = new PrizeSearchVo();
        searchVo.setActivityId(activityId);
        searchVo.setRemainingNumCheck(Boolean.TRUE);
        searchVo.setPage(false);
        return prizeService.listPrize(searchVo);
    }

    /**
     * 获取活动总参与数量
     * @param activityId
     * @return
     */
    private int getRecordsCount(Long activityId) {
        PrizeRecordsSearchVo searchVo = new PrizeRecordsSearchVo();
        searchVo.setActivityId(activityId);
        return countRecords(searchVo);
    }

}
