package com.senox.user.config;

import com.senox.context.AdminContext;
import com.senox.context.AdminUserDto;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

/**
 * <AUTHOR>
 * @Date 2021/1/11 9:01
 */
public class AdminUserClientInterceptor implements RequestInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(AdminUserClientInterceptor.class);

    @Value("${senox.adminFilter.invokeToken:}")
    private String invokeToken;

    @Override
    public void apply(RequestTemplate requestTemplate) {
        // 从应用上下文取出用户信息，放入Feign请求头中
        AdminUserDto adminUser = AdminContext.getUser();

        if (adminUser != null) {
            logger.info("======================= token: {}, wechatToken:{}", adminUser.getToken(), adminUser.getWechatToken());
            if (adminUser.getToken() != null && adminUser.getToken().length() > 0) {
                logger.info("Feign invoke with token。。。 {}", adminUser.getToken());
                requestTemplate.header(AdminContext.HEADER_AUTHORIZATION, adminUser.getToken());
                return;
            }

            if (adminUser.getWechatToken() != null && adminUser.getWechatToken().length() > 0) {
                logger.info("Feign invoke with wechat appId {} token。。。 {}", adminUser.getMpAppId(), adminUser.getWechatToken());
                requestTemplate.header(AdminContext.HEADER_WECHAT_MP, adminUser.getMpAppId());
                requestTemplate.header(AdminContext.HEADER_WECHAT_AUTH, adminUser.getWechatToken());
                return;
            }

            if (adminUser.getAuthCredentials() != null && adminUser.getAuthCredentials().length() > 0) {
                logger.info("Feign invoke with credentials。。。 {}", adminUser.getAuthCredentials());
                requestTemplate.header(AdminContext.HEADER_AUTH_CREDENTIALS, adminUser.getAuthCredentials());
                return;
            }
            if (adminUser.getAccessToken() != null && adminUser.getAccessToken().length() > 0) {
                logger.info("Feign invoke with access token。。。 {}", adminUser.getAccessToken());
                requestTemplate.header(AdminContext.HEADER_ACCESS_TOKEN, adminUser.getAccessToken());
                return;
            }
            if (adminUser.getFeignToken() != null && adminUser.getFeignToken().length() > 0) {
                logger.info("Feign invoke with feign token。。。 {}", adminUser.getFeignToken());
                requestTemplate.header(AdminContext.HEADER_FEIGN_TOKEN, adminUser.getFeignToken());
                return;
            }
        }

        if (invokeToken != null && invokeToken.length() > 0) {
            logger.info("Feign invoke with define token。。。 {}", invokeToken);
            requestTemplate.header(AdminContext.HEADER_AUTHORIZATION, invokeToken);
        }
    }
}
