package com.senox.user.service;

import com.senox.common.vo.DataSepDto;
import com.senox.user.domain.Holiday;
import com.senox.user.mapper.HolidayMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/4/19 9:17
 */
@Service
public class HolidayService {

    @Autowired
    private HolidayMapper holidayMapper;

    @Transactional(rollbackFor = Exception.class)
    public void batchSaveHoliday(List<Holiday> holidayList) {
        if (CollectionUtils.isEmpty(holidayList)) {
            return;
        }

        // holiday in dateList
        List<LocalDate> dateList = holidayList.stream().map(Holiday::getHoliday).collect(Collectors.toList());
        List<Holiday> srcList = holidayMapper.listHolidayInDate(dateList);
        // 对比结果
        DataSepDto<Holiday> sepResult = sepHoliday(srcList, holidayList);

        // save
        batchAddHoliday(sepResult.getAddList());
        batchUpdateHoliday(sepResult.getUpdateList());
    }

    /**
     * 批量添加假期
     * @param holidayList
     */
    public void batchAddHoliday(List<Holiday> holidayList) {
        if (CollectionUtils.isEmpty(holidayList)) {
            return;
        }
        holidayMapper.batchAddHoliday(holidayList);
    }

    /**
     * 批量更新假期
     * @param holidayList
     */
    public void batchUpdateHoliday(List<Holiday> holidayList) {
        if (CollectionUtils.isEmpty(holidayList)) {
            return;
        }
        holidayMapper.batchUpdateHoliday(holidayList);
    }

    /**
     * 批量删除假期
     * @param dateList
     */
    public void batchDelHoliday(List<LocalDate> dateList) {
        if (CollectionUtils.isEmpty(dateList)) {
            return;
        }
        holidayMapper.batchDeleteHoliday(dateList);
    }

    /**
     * 查询日期范围内的
     * @param startDate
     * @param endDate
     * @return
     */
    public List<Holiday> listHoliday(LocalDate startDate, LocalDate endDate) {
        if (startDate == null || endDate == null || startDate.isAfter(endDate)) {
            return Collections.emptyList();
        }
        return holidayMapper.listHolidayInRange(startDate, endDate);
    }

    public boolean isHoliday(LocalDate date) {
        return !CollectionUtils.isEmpty(holidayMapper.listHolidayInDate(Collections.singletonList(date)));
    }

    /**
     * 对比数据，区分addList， updateList
     * @param srcList
     * @param targetList
     * @return
     */
    private DataSepDto<Holiday> sepHoliday(List<Holiday> srcList, List<Holiday> targetList) {
        DataSepDto<Holiday> result = new DataSepDto<>();
        if (CollectionUtils.isEmpty(srcList)) {
            result.setAddList(targetList);
        } else {
            List<Holiday> addList = new ArrayList<>(targetList.size());
            List<Holiday> updateList = new ArrayList<>(targetList.size());
            for (Holiday item : targetList) {
                if (srcList.contains(item)) {
                    updateList.add(item);
                } else {
                    addList.add(item);
                }
            }
            result.setAddList(addList);
            result.setUpdateList(updateList);
        }
        return result;
    }
}
