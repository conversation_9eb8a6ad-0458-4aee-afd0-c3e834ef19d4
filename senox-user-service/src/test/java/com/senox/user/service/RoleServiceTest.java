package com.senox.user.service;

import com.senox.user.BaseTest;
import com.senox.user.domain.Role;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/27 8:38
 */
public class RoleServiceTest extends BaseTest {

    @Autowired
    private RoleService roleService;

    @Test
    public void saveRole() {
        Role role1 = mockRole();
        List<Long> cosList1 = mockCosList(randLong(1, 50), 10);
        Long result1 = roleService.addRole(role1, cosList1);
        Assertions.assertTrue(result1 > 0L);

        Role dbRole1 = roleService.findRoleById(result1);
        Assertions.assertEquals(role1, dbRole1);
        List<Long> dbRoleCos1 = roleService.listRoleCos(result1);
        Assertions.assertEquals(cosList1.size(), dbRoleCos1.size());
        for (Long item : cosList1) {
            Assertions.assertTrue(dbRoleCos1.contains(item));
        }

        Role role2 = new Role();
        role2.setId(result1);
        role2.setName(randStr(20));
        role2.setModifierId(randLong(1, 100));
        role2.setModifierName(randStr(20));
        List<Long> cosList2 = mockCosList(randLong(1, 60), 5);
        Assertions.assertTrue(roleService.updateRole(role2, cosList2));

        Role dbRole2 = roleService.findRoleById(result1);
        Assertions.assertEquals(role2.getName(), dbRole2.getName());
        List<Long> dbRoleCos2 = roleService.listRoleCos(result1);
        Assertions.assertEquals(cosList2.size(), dbRoleCos2.size());
        for (Long item : cosList2) {
            Assertions.assertTrue(dbRoleCos2.contains(item));
        }
    }


    private Role mockRole() {
        Role result = new Role();
        result.setName(randStr(20));
        result.setCreatorId(randLong(1, 100));
        result.setCreatorName(randStr(20));
        result.setModifierId(randLong(1, 100));
        result.setModifierName(randStr(20));
        return result;
    }

    private List<Long> mockCosList(Long baseIndex, int size) {
        List<Long> result = new ArrayList<>(size);
        for (int i = 0; i < size; i++) {
            result.add(baseIndex + i);
        }
        return result;
    }
}