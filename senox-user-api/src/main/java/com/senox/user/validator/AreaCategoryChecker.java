package com.senox.user.validator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @Date 2020/12/30 15:02
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = AreaCategoryValidator.class)
public @interface AreaCategoryChecker {

    String message() default "Invalid area category.";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
