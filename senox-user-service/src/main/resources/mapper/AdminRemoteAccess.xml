<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.user.mapper.AdminRemoteAccessMapper">

    <resultMap id="Result_RemoteAccess" type="com.senox.user.vo.AdminRemoteAccessVo">
        <result property="id" column="id"/>
        <result property="adminUserId" column="admin_user_id"/>
        <result property="adminRealName" column="real_name"/>
        <result property="deviceId" column="device_id"/>
        <result property="deviceName" column="device_name"/>
        <result property="deviceIp" column="device_ip"/>
        <result property="regionId" column="region_id"/>
        <result property="regionName" column="region_name"/>
        <result property="streetId" column="street_id"/>
        <result property="streetName" column="street_name"/>
    </resultMap>

    <select id="findRemoteAccessById" parameterType="java.lang.Long" resultMap="Result_RemoteAccess">
        select uara.id, uara.admin_user_id, uara.device_id, uau.real_name, dac.device_name, dac.device_ip,
               dac.region_id, dac.region_name, dac.street_id, dac.street_name
        from d_access_control dac
               LEFT JOIN u_admin_remote_access uara ON uara.device_id = dac.id
               LEFT JOIN u_admin_user uau ON uara.admin_user_id = uau.id
        where uara.id = #{id}
    </select>

    <select id="count" parameterType="com.senox.user.vo.AdminRemoteAccessSearchVo" resultType="int">
        select count(uara.id) from d_access_control dac
            LEFT JOIN u_admin_remote_access uara ON uara.device_id = dac.id
            LEFT JOIN u_admin_user uau ON uara.admin_user_id = uau.id
        <where>
            <if test="adminUserId != null">
                AND uara.admin_user_id = #{adminUserId}
            </if>
            <if test="null != adminRealName and adminRealName != ''">
                and uau.real_name = #{adminRealName}
            </if>
            <if test="deviceId != null">
                AND uara.device_id = #{deviceId}
            </if>
            <if test="deviceName != null and deviceName != ''">
                AND dac.device_name = #{deviceName}
            </if>
            <if test="regionId != null">
                AND dac.region_id = #{regionId}
            </if>
            <if test="streetId != null">
                AND dac.street_id = #{streetId}
            </if>
            and uara.disabled = 0 and dac.disabled = 0
        </where>
    </select>

    <select id="list" parameterType="com.senox.user.vo.AdminRemoteAccessSearchVo" resultMap="Result_RemoteAccess">
        select uara.id, uara.admin_user_id, uara.device_id, uau.real_name, dac.device_name, dac.device_ip,
               dac.region_id, dac.region_name, dac.street_id, dac.street_name
        from d_access_control dac
               LEFT JOIN u_admin_remote_access uara ON uara.device_id = dac.id
               LEFT JOIN u_admin_user uau ON uara.admin_user_id = uau.id
        <where>
            <if test="adminUserId != null">
                AND uara.admin_user_id = #{adminUserId}
            </if>
            <if test="null != adminRealName and adminRealName != ''">
                and uau.real_name = #{adminRealName}
            </if>
            <if test="deviceId != null">
                AND uara.device_id = #{deviceId}
            </if>
            <if test="deviceName != null and deviceName != ''">
                AND dac.device_name = #{deviceName}
            </if>
            <if test="regionId != null">
                AND dac.region_id = #{regionId}
            </if>
            <if test="streetId != null">
                AND dac.street_id = #{streetId}
            </if>
            and uara.disabled = 0 and dac.disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                order by ${orderStr}
            </when>
            <otherwise>
                order by uara.id
            </otherwise>
        </choose>
        <if test="page">
            limit ${offset}, ${pageSize}
        </if>
    </select>

</mapper>