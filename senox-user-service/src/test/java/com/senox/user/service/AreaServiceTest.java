package com.senox.user.service;

import com.senox.user.BaseTest;
import com.senox.user.constant.AreaCategory;
import com.senox.user.domain.Area;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;


/**
 * <AUTHOR>
 * @Date 2020/12/15 9:59
 */
public class AreaServiceTest extends BaseTest {

    private static final Logger logger = LoggerFactory.getLogger(AreaServiceTest.class);

    @Autowired
    private AreaService areaService;

    @Test
    public void addArea() {
        Area area = mockArea(0);
        long result = areaService.addArea(area);
        Assertions.assertTrue(result > 0L);

        Area dbArea = areaService.findById(result);
        Assertions.assertEquals(area, dbArea);
        Assertions.assertEquals(Integer.valueOf(AreaCategory.PROVINCE.getValue()), dbArea.getCategory());
        logger.info("area: {}", dbArea);
    }

    @Test
    public void listByParentId() {
        Assertions.assertTrue(areaService.listByParentIdAndCategory(randLong(200, 300),
                Arrays.asList(AreaCategory.fromValue(randInt(1, AreaCategory.values().length)))).isEmpty());

        Area parent = mockArea(0);
        long result = areaService.addArea(parent);

        Area child1 = mockArea(result);
        Area child2 = mockArea(result);
        Area child3 = mockArea(result);
        areaService.addArea(child1);
        areaService.addArea(child2);
        areaService.addArea(child3);

        List<Area> areaList = areaService.listByParentIdAndCategory(result, Arrays.asList(AreaCategory.CITY));
        Assertions.assertEquals(3, areaList.size());
        Assertions.assertEquals(Integer.valueOf(AreaCategory.CITY.getValue()), areaList.get(0).getCategory());

        Assertions.assertEquals(child1, areaList.stream().filter(x -> x.getId().equals(child1.getId())).findFirst().get());
        Assertions.assertEquals(child2, areaList.stream().filter(x -> x.getId().equals(child2.getId())).findFirst().get());
        Assertions.assertEquals(child3, areaList.stream().filter(x -> x.getId().equals(child3.getId())).findFirst().get());
        Assertions.assertTrue(areaList.stream().allMatch(x -> x.getParentId().equals(result)));
    }


    private Area mockArea(long parentId) {
        Area result = new Area();
        result.setSerialNo(randNumStr(20));
        result.setName(randStr(20));
        result.setBriefName(randStr(5));
        result.setCategory(parentId == 0 ? AreaCategory.PROVINCE.getValue() : AreaCategory.CITY.getValue());
        result.setParentId(parentId);
        result.setCreatorId(randLong(0, 100));
        result.setCreatorName(randStr(10));
        result.setModifierId(randLong(0, 100));
        result.setModifierName(randStr(10));
        return result;
    }
}