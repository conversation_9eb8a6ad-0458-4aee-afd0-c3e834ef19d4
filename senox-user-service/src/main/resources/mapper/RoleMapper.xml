<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.user.mapper.RoleMapper">

    <!-- 角色 -->
    <resultMap id="Result_Role" type="com.senox.user.domain.Role">
        <id property="id" jdbcType="BIGINT" column="id" />
        <result property="name" jdbcType="VARCHAR" column="name" />
        <result property="disabled" jdbcType="TINYINT" column="is_disabled" />
        <result property="creatorId" jdbcType="BIGINT" column="creator_id" />
        <result property="creatorName" jdbcType="VARCHAR" column="creator_name" />
        <result property="createTime" jdbcType="TIMESTAMP" column="create_time" />
        <result property="modifierId" jdbcType="BIGINT" column="modifier_id" />
        <result property="modifierName" jdbcType="VARCHAR" column="modifier_name" />
        <result property="modifiedTime" jdbcType="TIMESTAMP" column="modified_time" />
    </resultMap>

    <!-- 角色权限关系 -->
    <resultMap id="Result_RoleCos" type="com.senox.user.domain.RoleCos">
        <result property="roleId" jdbcType="BIGINT" column="role_id" />
        <result property="cosId" jdbcType="BIGINT" column="cos_id" />
    </resultMap>

    <!-- 权限 -->
    <resultMap id="Result_CosItem" type="com.senox.user.domain.CosItem">
        <id property="id" jdbcType="BIGINT" column="id" />
        <result property="name" jdbcType="VARCHAR" column="name" />
        <result property="url" jdbcType="VARCHAR" column="url" />
        <result property="parentId" jdbcType="BIGINT" column="parent_id" />
    </resultMap>

    <!-- 添加角色 -->
    <insert id="addRole" parameterType="com.senox.user.domain.Role" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO u_role(
            name, code, creator_id, creator_name, create_time, modifier_id, modifier_name, modified_time
        ) VALUES (
            #{name}, #{code}, #{creatorId}, #{creatorName}, NOW(), #{modifierId}, #{modifierName}, NOW()
        )
    </insert>

    <!-- 更新角色 -->
    <update id="updateRole" parameterType="com.senox.user.domain.Role">
        UPDATE u_role
        <set>
            <if test="name != null and name != ''">
                , name = #{name}
            </if>
            <if test="code != null and code != ''">
                , code = #{code}
            </if>
            , modifier_id = #{modifierId}
            , modifier_name = #{modifierName}
            , modified_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 删除角色 -->
    <delete id="deleteRole" parameterType="java.lang.Long">
        DELETE FROM u_role WHERE id = #{id}
    </delete>

    <!-- 根据id查找角色 -->
    <select id="findById" parameterType="java.lang.Long" resultMap="Result_Role">
        SELECT id, name, code FROM u_role WHERE id = #{id}
    </select>

    <!-- 根据角色名查找角色id -->
    <select id="findIdByName" parameterType="java.lang.String" resultType="java.lang.Long">
        SELECT id FROM u_role WHERE name = #{name} AND is_disabled = 0
    </select>

    <!-- 角色列表 -->
    <select id="listAll" resultMap="Result_Role">
        SELECT id, name, code FROM u_role WHERE is_disabled = 0
    </select>

    <!-- 批量添加角色权限信息 -->
    <insert id="batchAddRoleCos" parameterType="com.senox.user.domain.RoleCos">
        INSERT INTO u_role_cos(
            role_id, cos_id, creator_id, creator_name, create_time, modifier_id, modifier_name, modified_time
        ) VALUES
        <foreach collection="roleCosList" item="item" separator=",">
        (
            #{item.roleId}, #{item.cosId}, #{item.creatorId}, #{item.creatorName}, NOW(), #{item.modifierId}, #{item.modifierName}, NOW()
        )
        </foreach>
    </insert>

    <!-- 删除角色权限 -->
    <delete id="deleteRoleCos" parameterType="java.lang.Long">
        DELETE FROM u_role_cos WHERE role_id = #{roleId}
    </delete>

    <!-- 批量删除角色权限信息 -->
    <delete id="batchDelRoleCos">
        DELETE FROM u_role_cos
        WHERE role_id = #{roleId}
            AND cos_id IN <foreach collection="cosIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </delete>

    <!-- 权限角色统计 -->
    <select id="countRoleCos" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM u_role_cos WHERE cos_id = #{cosId} AND is_disabled = 0
    </select>

    <!-- 角色权限关系列表 -->
    <select id="listRoleCos" parameterType="java.lang.Long" resultType="java.lang.Long">
        SELECT cos_id FROM u_role_cos WHERE role_id = #{roleId} AND is_disabled = 0
    </select>

</mapper>
