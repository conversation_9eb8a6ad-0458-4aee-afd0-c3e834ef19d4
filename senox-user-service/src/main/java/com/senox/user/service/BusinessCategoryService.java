package com.senox.user.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.user.constant.UserConst;
import com.senox.user.domain.BusinessCategory;
import com.senox.user.mapper.BusinessCategoryMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/10/8 11:04
 */
@Service
public class BusinessCategoryService extends ServiceImpl<BusinessCategoryMapper, BusinessCategory> {

    /**
     * 添加经营范围字典
     * @param category
     */
    public Long addCategory(BusinessCategory category) {
        if (checkCategoryDuplicated(category)) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR);
        }

        category.setCreatorId(category.getModifierId());
        category.setCreatorName(category.getModifierName());
        category.setCreateTime(LocalDateTime.now());
        category.setModifiedTime(LocalDateTime.now());

        save(category);

        // clear cache
        RedisUtils.del(UserConst.Cache.KEY_BUSINESS_CATEGORY);
        return category.getId();
    }

    /**
     * 更新经营范围字典
     * @param category
     */
    public void updateCategory(BusinessCategory category) {
        if (!WrapperClassUtils.biggerThanLong(category.getId(), 0L)) {
            return;
        }

        if (checkCategoryDuplicated(category)) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR);
        }
        category.setModifiedTime(LocalDateTime.now());
        updateById(category);

        // clear cache
        RedisUtils.del(UserConst.Cache.KEY_BUSINESS_CATEGORY);
    }

    /**
     * 删除经营范围字典
     * @param id
     */
    public void deleteCategory(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        removeById(id);
        // clear cache
        RedisUtils.del(UserConst.Cache.KEY_BUSINESS_CATEGORY);
    }

    /**
     * 根据编码找范围
     * @param code
     * @return
     */
    public BusinessCategory findByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }

        LambdaQueryWrapper<BusinessCategory> queryWrapper = new QueryWrapper<BusinessCategory>().lambda()
                .eq(BusinessCategory::getCode, code)
                .eq(BusinessCategory::getDisabled, Boolean.FALSE);
        return getOne(queryWrapper);
    }

    /**
     * 获取
     * @return
     */
    public List<BusinessCategory> listAll() {
        List<BusinessCategory> resultList = null;

        // load from cache
        String cachedValue = RedisUtils.get(UserConst.Cache.KEY_BUSINESS_CATEGORY);
        if (!StringUtils.isBlank(cachedValue)) {
            resultList = JsonUtils.json2GenericObject(cachedValue, new TypeReference<List<BusinessCategory>>() {});
        }

        // load from db
        if (CollectionUtils.isEmpty(resultList)) {
            LambdaQueryWrapper<BusinessCategory> queryWrapper = new QueryWrapper<BusinessCategory>().lambda()
                    .eq(BusinessCategory::getDisabled, Boolean.FALSE)
                    .orderByAsc(BusinessCategory::getOrderNum);

            resultList = list(queryWrapper);

            // cache data
            if (!CollectionUtils.isEmpty(resultList)) {
                RedisUtils.set(UserConst.Cache.KEY_BUSINESS_CATEGORY, JsonUtils.object2Json(resultList), UserConst.Cache.TTL_7D);
            }
        }

        return resultList;
    }

    /**
     * 经营范围字典是否已存在
     * @param category
     * @return
     */
    private boolean checkCategoryDuplicated(BusinessCategory category) {
        if (StringUtils.isBlank(category.getCode())) {
            return false;
        }

        BusinessCategory existItem = findByCode(category.getCode());
        return existItem != null && !Objects.equals(category.getId(), existItem.getId());
    }
}
