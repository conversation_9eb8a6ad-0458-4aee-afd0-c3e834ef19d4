package com.senox.user.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.vo.PageResult;
import com.senox.user.convert.AdminRemoteAccessConvertor;
import com.senox.user.domain.AdminRemoteAccess;
import com.senox.user.service.AdminRemoteAccessService;
import com.senox.user.vo.AdminRemoteAccessSearchVo;
import com.senox.user.vo.AdminRemoteAccessVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @date 2023/5/26 10:56
 */
@Api(tags = "远程开门权限")
@RestController
@RequestMapping("/remote/access")
@RequiredArgsConstructor
public class AdminRemoteAccessController extends BaseController {

    private final AdminRemoteAccessService adminRemoteAccessService;
    private final AdminRemoteAccessConvertor convertor;

    @ApiOperation("添加远程权限")
    @PostMapping("/add")
    public Long addRemoteAccess(@Validated(Add.class) @RequestBody AdminRemoteAccessVo adminRemoteAccessVo) {
        AdminRemoteAccess remoteAccess = convertor.toDo(adminRemoteAccessVo);
        initEntityCreator(remoteAccess);
        initEntityModifier(remoteAccess);
        return adminRemoteAccessService.addRemoteAccess(remoteAccess);
    }

    @ApiOperation("删除远程权限")
    @PostMapping("/delete/{id}")
    public void deleteRemoteAccess(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        adminRemoteAccessService.deleteRemoteAccess(id);
    }

    @ApiOperation("获取远程权限")
    @GetMapping("/get/{id}")
    public AdminRemoteAccessVo findRemoteAccessById(@PathVariable Long id){
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return adminRemoteAccessService.findRemoteAccessById(id);
    }

    @ApiOperation("远程权限列表")
    @PostMapping("/list")
    public PageResult<AdminRemoteAccessVo> remoteAccessList(@RequestBody AdminRemoteAccessSearchVo search){
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return adminRemoteAccessService.list(search);
    }
}
