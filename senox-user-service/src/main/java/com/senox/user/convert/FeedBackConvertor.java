package com.senox.user.convert;

import com.senox.common.convert.BaseConvert;
import com.senox.user.domain.FeedBack;
import com.senox.user.vo.FeedBackVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023/6/1 8:37
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface FeedBackConvertor extends BaseConvert<FeedBack, FeedBackVo> {

}
