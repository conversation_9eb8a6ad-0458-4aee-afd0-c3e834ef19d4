package com.senox.user.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.service.WxUserService;
import com.senox.user.vo.WxUserRealtyVo;
import com.senox.user.vo.WxUserRemarkVo;
import com.senox.user.vo.WxUserSearchVo;
import com.senox.user.vo.WxUserVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/7 14:48
 */
@RestController
@RequestMapping("/wxUser")
public class WxUserController extends BaseController {

    @Autowired
    private WxUserService wxUserService;

    @ApiOperation("设置灰度用户")
    @PostMapping("/gray/set/{userId}")
    public void setGrayUser(@PathVariable Long userId) {
        if (!WrapperClassUtils.biggerThanLong(userId, 0L)) {
            throw new InvalidParameterException("无效的用户id");
        }
        updateGrayUser(userId, Boolean.TRUE);
    }

    @ApiOperation("取消灰度用户")
    @PostMapping("/gray/cancel/{userId}")
    public void cancelGrayUser(@PathVariable Long userId) {
        if (!WrapperClassUtils.biggerThanLong(userId, 0L)) {
            throw new InvalidParameterException("无效的用户id");
        }
        updateGrayUser(userId, Boolean.FALSE);
    }

    @ApiOperation("根据openid获取微信用户")
    @GetMapping("/getByOpenid")
    public WxUserVo findByOpenid(@RequestParam(value = "appId", required = false) String appId, @RequestParam("openid") String openid) {
        if (StringUtils.isBlank(openid)) {
            throw new InvalidParameterException();
        }
        return wxUserService.findByOpenid(appId, openid);
    }

    @ApiOperation("微信用户列表")
    @PostMapping("/list")
    public PageResult<WxUserVo> listWxUserPage(@RequestBody WxUserSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return wxUserService.listWxUserPage(searchVo);
    }

    @ApiOperation("绑定物业")
    @PostMapping("/realty/bind")
    public void bindRealty(@RequestBody WxUserRealtyVo userRealty) {
        if (!WrapperClassUtils.biggerThanLong(userRealty.getUserId(), 0L)
                || StringUtils.isBlank(userRealty.getContractNo())) {
            throw new InvalidParameterException();
        }
        wxUserService.bindWxUserRealty(userRealty.getUserId(), userRealty.getContractNo());
    }

    @ApiOperation("解绑物业")
    @PostMapping("/realty/unbind")
    public void unbindRealty(@RequestBody WxUserRealtyVo userRealty) {
        if (!WrapperClassUtils.biggerThanLong(userRealty.getUserId(), 0L)
                || !WrapperClassUtils.biggerThanLong(userRealty.getRealtyId(), 0L)) {
            throw new InvalidParameterException();
        }
        wxUserService.unbindWxUserRealty(userRealty.getUserId(), userRealty.getRealtyId());
    }

    @ApiOperation("微信用户绑定的物业列表")
    @PostMapping("/listRealty/{userId}")
    public List<WxUserRealtyVo> listWxUserRealty(@PathVariable Long userId) {
        if (!WrapperClassUtils.biggerThanLong(userId, 0L)) {
            throw new InvalidParameterException("无效的微信用户id");
        }
        return wxUserService.listWxUserRealty(userId);
    }

    @ApiOperation("微信用户备注更新")
    @PostMapping("/update/remark")
    public void updateWxUserRemark(@RequestBody WxUserRemarkVo remarkVo) {
        wxUserService.updateWxUserRemark(remarkVo);
    }

    @ApiOperation("解绑骑手")
    @PostMapping("/unbindRider/{riderId}")
    public void unbindRider(@PathVariable Long riderId) {
        wxUserService.unbindRider(riderId);
    }

    /**
     * 更新灰度用户
     * @param userId
     * @param gray
     */
    private void updateGrayUser(Long userId, Boolean gray) {
        WxUserVo user = new WxUserVo();
        user.setId(userId);
        user.setGray(gray);
        wxUserService.updateWxUser(user);
    }
}
