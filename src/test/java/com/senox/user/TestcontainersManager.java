package com.senox.user;

import com.senox.user.config.GlobalRedisAutoConfiguration;
import com.senox.user.utils.DockerManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.testcontainers.containers.GenericContainer;

import javax.annotation.PreDestroy;

/**
 * 极简版TestcontainersManager
 */
@TestConfiguration
public class TestcontainersManager {

    private static final Logger logger = LoggerFactory.getLogger(TestcontainersManager.class);

    private static GenericContainer<?> GLOBAL_REDIS_CONTAINER;
    public static RedisTemplate<String, Object> GLOBAL_REDIS_TEMPLATE;

    @Bean
    @Primary
    public RedisConnectionFactory redisConnectionFactory() {
        logger.info("初始化Redis容器和连接工厂");

        DockerManager.ensureDockerRunning();

        if (GLOBAL_REDIS_CONTAINER == null) {
            String redisVersion = "redis:6.2.6";
            GLOBAL_REDIS_CONTAINER = new GenericContainer<>(redisVersion)
                .withExposedPorts(6379)
                .withReuse(true);
            GLOBAL_REDIS_CONTAINER.start();
            logger.info("Redis容器启动: {}:{}",
                GLOBAL_REDIS_CONTAINER.getHost(), GLOBAL_REDIS_CONTAINER.getMappedPort(6379));
        }

        LettuceConnectionFactory factory = new LettuceConnectionFactory(
            GLOBAL_REDIS_CONTAINER.getHost(), GLOBAL_REDIS_CONTAINER.getMappedPort(6379));
        factory.afterPropertiesSet();
        return factory;
    }

    @Bean
    @Primary
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        logger.info("创建RedisTemplate");

        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(new GenericJackson2JsonRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(new GenericJackson2JsonRedisSerializer());
        template.afterPropertiesSet();

        GLOBAL_REDIS_TEMPLATE = template;

        // 异步触发自动配置
        new Thread(() -> {
            try {
                Thread.sleep(2000);
                GlobalRedisAutoConfiguration.autoConfigureRedisUtils();
            } catch (Exception e) {
                logger.warn("自动配置失败: {}", e.getMessage());
            }
        }).start();

        return template;
    }

    @PreDestroy
    public void cleanup() {
        logger.info("清理容器资源");

        if (GLOBAL_REDIS_CONTAINER != null) {
            try {
                GLOBAL_REDIS_CONTAINER.stop();
            } catch (Exception e) {
                logger.error("停止容器失败: {}", e.getMessage());
            }
        }
        DockerManager.stopDocker();
        GLOBAL_REDIS_CONTAINER = null;
        GLOBAL_REDIS_TEMPLATE = null;
    }
}
