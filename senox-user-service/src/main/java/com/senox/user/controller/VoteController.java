package com.senox.user.controller;

import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.user.convert.VoteCategoryConvertor;
import com.senox.user.convert.VoteRecordsConvertor;
import com.senox.user.convert.VoteResourcesConvertor;
import com.senox.user.domain.VoteCategory;
import com.senox.user.domain.VoteRecords;
import com.senox.user.domain.VoteResources;
import com.senox.user.service.VoteCategoryService;
import com.senox.user.service.VoteRecordsService;
import com.senox.user.service.VoteResourcesService;
import com.senox.user.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2025/4/10 10:16
 */
@Api(tags = "投票")
@RestController
@RequiredArgsConstructor
@RequestMapping("/vote")
public class VoteController {

    private final VoteCategoryService categoryService;
    private final VoteCategoryConvertor categoryConvertor;
    private final VoteResourcesService resourcesService;
    private final VoteResourcesConvertor resourcesConvertor;
    private final VoteRecordsService recordsService;
    private final VoteRecordsConvertor recordsConvertor;

    @ApiOperation("添加投票分类")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/category/save")
    public void saveVoteCategory(@RequestBody VoteCategoryVo categoryVo) {
        VoteCategory voteCategory = categoryConvertor.toDo(categoryVo);
        categoryService.saveVoteCategory(voteCategory);
    }

    @ApiOperation("根据id获取投票分类")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/category/get/{id}")
    public VoteCategoryVo findCategoryById(@PathVariable Long id) {
        return categoryConvertor.toVo(categoryService.findById(id));
    }

    @ApiOperation("根据id删除投票类别")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/category/delete/{id}")
    public void deleteVoteCategoryById(@PathVariable Long id) {
        categoryService.deleteVoteCategoryById(id);
    }

    @ApiOperation("投票类别分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/category/page")
    public PageResult<VoteCategoryVo> pageCategoryResult(@RequestBody VoteCategorySearchVo searchVo) {
        return categoryService.page(searchVo);
    }

    @ApiOperation("新增投票资源")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/resources/save")
    public void saveVoteResources(@RequestBody VoteResourcesVo resourcesVo) {
        VoteResources resources = resourcesConvertor.toDo(resourcesVo);
        resourcesService.saveVoteResources(resources);
    }

    @ApiOperation("根据id获取投票资源")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/resources/get/{id}")
    public VoteResourcesVo findResourcesById(@PathVariable Long id) {
        return resourcesConvertor.toVo(resourcesService.findById(id));
    }

    @ApiOperation("根据id删除投票资源")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/resources/delete/{id}")
    public void deleteVoteResourcesById(@PathVariable Long id) {
        resourcesService.deleteVoteResourcesById(id);
    }

    @ApiOperation("投票资源分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/resources/page")
    public PageResult<VoteResourcesVo> pageResourcesResult(@RequestBody VoteResourcesSearchVo searchVo) {
        return resourcesService.page(searchVo);
    }

    @ApiOperation("新增投票记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/records/save")
    public void saveVoteRecords(@RequestBody VoteRecordsVo recordsVo) {
        VoteRecords records = recordsConvertor.toDo(recordsVo);
        recordsService.saveVoteRecords(records);
    }

    @ApiOperation("用户可用次数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/records/availableNumbers/{activityId}")
    public Integer availableNumbers(@PathVariable Long activityId, @RequestParam String openid) {
        return recordsService.availableNumbers(activityId, openid);
    }
}
