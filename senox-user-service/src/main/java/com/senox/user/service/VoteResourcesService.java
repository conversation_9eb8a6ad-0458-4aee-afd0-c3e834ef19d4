package com.senox.user.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.domain.VoteResources;
import com.senox.user.mapper.VoteResourcesMapper;
import com.senox.user.utils.ContextUtils;
import com.senox.user.vo.VoteResourcesSearchVo;
import com.senox.user.vo.VoteResourcesVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/10/16 11:45
 */
@Service
@Slf4j
public class VoteResourcesService extends ServiceImpl<VoteResourcesMapper, VoteResources> {

    /**
     * 添加投票资源
     * @param resources
     */
    public void addVoteResources(VoteResources resources) {
        Integer maxSerial = getBaseMapper().findMaxSerial(resources.getActivityId());
        int serial = maxSerial == null ? 0 : maxSerial;
        resources.setSerial(serial + 1);
        ContextUtils.initEntityCreator(resources);
        ContextUtils.initEntityModifier(resources);
        resources.setCreateTime(LocalDateTime.now());
        resources.setModifiedTime(LocalDateTime.now());
        save(resources);
    }

    /**
     * 更新投票资源
     * @param resources
     */
    public void updateVoteResources(VoteResources resources) {
        VoteResources serial = findBySerialAndActivityId(resources.getSerial(), resources.getActivityId());
        if (serial != null && !Objects.equals(serial.getId(), resources.getId())) {
            throw new BusinessException("该编号已经存在！");
        }
        ContextUtils.initEntityModifier(resources);
        resources.setModifiedTime(LocalDateTime.now());
        resources.setNumbers(null);
        updateById(resources);
    }

    /**
     * 保存投票资源
     * @param resources
     */
    public void saveVoteResources(VoteResources resources) {
        if (!WrapperClassUtils.biggerThanLong(resources.getId(), 0L)) {
            addVoteResources(resources);
        } else {
            updateVoteResources(resources);
        }
    }

    /**
     * 根据id获取投票资源
     * @param id
     * @return
     */
    public VoteResources findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return getById(id);
    }

    /**
     * 根据id删除投票资源
     * @param id
     */
    public void deleteVoteResourcesById(Long id) {
        VoteResources resources = findById(id);
        if (resources == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        resources.setName(resources.getName().concat("_").concat(resources.getId().toString()));
        resources.setDisabled(Boolean.TRUE);
        resources.setModifiedTime(LocalDateTime.now());
        ContextUtils.initEntityModifier(resources);
        updateById(resources);
    }

    /**
     * 增加票数
     * @param id
     * @param numbers
     */
    public void incrNumbers(Long id, Integer numbers) {
        VoteResources resources = findById(id);
        if (resources == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        resources.setNumbers(resources.getNumbers() + numbers);
        updateById(resources);
    }

    /**
     * 统计数量
     * @param searchVo
     * @return
     */
    public int count(VoteResourcesSearchVo searchVo) {
        return getBaseMapper().count(searchVo);
    }

    /**
     * 列表
     * @param searchVo
     * @return
     */
    public List<VoteResourcesVo> list(VoteResourcesSearchVo searchVo) {
        return getBaseMapper().list(searchVo);
    }

    /**
     * 投票活动分页
     * @param searchVo
     * @return
     */
    public PageResult<VoteResourcesVo> page(VoteResourcesSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return PageUtils.commonPageResult(searchVo, () -> getBaseMapper().count(searchVo), () -> getBaseMapper().list(searchVo));
    }

    /**
     * 根据编号和活动Id查询投票资源
     * @param serial
     * @param activityId
     * @return
     */
    private VoteResources findBySerialAndActivityId(Integer serial, Long activityId) {
        if (!WrapperClassUtils.biggerThanInt(serial, 0)
            || !WrapperClassUtils.biggerThanLong(activityId, 0L)) {
            throw new BusinessException("编号和活动不能为空！");
        }
        return getOne(new QueryWrapper<VoteResources>().lambda()
                .eq(VoteResources::getSerial, serial)
                .eq(VoteResources::getActivityId, activityId)
                .eq(VoteResources::getDisabled, Boolean.FALSE));
    }
}
