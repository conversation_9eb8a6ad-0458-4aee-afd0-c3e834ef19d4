package com.senox.user.component;

import com.senox.common.utils.FeignUtils;
import com.senox.dm.api.clients.AccessControlClient;
import com.senox.dm.vo.AccessControlVo;
import com.senox.dm.vo.AccessDeviceRightVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/23 9:44
 */
@Component
@RequiredArgsConstructor
public class AccessControlComponent {

    private final AccessControlClient accessControlClient;

    /**
     * 根据id获取海康门禁设备
     *
     * @param id
     * @return
     */
    public AccessControlVo findAccessControlById(Long id) {
        try {
            return accessControlClient.findById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }

    /**
     * 添加门禁权限
     *
     * @param rightVo
     */
    public void addAccessRight(AccessDeviceRightVo rightVo) {
        try {
            accessControlClient.addAccessRight(rightVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 删除门禁权限
     *
     * @param rightVo
     */
    public void deleteAccessRight(AccessDeviceRightVo rightVo) {
        try {
            accessControlClient.deleteAccessRight(rightVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

    /**
     * 批量添加门禁权限
     * @param rightVoList
     */
    public void batchAddAccessRight(List<AccessDeviceRightVo> rightVoList) {
        try {
            accessControlClient.batchAddAccessRight(rightVoList);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }

}
