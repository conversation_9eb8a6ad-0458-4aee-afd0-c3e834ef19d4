package com.senox.user.service;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.PageUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.common.vo.PageResult;
import com.senox.user.domain.DiningInformation;
import com.senox.user.domain.Employee;
import com.senox.user.mapper.DiningInformationMapper;
import com.senox.user.vo.DiningInformationSearchVo;
import com.senox.user.vo.DiningInformationVo;
import com.senox.user.vo.EmployeeSearchVo;
import com.senox.user.vo.EmployeeVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @date 2023/3/23
 */
@RequiredArgsConstructor
@Service
public class DiningInformationService extends ServiceImpl<DiningInformationMapper, DiningInformation> {

    private final EmployeeService employeeService;


    public void saveDiningInformation(DiningInformation diningInformation) {
        if (!checkEmployeeExist(diningInformation)) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "该用户不存在");
        }
        if (checkDiningInformationExist(diningInformation)) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR, "报餐数据已存在");
        }
        diningInformation.setCreateTime(LocalDateTime.now());
        diningInformation.setModifiedTime(LocalDateTime.now());
        save(diningInformation);
    }


    /**
     * 判断报餐数据是否已经存在
     *
     * @param diningInformation
     * @return
     */
    private boolean checkDiningInformationExist(DiningInformation diningInformation) {
        if (diningInformation.getEmployeeName() == null || diningInformation.getMealDate() == null) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER, "无效的用户名和日期");
        }
        LambdaQueryWrapper<DiningInformation> wrapper = new QueryWrapper<DiningInformation>().lambda()
                .eq(DiningInformation::getEmployeeName, diningInformation.getEmployeeName())
                .eq(DiningInformation::getMealDate, diningInformation.getMealDate());
        DiningInformation information = getBaseMapper().selectOne(wrapper);
        return information != null;
    }


    /**
     * 批量插入
     *
     * @param list
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveDiningInformationBatch(List<DiningInformation> list) {
        //根据日期和员工去重
        list = list.stream().distinct().collect(Collectors.toList());
        list = filterNonExistEmployee(list);

        // 数据记录
        List<DiningInformation> dbList = CollectionUtils.isEmpty(list) ? Collections.emptyList() : getBaseMapper().diningInformationList(list);

        // 比较数据库数据，分情况处理
        DataSepDto<DiningInformation> sepData = separateDiningInformation(dbList, list);
        if (!CollectionUtils.isEmpty(sepData.getAddList())) {
            saveBatch(sepData.getAddList());
        } else if (!CollectionUtils.isEmpty(sepData.getUpdateList())) {
            updateBatchById(sepData.getUpdateList());
        }
    }


    /**
     * 修改报餐信息
     *
     * @param diningInformation
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDiningInformation(DiningInformation diningInformation) {
        if (!checkEmployeeExist(diningInformation)) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "该用户不存在");
        }
        diningInformation.setModifiedTime(LocalDateTime.now());
        return updateById(diningInformation);
    }


    /**
     * 获取报餐信息
     *
     * @param id
     * @return
     */
    public DiningInformationVo findById(Long id) {
        return this.getBaseMapper().findById(id);
    }

    /**
     * 删除报餐信息
     *
     * @param id
     * @return
     */
    public boolean deleteDiningInformation(Long id) {
        return removeById(id);
    }

    /**
     * 查询报餐信息列表
     *
     * @param searchVo
     * @return
     */
    public PageResult<DiningInformationVo> informationList(DiningInformationSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        // total
        int total = this.getBaseMapper().count(searchVo);

        //开始分页查询
        searchVo.prepare();
        if (total <= searchVo.getOffset()) {
            return PageResult.emptyPage();
        }
        //查询分页结果
        List<DiningInformationVo> diningInformationList = this.getBaseMapper().list(searchVo);
        return PageUtils.resultPage(searchVo, total, diningInformationList);
    }

    /**
     * 判断员工是否存在
     *
     * @param diningInformation
     * @return
     */
    private boolean checkEmployeeExist(DiningInformation diningInformation) {
        if (diningInformation.getEmployeeName() == null) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER, "无效的用户名");
        }
        Employee employee = employeeService.findByName(diningInformation.getEmployeeName());
        return employee != null;
    }

    /**
     * 只处理存在的员工
     * @param list
     * @return
     */
    private List<DiningInformation> filterNonExistEmployee(List<DiningInformation> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        List<String> employeeNames = list.stream().map(DiningInformation::getEmployeeName).distinct().collect(toList());

        // 员工查询
        EmployeeSearchVo search = new EmployeeSearchVo();
        search.setUsernames(employeeNames);
        search.setPage(false);
        List<EmployeeVo> employees = employeeService.listEmployee(search);
        List<String> existEmployeeNames = employees.stream().map(EmployeeVo::getUsername).collect(toList());
        return list.stream().filter(x -> existEmployeeNames.contains(x.getEmployeeName())).collect(toList());
    }


    /**
     * 数据库和导入的数据作比较
     *
     * @param srcList
     * @param targetList
     * @return
     */
    private DataSepDto<DiningInformation> separateDiningInformation(List<DiningInformation> srcList,
                                                                    List<DiningInformation> targetList) {
        List<DiningInformation> addList = new ArrayList<>(targetList.size());
        List<DiningInformation> updateList = new ArrayList<>(targetList.size());
        List<DiningInformation> removeList = new ArrayList<>(targetList.size());

        if (CollectionUtils.isEmpty(srcList)) {
            for (DiningInformation item : targetList) {
                item.setCreateTime(LocalDateTime.now());
                item.setModifiedTime(LocalDateTime.now());
            }
            addList = targetList;

        } else {
            Map<String, DiningInformation> srcMap = srcList.stream()
                    .collect(Collectors.toMap(this::buildDiningInformationKey, Function.identity()));

            for (DiningInformation item : targetList) {

                DiningInformation srcItem = srcMap.get(buildDiningInformationKey(item));
                if (srcItem == null) {
                    // 新增
                    item.setCreateTime(LocalDateTime.now());
                    item.setModifiedTime(LocalDateTime.now());
                    addList.add(item);

                } else {
                    // 更新
                    item.setModifiedTime(LocalDateTime.now());
                    item.setId(srcItem.getId());
                    updateList.add(item);
                }
            }
        }

        DataSepDto<DiningInformation> result = new DataSepDto<>();
        result.setAddList(addList);
        result.setUpdateList(updateList);
        result.setRemoveList(removeList);
        return result;
    }

    private String buildDiningInformationKey(DiningInformation information) {
        return information.getMealDate().toString().concat("_").concat(information.getEmployeeName());
    }
}
