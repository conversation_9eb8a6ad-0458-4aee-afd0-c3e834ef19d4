package com.senox.user.listener;

import com.senox.common.utils.JsonUtils;
import com.senox.dm.constant.EmployeeType;
import com.senox.dm.vo.AccessDeviceRightVo;
import com.senox.user.component.AccessControlComponent;
import com.senox.user.event.ResidentAccessSyncEvent;
import com.senox.user.vo.ResidentAccessVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/25 14:17
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ResidentAccessSyncListener {

    private final AccessControlComponent accessControlComponent;


    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = ResidentAccessSyncEvent.class, fallbackExecution = true)
    public void accessSyncListener(ResidentAccessSyncEvent event) {
        log.info("收到住户权限同步事件 {}", JsonUtils.object2Json(event));

        List<ResidentAccessVo> accessVoList = event.getAccessVoList();

        List<AccessDeviceRightVo> rightVoList = buildAccessRightVoList(accessVoList);
        if (CollectionUtils.isEmpty(rightVoList)) {
            return;
        }

        accessControlComponent.batchAddAccessRight(rightVoList);
    }

    private List<AccessDeviceRightVo> buildAccessRightVoList(List<ResidentAccessVo> accessVoList) {
        List<AccessDeviceRightVo> rightVoList = new ArrayList<>(accessVoList.size());
        for (ResidentAccessVo accessVo : accessVoList) {
            AccessDeviceRightVo rightVo = new AccessDeviceRightVo();
            rightVo.setDeviceIps(new String[]{accessVo.getDeviceIp()});
            rightVo.setEmployeeNo(accessVo.getResidentNo());
            rightVo.setEmployeeName(accessVo.getResidentName());
            rightVo.setFaceUrl(accessVo.getFaceUrl());
            rightVo.setEmployeeType(EmployeeType.RESIDENT.getValue());
            rightVoList.add(rightVo);
        }
        return rightVoList;
    }
}
