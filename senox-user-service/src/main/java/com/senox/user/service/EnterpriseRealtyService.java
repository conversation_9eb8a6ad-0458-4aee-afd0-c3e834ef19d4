package com.senox.user.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.SeparateUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.context.AdminUserDto;
import com.senox.user.domain.EnterpriseRealty;
import com.senox.user.mapper.EnterpriseRealtyMapper;
import com.senox.user.vo.EnterpriseRealtyVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/9 11:09
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EnterpriseRealtyService extends ServiceImpl<EnterpriseRealtyMapper, EnterpriseRealty> {

    /**
     * 保存经营企业与物业关系
     * @param enterpriseId
     * @param realtySerials
     * @param operator
     */
    public void saveEnterpriseRealty(Long enterpriseId, List<EnterpriseRealtyVo> realtySerials, AdminUserDto operator) {
        if (realtySerials == null) {
            return;
        }

        // 非拆租物业
        List<String> uniqueItems = realtySerials.stream()
                .filter(x -> !BooleanUtils.isTrue(x.getRealtyShared()))
                .map(EnterpriseRealtyVo::getRealtySerial)
                .collect(Collectors.toList());
        saveEnterpriseRealtyUnique(enterpriseId, uniqueItems, operator);

        // 拆租物业
        if (realtySerials.size() > uniqueItems.size()) {
            List<String> shareItems = realtySerials.stream()
                    .filter(x -> BooleanUtils.isTrue(x.getRealtyShared()))
                    .map(EnterpriseRealtyVo::getRealtySerial)
                    .collect(Collectors.toList());
            saveEnterpriseRealtyShare(enterpriseId, shareItems, operator);
        }

    }

    /**
     * 经营户关联非拆租物业
     * @param enterpriseId
     * @param realtySerials
     * @param operator
     */
    public void saveEnterpriseRealtyUnique(Long enterpriseId, List<String> realtySerials, AdminUserDto operator) {
        if (realtySerials == null) {
            return;
        }
        checkEnterpriseRealtyDuplicated(enterpriseId, realtySerials);

        // 源数据
        List<EnterpriseRealty> srcList = listByEnterpriseId(enterpriseId);
        // 目标数据
        List<EnterpriseRealty> targetList = buildEnterpriseRealty(enterpriseId, realtySerials, operator);

        // 数据对比
        DataSepDto<EnterpriseRealty> sepDto = SeparateUtils.separateData(srcList, targetList);
        log.info("Unique save enterprise realty separated result {}.", JsonUtils.object2Json(sepDto));

        // 处理数据
        addEnterpriseRealtyBatch(sepDto.getAddList());
        removeEnterpriseRealtyBatch(sepDto.getRemoveList());
    }

    /**
     * 经营户关联拆租物业（一间物业下有多个经营户）
     * @param enterpriseId
     * @param realtySerials
     * @param operator
     */
    public void saveEnterpriseRealtyShare(Long enterpriseId, List<String> realtySerials, AdminUserDto operator) {
        if (CollectionUtils.isEmpty(realtySerials)) {
            return;
        }

        List<EnterpriseRealty> list = listByRealtySerials(realtySerials);
        for (String realtySerial : realtySerials) {
            List<EnterpriseRealty> shareList = list.stream().filter(x -> Objects.equals(x.getRealtySerial(), realtySerial)).collect(Collectors.toList());
            char st = 'A';
            boolean oldShared = false;
            for (EnterpriseRealty shareItem : shareList) {
                shareItem.setRealtyAlias(realtySerial + st);
                shareItem.setModifierId(operator.getUserId());
                shareItem.setModifierName(operator.getUsername());
                st += 1;
                oldShared = oldShared || Objects.equals(shareItem.getEnterpriseId(), enterpriseId);

                updateByEnterpriseRealty(shareItem);
            }

            if (!oldShared) {
                EnterpriseRealty er = new EnterpriseRealty(enterpriseId, realtySerial);
                er.setRealtyAlias(realtySerial + st);
                er.setModifierId(operator.getUserId());
                er.setModifierName(operator.getUsername());
                addEnterpriseRealty(er);
            }
        }

    }


    /**
     * 添加经营户与物业关系
     * @param enterpriseRealty
     */
    public void addEnterpriseRealty(EnterpriseRealty enterpriseRealty) {
        enterpriseRealty.setCreatorId(enterpriseRealty.getModifierId());
        enterpriseRealty.setCreatorName(enterpriseRealty.getModifierName());
        enterpriseRealty.setCreateTime(LocalDateTime.now());
        enterpriseRealty.setModifiedTime(LocalDateTime.now());

        save(enterpriseRealty);
    }

    /**
     * 批量保存经营户与物业关系
     * @param list
     */
    public void addEnterpriseRealtyBatch(List<EnterpriseRealty> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        for (EnterpriseRealty item : list) {
            item.setCreatorId(item.getModifierId());
            item.setCreatorName(item.getModifierName());
            item.setCreateTime(LocalDateTime.now());
            item.setModifiedTime(LocalDateTime.now());
        }
        saveBatch(list);
    }

    /**
     * 更新经营户与物业关系
     * @param realty
     */
    public void updateByEnterpriseRealty(EnterpriseRealty realty) {
        LambdaQueryWrapper<EnterpriseRealty> wrapper = new QueryWrapper<EnterpriseRealty>().lambda()
                .eq(EnterpriseRealty::getEnterpriseId, realty.getEnterpriseId())
                .eq(EnterpriseRealty::getRealtySerial, realty.getRealtySerial());

        EnterpriseRealty updateInfo = new EnterpriseRealty();
        updateInfo.setRealtyAlias(realty.getRealtyAlias());
        updateInfo.setModifierId(realty.getModifierId());
        updateInfo.setModifierName(realty.getModifierName());
        updateInfo.setModifiedTime(LocalDateTime.now());

        update(updateInfo, wrapper);
    }

    /**
     * 根据物业编号重置别名
     * @param realtySerials
     */
    public void resetAliasByRealtySerial(List<String> realtySerials) {
        if (CollectionUtils.isEmpty(realtySerials)) {
            return;
        }

        getBaseMapper().resetAliasByRealtySerial(realtySerials);
    }

    /**
     * 批量删除营企业物业关系
     * @param list
     */
    public void removeEnterpriseRealtyBatch(List<EnterpriseRealty> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<Long> ids = list.stream().map(EnterpriseRealty::getId).distinct().collect(Collectors.toList());
        removeByIds(ids);
    }

    /**
     * 根据企业id删除企业物业关系
     * @param enterpriseId
     */
    public void removeByEnterpriseId(Long enterpriseId) {
        if (!WrapperClassUtils.biggerThanLong(enterpriseId, 0L)) {
            return;
        }

        Wrapper<EnterpriseRealty> wrapper = new QueryWrapper<EnterpriseRealty>().lambda().eq(EnterpriseRealty::getEnterpriseId, enterpriseId);
        remove(wrapper);
    }


    /**
     * 根据企业id查找物业编号
     * @param enterpriseId
     * @return
     */
    public List<EnterpriseRealty> listByEnterpriseId(Long enterpriseId) {
        if (!WrapperClassUtils.biggerThanLong(enterpriseId, 0L)) {
            return Collections.emptyList();
        }

        Wrapper<EnterpriseRealty> wrapper = new QueryWrapper<EnterpriseRealty>().lambda()
                .eq(EnterpriseRealty::getEnterpriseId, enterpriseId)
                .eq(EnterpriseRealty::getDisabled, Boolean.FALSE);
        return list(wrapper);
    }

    /**
     * 根据企业id查找物业信息
     * @param enterpriseIds
     * @return
     */
    public List<EnterpriseRealtyVo> listVoByEnterpriseIds(List<Long> enterpriseIds) {
        if (CollectionUtils.isEmpty(enterpriseIds)) {
            return Collections.emptyList();
        }

        return getBaseMapper().listVoByEnterpriseIds(enterpriseIds);
    }

    /**
     * 根据企业id查找物业信息，并按企业分组
     * @param enterpriseIds
     * @return
     */
    public Map<Long, List<EnterpriseRealtyVo>> listVoMapByEnterprisesIds(List<Long> enterpriseIds) {
        List<EnterpriseRealtyVo> list = listVoByEnterpriseIds(enterpriseIds);

        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.groupingBy(EnterpriseRealtyVo::getEnterpriseId));
    }

    /**
     * 根据物业编号查找企业
     * @param realtySerials
     * @return
     */
    public List<EnterpriseRealty> listByRealtySerials(List<String> realtySerials) {
        if (CollectionUtils.isEmpty(realtySerials)) {
            return Collections.emptyList();
        }

        Wrapper<EnterpriseRealty> wrapper = new QueryWrapper<EnterpriseRealty>().lambda()
                .in(EnterpriseRealty::getRealtySerial, realtySerials)
                .eq(EnterpriseRealty::getDisabled, Boolean.FALSE);
        return list(wrapper);
    }

    /**
     * 合租但仅有1个经营户的物业列表
     * @return
     */
    public List<String> listUniqueEnterpriseRealtyWithAlias() {
        return getBaseMapper().listUniqueEnterpriseRealtyWithAlias();
    }


    /**
     * 构建企业物业关系
     * @param enterpriseId
     * @param realtySerials
     * @param operator
     * @return
     */
    private List<EnterpriseRealty> buildEnterpriseRealty(Long enterpriseId, List<String> realtySerials, AdminUserDto operator) {
        if (CollectionUtils.isEmpty(realtySerials)) {
            return Collections.emptyList();
        }

        return realtySerials.stream().map(x -> {
            EnterpriseRealty er = new EnterpriseRealty(enterpriseId, x);
            er.setModifierId(operator.getUserId());
            er.setModifierName(operator.getUsername());
            er.setModifiedTime(LocalDateTime.now());
            return er;
        }).collect(Collectors.toList());
    }

    /**
     * 企业物业关系重复校验
     * @param enterpriseId
     * @param realtySerials
     */
    private void checkEnterpriseRealtyDuplicated(Long enterpriseId, List<String> realtySerials) {
        List<EnterpriseRealty> list = listByRealtySerials(realtySerials);
        list = list.stream().filter(x -> !Objects.equals(enterpriseId, x.getEnterpriseId())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(list)) {
            String[] duplicatedSerials = list.stream().map(EnterpriseRealty::getRealtySerial).toArray(String[]::new);
            String duplicatedStr = StringUtils.join(duplicatedSerials, ',');
            throw new BusinessException("重复的企业物业： " + duplicatedStr);
        }
    }

}
