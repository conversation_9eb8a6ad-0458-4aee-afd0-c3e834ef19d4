package com.senox.user.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Docker管理工具类  启动和停止Docker
 */
public class DockerManager {

    private static final Logger logger = LoggerFactory.getLogger(DockerManager.class);

    /**
     * 确保Docker正在运行
     * 如果没有运行，尝试自动启动
     */
    public static boolean ensureDockerRunning() {
        try {
            if (isDockerRunning()) {
                logger.debug("Docker已在运行");
                return true;
            }

            boolean autoStart = Boolean.parseBoolean(
                System.getProperty("testcontainers.docker.autoStart", "true")
            );
            
            if (!autoStart) {
                logger.info("Docker自动启动已禁用");
                return false;
            }

            logger.info("检测到Docker未运行，尝试自动启动...");
            return startDocker();

        } catch (Exception e) {
            logger.error("确保Docker运行失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查Docker是否运行
     */
    public static boolean isDockerRunning() {
        try {
            Process process = Runtime.getRuntime().exec("docker info");
            int exitCode = process.waitFor();
            return exitCode == 0;
        } catch (Exception e) {
            logger.debug("Docker检查失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 停止Docker
     */
    public static boolean stopDocker() {
        try {
            String os = System.getProperty("os.name").toLowerCase();
            logger.info("停止Docker... 操作系统: {}", os);

            boolean success = false;
            if (os.contains("win")) {
                success = stopDockerWindows();
            } else if (os.contains("linux")) {
                success = stopDockerLinux();
            } else if (os.contains("mac")) {
                success = stopDockerMac();
            } else {
                logger.warn("不支持的操作系统: {}", os);
                return false;
            }

            if (success) {
                logger.info("Docker停止成功");
            } else {
                logger.warn("Docker停止失败");
            }

            return success;

        } catch (Exception e) {
            logger.error("停止Docker失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 启动Docker
     */
    public static boolean startDocker() {
        try {
            String os = System.getProperty("os.name").toLowerCase();
            logger.info("启动Docker... 操作系统: {}", os);

            boolean success = false;
            if (os.contains("win")) {
                success = startDockerWindows();
            } else if (os.contains("linux")) {
                success = startDockerLinux();
            } else if (os.contains("mac")) {
                success = startDockerMac();
            } else {
                logger.warn("不支持的操作系统: {}", os);
                return false;
            }

            if (success) {
                logger.info(" Docker启动成功");
            } else {
                logger.warn(" Docker启动失败");
            }

            return success;

        } catch (Exception e) {
            logger.error("启动Docker失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * Windows平台启动Docker
     */
    private static boolean startDockerWindows() {
        try {
            logger.info("Windows：启动Docker Desktop");
            
            // 方法1：启动Docker Desktop
            try {
                String[] commands = {
                    "\"C:\\Program Files\\Docker\\Docker\\Docker Desktop.exe\"",
                    "\"C:\\Program Files (x86)\\Docker\\Docker\\Docker Desktop.exe\"",
                    "cmd /c start \"\" \"Docker Desktop\""
                };
                
                for (String command : commands) {
                    try {
                        Runtime.getRuntime().exec(command);
                        logger.debug("执行启动命令: {}", command);
                        break;
                    } catch (Exception e) {
                        logger.debug("命令执行失败: {}", command);
                    }
                }
            } catch (Exception e) {
                logger.debug("启动Docker Desktop失败: {}", e.getMessage());
            }

            // 方法2：启动Docker服务
            try {
                Process process = Runtime.getRuntime().exec("net start com.docker.service");
                process.waitFor();
                logger.debug("Docker服务启动命令已执行");
            } catch (Exception e) {
                logger.debug("启动Docker服务失败: {}", e.getMessage());
            }

            // 等待Docker就绪
            return waitForDockerReady(90); // Windows需要更长时间

        } catch (Exception e) {
            logger.error("Windows Docker启动失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Linux平台启动Docker
     */
    private static boolean startDockerLinux() {
        try {
            logger.info("Linux：启动Docker服务");
            
            Process process = Runtime.getRuntime().exec("sudo systemctl start docker");
            int exitCode = process.waitFor();
            
            if (exitCode == 0) {
                logger.info("Docker服务启动命令执行成功");
                return waitForDockerReady(30);
            } else {
                logger.warn("Docker服务启动失败，退出码: {}", exitCode);
                return false;
            }

        } catch (Exception e) {
            logger.error("Linux Docker启动失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Mac平台启动Docker
     */
    private static boolean startDockerMac() {
        try {
            logger.info("Mac：启动Docker Desktop");
            
            Process process = Runtime.getRuntime().exec("open -a Docker");
            int exitCode = process.waitFor();
            
            if (exitCode == 0) {
                logger.info("Docker Desktop启动命令执行成功");
                return waitForDockerReady(60);
            } else {
                logger.warn("Docker Desktop启动失败，退出码: {}", exitCode);
                return false;
            }

        } catch (Exception e) {
            logger.error("Mac Docker启动失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Windows平台停止Docker
     */
    private static boolean stopDockerWindows() {
        try {
            logger.info("Windows：停止Docker");
            
            // 停止Docker Desktop
            Runtime.getRuntime().exec("taskkill /f /im \"Docker Desktop.exe\"");
            Thread.sleep(2000);
            
            // 停止Docker服务
            Runtime.getRuntime().exec("net stop docker");
            Runtime.getRuntime().exec("net stop com.docker.service");
            
            // 强制结束相关进程
            Runtime.getRuntime().exec("taskkill /f /im \"dockerd.exe\"");
            Runtime.getRuntime().exec("taskkill /f /im \"com.docker.backend.exe\"");
            
            return true;

        } catch (Exception e) {
            logger.error("Windows Docker停止失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Linux平台停止Docker
     */
    private static boolean stopDockerLinux() {
        try {
            logger.info("Linux：停止Docker服务");
            
            Process process = Runtime.getRuntime().exec("sudo systemctl stop docker");
            int exitCode = process.waitFor();
            
            return exitCode == 0;

        } catch (Exception e) {
            logger.error("Linux Docker停止失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Mac平台停止Docker
     */
    private static boolean stopDockerMac() {
        try {
            logger.info("Mac：停止Docker Desktop");
            
            Process process = Runtime.getRuntime().exec("osascript -e 'quit app \"Docker\"'");
            int exitCode = process.waitFor();
            
            return exitCode == 0;

        } catch (Exception e) {
            logger.error("Mac Docker停止失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 等待Docker就绪
     */
    private static boolean waitForDockerReady(int maxWaitSeconds) {
        logger.info("等待Docker就绪，最多等待{}秒...", maxWaitSeconds);
        
        for (int i = 0; i < maxWaitSeconds; i++) {
            try {
                if (isDockerRunning()) {
                    logger.info(" Docker已就绪，耗时{}秒", i + 1);
                    return true;
                }
                
                if (i % 15 == 0 && i > 0) { // 每15秒输出一次进度
                    logger.info(" 等待Docker启动中... ({}/{}秒)", i + 1, maxWaitSeconds);
                }
                
                Thread.sleep(1000);
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.warn("等待Docker启动被中断");
                return false;
            } catch (Exception e) {
                logger.debug("检查Docker状态失败: {}", e.getMessage());
            }
        }
        
        logger.warn(" 等待Docker启动超时（{}秒）", maxWaitSeconds);
        return false;
    }

}
