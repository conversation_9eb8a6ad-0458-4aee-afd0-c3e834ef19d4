package com.senox.user.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.user.convert.BusinessCategoryConvertor;
import com.senox.user.domain.BusinessCategory;
import com.senox.user.service.BusinessCategoryService;
import com.senox.user.vo.BusinessCategoryVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/8 11:05
 */
@Api(tags = "经营范围")
@RestController
@RequiredArgsConstructor
@RequestMapping("/dict/businessCategory")
public class BusinessCategoryController extends BaseController {

    private final BusinessCategoryService categoryService;
    private final BusinessCategoryConvertor categoryConvertor;

    @ApiOperation("新增经营范围")
    @PostMapping("/add")
    public Long addCategory(@Validated @RequestBody BusinessCategoryVo category) {
        BusinessCategory entity = categoryConvertor.toDo(category);
        initEntityModifier(entity);
        return categoryService.addCategory(entity);
    }

    @ApiOperation("更新经营范围")
    @PostMapping("/update")
    public void updateCategory(@Validated @RequestBody BusinessCategoryVo category) {
        if (!WrapperClassUtils.biggerThanLong(category.getId(), 0L)) {
            throw new InvalidParameterException();
        }

        BusinessCategory entity = categoryConvertor.toDo(category);
        initEntityModifier(entity);
        categoryService.updateCategory(entity);
    }

    @ApiOperation("删除经营范围")
    @PostMapping("/delete/{id}")
    public void deleteCategory(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        categoryService.deleteCategory(id);
    }

    @ApiOperation("更新经营范围列表")
    @PostMapping("/list")
    public List<BusinessCategoryVo> listCategory() {
        List<BusinessCategory> list = categoryService.listAll();
        return CollectionUtils.isEmpty(list) ? Collections.emptyList() : categoryConvertor.toVo(list);
    }


}
