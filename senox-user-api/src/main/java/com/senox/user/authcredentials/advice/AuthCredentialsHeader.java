package com.senox.user.authcredentials.advice;


import lombok.Getter;

@Getter
public class AuthCredentialsHeader {
    private AuthCredentialsHeader(){}

    protected static final String NAME = "S-";
    public static final String APP_KEY = NAME.concat("App-Key");
    public static final String SING = NAME.concat("Sign");
    public static final String CURRENT_TIME = NAME.concat("Current-Time");
    public static final String CHECKSUM = NAME.concat("Checksum");

}
