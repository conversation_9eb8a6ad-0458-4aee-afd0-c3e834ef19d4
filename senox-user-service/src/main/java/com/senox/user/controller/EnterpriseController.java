package com.senox.user.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.convert.EnterpriseConvertor;
import com.senox.user.domain.BusinessCategory;
import com.senox.user.domain.Enterprise;
import com.senox.user.service.BusinessCategoryService;
import com.senox.user.service.EnterpriseRealtyService;
import com.senox.user.service.EnterpriseService;
import com.senox.user.vo.EnterpriseEditVo;
import com.senox.user.vo.EnterpriseRealtyVo;
import com.senox.user.vo.EnterpriseSearchVo;
import com.senox.user.vo.EnterpriseViewVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/13 10:24
 */
@Api(tags = "经营户")
@RestController
@RequiredArgsConstructor
@RequestMapping("/enterprise")
public class EnterpriseController extends BaseController {

    private final EnterpriseService enterpriseService;
    private final EnterpriseRealtyService enterpriseRealtyService;
    private final BusinessCategoryService categoryService;
    private final EnterpriseConvertor enterpriseConvertor;


    @ApiOperation("保存企业")
    @PostMapping("/save")
    public Long saveEnterprise(@Validated @RequestBody EnterpriseEditVo enterprise) {
        Enterprise entity = enterpriseConvertor.editVoToDo(enterprise);
        initEntityModifier(entity);

        return enterpriseService.saveEnterprise(entity, enterprise.getRealtySerials());
    }

    @ApiOperation("删除企业")
    @PostMapping("/delete/{id}")
    public void deleteEnterprise(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        Enterprise enterprise = new Enterprise();
        enterprise.setId(id);
        initEntityModifier(enterprise);
        enterpriseService.deleteEnterprise(enterprise);
    }

    @ApiOperation("设定经营户消防重点场所")
    @PostMapping("/firefighting/emphasis/save")
    public void saveEnterpriseFirefightingEmphasis(@RequestBody List<Long> enterpriseIds) {
        if (CollectionUtils.isEmpty(enterpriseIds)) {
            throw new InvalidParameterException();
        }

        Enterprise enterprise = new Enterprise();
        enterprise.setFirefightingEmphasis(Boolean.TRUE);
        initEntityModifier(enterprise);
        enterpriseService.saveEnterpriseFirefightingEmphasis(enterpriseIds, enterprise);
    }

    @ApiOperation("取消经营户消防重点场所")
    @PostMapping("/firefighting/emphasis/cancel")
    public void cancelEnterpriseFirefightingEmphasis(@RequestBody List<Long> enterpriseIds) {
        if (CollectionUtils.isEmpty(enterpriseIds)) {
            throw new InvalidParameterException();
        }

        Enterprise enterprise = new Enterprise();
        enterprise.setFirefightingEmphasis(Boolean.FALSE);
        initEntityModifier(enterprise);
        enterpriseService.saveEnterpriseFirefightingEmphasis(enterpriseIds, enterprise);
    }

    @ApiOperation("获取企业")
    @GetMapping("/get/{id}")
    public EnterpriseViewVo findEnterpriseById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        Enterprise enterprise = enterpriseService.findById(id);
        if (enterprise == null) {
            return null;
        }

        // 结果
        EnterpriseViewVo result = enterpriseConvertor.toViewVo(enterprise);
        BusinessCategory category = categoryService.findByCode(result.getCategory());
        result.setCategoryDesc(StringUtils.trimToEmpty(category == null ? null : category.getDescription()));
        List<EnterpriseRealtyVo> list = enterpriseRealtyService.listVoByEnterpriseIds(Collections.singletonList(id));
        result.setRealtyList(list == null ? Collections.emptyList() : list);
        return result;
    }

    @ApiOperation("经营户列表")
    @PostMapping("/list")
    public List<EnterpriseViewVo> listEnterprise(@RequestBody EnterpriseSearchVo search) {
        search.setPage(Boolean.FALSE);
        List<EnterpriseViewVo> resultList = enterpriseService.listEnterprise(search);
        prepareEnterpriseRealty(resultList);
        return resultList;
    }

    @ApiOperation("经营户列表页")
    @PostMapping("/page")
    public PageResult<EnterpriseViewVo> listEnterprisePage(@RequestBody EnterpriseSearchVo search) {
        PageResult<EnterpriseViewVo> resultPage = PageUtils.commonPageResult(search,
                () -> enterpriseService.countEnterprise(search),
                () -> enterpriseService.listEnterprise(search));

        prepareEnterpriseRealty(resultPage.getDataList());
        return resultPage;
    }

    @ApiOperation("经营户物业列表")
    @PostMapping("/realty/list")
    public List<EnterpriseRealtyVo> listEnterpriseRealities(@RequestBody List<Long> enterpriseIds) {
        if (CollectionUtils.isEmpty(enterpriseIds)) {
            throw new InvalidParameterException();
        }

        return enterpriseRealtyService.listVoByEnterpriseIds(enterpriseIds);
    }

    /**
     * 企业列表转视图列表
     * @param list
     * @return
     */
    private void prepareEnterpriseRealty(List<EnterpriseViewVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        // 企业id
        List<Long> enterpriseIds = list.stream().map(EnterpriseViewVo::getId).collect(Collectors.toList());
        Map<Long, List<EnterpriseRealtyVo>> enterpriseRealtyMap = enterpriseRealtyService.listVoMapByEnterprisesIds(enterpriseIds);

        for (EnterpriseViewVo item : list) {
            item.setRealtyList(enterpriseRealtyMap.getOrDefault(item.getId(), Collections.emptyList()));
        }
    }

}
