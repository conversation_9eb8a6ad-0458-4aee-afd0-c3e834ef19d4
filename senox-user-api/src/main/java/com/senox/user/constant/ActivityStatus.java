package com.senox.user.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/10/15 16:45
 */
@Getter
public enum ActivityStatus {

    INIT(0, "初始化"),
    EFFECTIVE(1, "生效"),
    UN_EFFECTIVE(2, "未生效");

    private final int number;
    private final String name;

    ActivityStatus(int number, String name) {
        this.number = number;
        this.name = name;
    }

    public static ActivityStatus fromNumber(Integer number) {
        if (null == number) {
            return null;
        }
        for (ActivityStatus item : values()) {
            if (number.equals(item.number)) {
                return item;
            }
        }
        return null;
    }

    public static ActivityStatus fromName(String name) {
        if (null == name) {
            return null;
        }
        for (ActivityStatus item : values()) {
            if (name.equals(item.name)) {
                return item;
            }
        }
        return null;
    }
}
