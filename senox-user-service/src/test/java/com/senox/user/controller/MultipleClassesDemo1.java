package com.senox.user.controller;

import com.senox.common.utils.RedisUtils;
import com.senox.user.BaseControllerTest;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 多个测试类演示 - 第一个类
 */
public class MultipleClassesDemo1 extends BaseControllerTest {

    private static final Logger logger = LoggerFactory.getLogger(MultipleClassesDemo1.class);

    @Test
    public void testClass1_UserCache() {
        logger.info("=== 测试类1：用户缓存场景 ===");
        
        String userId = "class1-user456";
        String cacheKey = String.format("senox:user:%s", userId);
        String userInfo = "{\"id\":456,\"name\":\"测试类1用户\",\"class\":\"demo1\"}";
        
        // 设置用户缓存
        RedisUtils.set(cacheKey, userInfo, 2 * 60 * 60);
        logger.info("设置用户缓存: {} = {}", cacheKey, userInfo);
        
        // 读取验证
        String result = RedisUtils.get(cacheKey);
        assert userInfo.equals(result) : "用户缓存读取失败";
        
        logger.info("用户缓存验证通过");
        
        // 清理
        RedisUtils.del(cacheKey);
        
        logger.info("测试类1完成");
    }

    @Test
    public void testClass1_SessionCache() {
        logger.info("=== 测试类1：会话缓存场景 ===");
        
        String sessionId = "class1-session-abc123";
        String sessionKey = String.format("senox:session:%s", sessionId);
        String sessionData = "{\"userId\":456,\"loginTime\":\"2025-07-28\",\"class\":\"demo1\"}";
        
        // 设置会话缓存，30分钟过期
        RedisUtils.set(sessionKey, sessionData, 30 * 60);
        logger.info("设置会话缓存: {} = {}", sessionKey, sessionData);
        
        // 验证存在
        boolean exists = RedisUtils.hasKey(sessionKey);
        assert exists : "会话缓存应该存在";
        
        // 读取验证
        String result = RedisUtils.get(sessionKey);
        assert sessionData.equals(result) : "会话缓存读取失败";
        
        logger.info("会话缓存验证通过");
        
        // 清理
        RedisUtils.del(sessionKey);
        
        logger.info("测试类1会话测试完成");
    }
}
