<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.user.mapper.ProfessionMapper">

    <resultMap id="Result_Profession" type="com.senox.user.domain.Profession">
        <id property="id" jdbcType="BIGINT" column="id" />
        <result property="name" jdbcType="VARCHAR" column="name" />
        <result property="disabled" jdbcType="TINYINT" column="is_disabled" />
        <result property="creatorId" jdbcType="BIGINT" column="creator_id" />
        <result property="creatorName" jdbcType="VARCHAR" column="creator_name" />
        <result property="createTime" jdbcType="TIMESTAMP" column="create_time" />
        <result property="modifierId" jdbcType="BIGINT" column="modifier_id" />
        <result property="modifierName" jdbcType="VARCHAR" column="modifier_name" />
        <result property="modifiedTime" jdbcType="TIMESTAMP" column="modified_time" />
    </resultMap>

    <!-- 添加行业 -->
    <insert id="addProfession" parameterType="com.senox.user.domain.Profession" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO dict_profession(
            name, creator_id, creator_name, create_time, modifier_id, modifier_name, modified_time
        ) VALUES (
            #{name}, #{creatorId}, #{creatorName}, NOW(), #{modifierId}, #{modifierName}, NOW()
        )
    </insert>

    <!-- 更新行业 -->
    <update id="updateProfession" parameterType="com.senox.user.domain.Profession">
        update dict_profession
        <set>
            <if test="name != null and name != ''">
                , name = #{name}
            </if>
            <if test="disabled != null">
                , is_disabled = #{disabled}
            </if>
            , modifier_id = #{modifierId}
            , modifier_name = #{modifierName}
            , modified_time = NOW()
        </set>
        WHERE id = #{id} AND is_disabled = 0
    </update>

    <!-- 根据id查找行业 -->
    <select id="findById" parameterType="java.lang.Long" resultMap="Result_Profession">
        SELECT id, name FROM dict_profession WHERE id = #{id}
    </select>

    <!-- 根据名字查找id -->
    <select id="findIdByName" parameterType="java.lang.String" resultType="java.lang.Long">
        SELECT id FROM dict_profession WHERE name = #{name}
    </select>

    <!-- 行业列表 -->
    <select id="listAll" resultMap="Result_Profession">
        SELECT id, name FROM dict_profession WHERE is_disabled = 0
    </select>

</mapper>