package com.senox.user.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.user.domain.Company;
import com.senox.user.domain.Employee;
import com.senox.user.service.EmployeeService;
import com.senox.user.vo.CompanyVo;
import com.senox.user.vo.EmployeeSearchVo;
import com.senox.user.vo.EmployeeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/4/1 13:59
 */
@Api(tags = "员工")
@RestController
@RequestMapping("/employee")
public class EmployeeController extends BaseController {

    @Autowired
    private EmployeeService employeeService;

    @ApiOperation("新增员工")
    @PostMapping("/add")
    public Long addEmployee(@Validated({Add.class}) @RequestBody EmployeeVo employeeVo) {
        Employee employee = employeeVo2Entity(employeeVo);
        initEntityCreator(employee);
        initEntityModifier(employee);

        // 员工代理
        List<Long> delegateEmployees = employeeVo.getDelegators() == null ? Collections.emptyList()
                :  employeeVo.getDelegators().stream().map(EmployeeVo::getId).collect(Collectors.toList());
        // 公司代理
        Long delegateCompany = employeeVo.getDelegateCompany() == null || employeeVo.getDelegateCompany().getId() == null
                ? 0L : employeeVo.getDelegateCompany().getId();

        return employeeService.addEmployee(employee, delegateEmployees, delegateCompany);
    }

    @ApiOperation("更新员工信息")
    @PostMapping("/update")
    public void updateEmployee(@Validated({Update.class}) @RequestBody EmployeeVo employeeVo) {
        if (employeeVo.getId() < 1L) {
            throw new InvalidParameterException("无效的id");
        }

        Employee employee = employeeVo2Entity(employeeVo);
        initEntityModifier(employee);

        // 代理人
        List<Long> delegators = employeeVo.getDelegators() == null ? Collections.emptyList()
                :  employeeVo.getDelegators().stream().map(EmployeeVo::getId).collect(Collectors.toList());
        // 公司代理
        Long delegateCompany = employeeVo.getDelegateCompany() == null || employeeVo.getDelegateCompany().getId() == null
                ? 0L : employeeVo.getDelegateCompany().getId();

        employeeService.updateEmployee(employee, delegators, delegateCompany);
    }

    @ApiOperation("根据id获取员工信息")
    @GetMapping("/get/{id}")
    public EmployeeVo getEmployee(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }

        EmployeeVo result = employeeService.findById(id);
        if (result != null) {
            List<Employee> delegateEmployees = employeeService.listDelegateEmployee(id);
            if (!CollectionUtils.isEmpty(delegateEmployees)) {
                result.setDelegators(delegateEmployees.stream().map(this::employee2Vo).collect(Collectors.toList()));
            }

            Company company = employeeService.findDelegateCompany(id);
            if (company != null) {
                result.setDelegateCompany(company2Vo(company));
            }
        }
        return result;
    }

    @ApiOperation("员工分页列表")
    @PostMapping("/list")
    public PageResult<EmployeeVo> listEmployeePage(@RequestBody EmployeeSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }

        PageResult<EmployeeVo> employeePage = employeeService.listEmployeePage(searchVo);
        return employeePage;
    }

    /**
     * 员工视图对象转实体对象
     * @param vo
     * @return
     */
    private Employee employeeVo2Entity(EmployeeVo vo) {
        Employee result = new Employee();
        BeanUtils.copyProperties(vo, result);
        return result;
    }

    /**
     * 员工实例对象转视图对象
     * @param employee
     * @return
     */
    private EmployeeVo employee2Vo(Employee employee) {
        EmployeeVo result = new EmployeeVo();
        BeanUtils.copyProperties(employee, result);
        return result;
    }

    /**
     * 公司实例转视图对象
     * @param company
     * @return
     */
    private CompanyVo company2Vo(Company company) {
        CompanyVo result = new CompanyVo();
        BeanUtils.copyProperties(company, result);
        return result;
    }
}
