package com.senox.user.constant;

/**
 * <AUTHOR>
 * @Date 2021/1/21 8:40
 */
public enum Covid19Category {

    /**
     * 核酸检测
     */
    NAT(1),
    /**
     * 疫苗
     */
    VACCINE(2),
    ;

    private int value;

    Covid19Category(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static Covid19Category fromValue(Integer value) {
        if (value != null) {
            for (Covid19Category item : values()) {
                if (item.getValue() == value) {
                    return item;
                }
            }
        }
        return null;
    }
}
