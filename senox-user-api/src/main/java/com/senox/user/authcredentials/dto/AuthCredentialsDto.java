package com.senox.user.authcredentials.dto;

import com.senox.user.authcredentials.exception.CredentialsException;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023-10-10
 */
@Data
public class AuthCredentialsDto implements Serializable {

    private static final long serialVersionUID = 5411361266676457626L;

    /**
     * 公钥
     */
    private String appKey;

    /**
     * 签名
     */
    private String sign;

    /**
     * 请求时间
     */
    private Long currentTime;

    /**
     * 校验和(appKey+sign+appSecret+hashIteration)
     */
    private String checksum;

    /**
     * 内容
     */
    private String content;


    public AuthCredentialsDto(String appKey, String sign, Long currentTime, String checksum) {
        this.appKey = appKey;
        this.sign = sign;
        this.currentTime = currentTime;
        this.checksum = checksum;
    }

    public AuthCredentialsDto(String appKey, String sign, Long currentTime, String checksum, String content) {
        this(appKey, sign, currentTime, checksum);
        this.content = content;
    }

    public AuthCredentialsDto validator() {
        if (isBlank(appKey) || isBlank(sign) || isBlank(checksum) || null == currentTime) {
            throw new CredentialsException("签名参数丢失");
        }
        return this;
    }


    private boolean isBlank(String str) {
        int strLen;
        if (str != null && (strLen = str.length()) != 0) {
            for (int i = 0; i < strLen; ++i) {
                if (!Character.isWhitespace(str.charAt(i))) {
                    return false;
                }
            }
        }

        return true;
    }
}
