package com.senox.user.validator;

import com.senox.user.constant.AreaCategory;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * <AUTHOR>
 * @Date 2020/12/30 15:03
 */
public class AreaCategoryValidator implements ConstraintValidator<AreaCategoryChecker, Integer> {

    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext constraintValidatorContext) {
        if (value == null) {
            return true;
        }
        AreaCategory category = AreaCategory.fromValue(value);
        return category != null;
    }
}
