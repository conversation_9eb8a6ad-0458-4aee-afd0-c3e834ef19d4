package com.senox.user.controller;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.user.domain.Profession;
import com.senox.user.service.ProfessionService;
import com.senox.user.vo.ProfessionVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.security.InvalidParameterException;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2020/12/31 16:34
 */
@Api(tags = "行业管理")
@RestController
@RequestMapping("/profession")
public class ProfessionController extends BaseController {

    @Autowired
    private ProfessionService professionService;

    @ApiOperation("添加行业")
    @PostMapping("/add")
    public Long addProfession(@Validated({Add.class}) @RequestBody ProfessionVo professionVo) {
        Profession profession = professionVo2Profession(professionVo);
        initEntityCreator(profession);
        initEntityModifier(profession);
        return professionService.addProfession(profession);
    }

    @ApiOperation("更新行业")
    @PostMapping("/update")
    public void updateProfession(@Validated({Update.class}) @RequestBody ProfessionVo professionVo) {
        Profession profession = professionVo2Profession(professionVo);
        initEntityModifier(profession);
        professionService.updateProfession(profession);
    }

    @ApiOperation("获取行业信息")
    @GetMapping("/get/{id}")
    public ProfessionVo getProfession(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        Profession profession = professionService.getProfession(id);
        return profession == null ? null : profession2Vo(profession);
    }

    @ApiOperation("获取行业列表")
    @PostMapping("/list")
    public List<ProfessionVo> listProfessions() {
        List<Profession> list = professionService.listAll();
        return list == null ? Collections.emptyList()
                : list.stream().map(this::profession2Vo).collect(Collectors.toList());
    }

    private Profession professionVo2Profession(ProfessionVo professionVo) {
        Profession result = new Profession();
        result.setId(professionVo.getId());
        result.setName(professionVo.getName());
        result.setDisabled(professionVo.getDisabled());
        return result;
    }

    private ProfessionVo profession2Vo(Profession profession) {
        ProfessionVo result = new ProfessionVo();
        result.setId(profession.getId());
        result.setName(profession.getName());
        return result;
    }
}

