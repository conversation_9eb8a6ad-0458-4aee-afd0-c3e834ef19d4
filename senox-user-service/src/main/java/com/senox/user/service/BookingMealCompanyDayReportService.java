package com.senox.user.service;

import com.senox.common.utils.PageUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.common.vo.PageResult;
import com.senox.user.domain.BookingMealCompanyDayReport;
import com.senox.user.mapper.BookingMealCompanyDayReportMapper;
import com.senox.user.vo.BookingMealDayReportSearchVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/4/16 9:02
 */
@Service
public class BookingMealCompanyDayReportService {

    @Autowired
    private BookingMealService bookingMealService;
    @Autowired
    private BookingMealCompanyDayReportMapper companyDayReportMapper;

    /**
     * 批量保存报餐公司日报
     * @param dayReportList
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchSaveCompanyDayReport(List<BookingMealCompanyDayReport> dayReportList) {
        if (CollectionUtils.isEmpty(dayReportList)) {
            return;
        }
        dayReportList.forEach(x -> {
            x.setMealYear(x.getMealDate().getYear());
            x.setMealMonth(x.getMealDate().getMonthValue());
        });

        // 日期列表
        List<LocalDate> dateList = dayReportList.stream()
                .map(BookingMealCompanyDayReport::getMealDate)
                .distinct()
                .collect(Collectors.toList());
        // 计算得到总记录
        for (LocalDate dateItem : dateList) {
            List<BookingMealCompanyDayReport> list = dayReportList.stream()
                    .filter(x -> Objects.equals(x.getMealDate(), dateItem))
                    .collect(Collectors.toList());
            dayReportList.add(newBookingMealCompanyDayReport(list));
        }
        dayReportList = dayReportList.stream()
                .sorted(Comparator.comparing(BookingMealCompanyDayReport::getMealDate))
                .collect(Collectors.toList());

        // 原日报
        List<BookingMealCompanyDayReport> srcReportList = companyDayReportMapper.listDayReportByMealDate(dateList);

        // 对比获得新增修改记录
        DataSepDto<BookingMealCompanyDayReport> sepResult = sepCompanyDayReport(srcReportList, dayReportList);
        if (!CollectionUtils.isEmpty(sepResult.getAddList())) {
            companyDayReportMapper.batchAddDayReport(sepResult.getAddList());
        }
        if (!CollectionUtils.isEmpty(sepResult.getUpdateList())) {
            companyDayReportMapper.batchUpdateDayReport(sepResult.getUpdateList());
        }
        if (!CollectionUtils.isEmpty(sepResult.getRemoveList())) {
            companyDayReportMapper.batchDeleteDayReport(sepResult.getRemoveList().stream().map(BookingMealCompanyDayReport::getId).collect(Collectors.toList()));
        }
    }

    /**
     * 报餐日报列表
     * @param searchVo
     * @return
     */
    public PageResult<BookingMealCompanyDayReport> listBookingMealDayReport(BookingMealDayReportSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }

        // 总记录数
        int totalSize = companyDayReportMapper.countDayReport(searchVo);
        searchVo.prepare();

        if (totalSize <= searchVo.getOffset()) {
            return PageResult.emptyPage();
        }

        // list
        List<BookingMealCompanyDayReport> resultList = companyDayReportMapper.listDayReport(searchVo);
        // 表是通过定时任务产生的，所以需要对今天及以后的数据处理
        fixCompanyDayReportRealTime(resultList, searchVo.getCompany());
        return PageUtils.resultPage(searchVo, totalSize, resultList);
    }

    /**
     * 日报详情
     * @param mealDate
     * @return
     */
    public List<BookingMealCompanyDayReport> listBookingMealDayReportDetail(LocalDate mealDate) {
        if (mealDate == null) {
            return Collections.emptyList();
        }

        if (mealDate.isBefore(LocalDate.now())) {
            return companyDayReportMapper.listDayReportDetail(mealDate);
        } else {
            BookingMealDayReportSearchVo searchVo = new BookingMealDayReportSearchVo();
            searchVo.setDateList(Collections.singletonList(mealDate));
            return bookingMealService.listCompanyDayReportRealTime(searchVo);
        }
    }

    /**
     * 日报总计
     * @param companyDayReportList
     * @return
     */
    private BookingMealCompanyDayReport newBookingMealCompanyDayReport(List<BookingMealCompanyDayReport> companyDayReportList) {
        if (CollectionUtils.isEmpty(companyDayReportList)) {
            return null;
        }
        BookingMealCompanyDayReport result = new BookingMealCompanyDayReport();
        result.setMealDate(companyDayReportList.get(0).getMealDate());
        result.setCompany(StringUtils.EMPTY);
        result.setBookedCount(companyDayReportList.stream().mapToInt(BookingMealCompanyDayReport::getBookedCount).sum());
        result.setUnbookedCount(companyDayReportList.stream().mapToInt(BookingMealCompanyDayReport::getUnbookedCount).sum());
        result.setMealYear(result.getMealDate().getYear());
        result.setMealMonth(result.getMealDate().getMonthValue());
        result.setTotal(Boolean.TRUE);
        return result;
    }

    /**
     * 对比公司日报
     * @param srcList
     * @param targetList
     * @return
     */
    private DataSepDto<BookingMealCompanyDayReport> sepCompanyDayReport(List<BookingMealCompanyDayReport> srcList,
                                                                        List<BookingMealCompanyDayReport> targetList) {
        DataSepDto<BookingMealCompanyDayReport> result = new DataSepDto<>();
        if (CollectionUtils.isEmpty(srcList)) {
            result.setAddList(targetList);

        } else if (CollectionUtils.isEmpty(targetList)) {
            result.setRemoveList(srcList);

        } else {
            List<BookingMealCompanyDayReport> addList = new ArrayList<>(targetList.size());
            List<BookingMealCompanyDayReport> updateList = new ArrayList<>(srcList.size());
            for (BookingMealCompanyDayReport item : targetList) {
                BookingMealCompanyDayReport srcItem = getCompanyDayReportInList(srcList, item);
                if (srcItem == null) {
                    addList.add(item);
                } else {
                    item.setId(srcItem.getId());
                    updateList.add(item);
                }
            }

            result.setAddList(addList);
            result.setUpdateList(updateList);
            result.setRemoveList(srcList.stream().filter(x -> getCompanyDayReportInList(targetList, x) == null).collect(Collectors.toList()));
        }
        return result;
    }

    /**
     * 公司日报是否存在
     * @param list
     * @param item
     * @return
     */
    private BookingMealCompanyDayReport getCompanyDayReportInList(List<BookingMealCompanyDayReport> list,
                                                                  BookingMealCompanyDayReport item) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.stream()
                .filter(x -> Objects.equals(x.getMealDate(), item.getMealDate()) && Objects.equals(x.getCompany(), item.getCompany()) && Objects.equals(x.getTotal(), item.getTotal()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 处理非实时结果
     * @param reportList
     * @param company
     */
    private void fixCompanyDayReportRealTime(List<BookingMealCompanyDayReport> reportList, String company) {
        if (CollectionUtils.isEmpty(reportList)) {
            return;
        }

        // 结果中日期为今天或以后的
        LocalDate now = LocalDate.now();
        List<LocalDate> dateList = reportList.stream().
                map(BookingMealCompanyDayReport::getMealDate).
                filter(x -> !x.isBefore(now))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dateList)) {
            return;
        }

        // 实时报表
        BookingMealDayReportSearchVo searchVo = new BookingMealDayReportSearchVo();
        searchVo.setDateList(dateList);
        searchVo.setCompany(company);
        List<BookingMealCompanyDayReport> realTimeList = bookingMealService.listCompanyDayReportRealTime(searchVo);
        if (CollectionUtils.isEmpty(realTimeList)) {
            return;
        }

        // 替换结果
        for (BookingMealCompanyDayReport item : reportList) {
            List<BookingMealCompanyDayReport> realList = realTimeList.stream()
                    .filter(x -> Objects.equals(x.getMealDate(), item.getMealDate()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(realList)) {
                continue;
            }

            item.setBookedCount(realList.stream().mapToInt(BookingMealCompanyDayReport::getBookedCount).sum());
            item.setUnbookedCount(realList.stream().mapToInt(BookingMealCompanyDayReport::getUnbookedCount).sum());
        }
    }
}
