package com.senox.user.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.constant.ActivityStatus;
import com.senox.user.domain.Activity;
import com.senox.user.domain.Prize;
import com.senox.user.mapper.PrizeMapper;
import com.senox.user.utils.ContextUtils;
import com.senox.user.vo.PrizeSearchVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/10 14:59
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PrizeService extends ServiceImpl<PrizeMapper, Prize> {

    private final ActivityService activityService;

    /**
     * 添加抽奖奖品
     * @param prize
     * @return
     */
    public Long addPrize(Prize prize) {
        //校验活动是否已经开启
        checkActivityValid(prize.getActivityId());
        ContextUtils.initEntityCreator(prize);
        prize.setCreateTime(LocalDateTime.now());
        ContextUtils.initEntityModifier(prize);
        prize.setModifiedTime(LocalDateTime.now());
        boolean save = save(prize);
        return save ? prize.getId() : 0L;
    }

    /**
     * 更新抽奖奖品
     * @param prize
     */
    public void updatePrize(Prize prize) {
        if (!WrapperClassUtils.biggerThanLong(prize.getId(), 0L)) {
            throw new InvalidParameterException();
        }
        //校验活动是否已经开启
        checkActivityValid(prize.getActivityId());
        ContextUtils.initEntityModifier(prize);
        prize.setModifiedTime(LocalDateTime.now());
        updateById(prize);
    }

    /**
     * 根据id获取抽奖奖品
     * @param id
     * @return
     */
    public Prize findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return getById(id);
    }

    /**
     * 根据id删除抽奖奖品
     * @param id
     */
    public void deleteById(Long id) {
        Prize prize = findById(id);
        if (prize != null) {
            checkActivityValid(prize.getActivityId());
            prize.setDisabled(Boolean.TRUE);
            prize.setName(prize.getName().concat(prize.getId().toString()));
            ContextUtils.initEntityModifier(prize);
            prize.setModifiedTime(LocalDateTime.now());
            updateById(prize);
        }
    }

    /**
     * 奖品列表
     * @param searchVo
     * @return
     */
    public List<Prize> listPrize(PrizeSearchVo searchVo) {
        return getBaseMapper().listPrize(searchVo);
    }

    /**
     * 奖品分页
     * @param searchVo
     * @return
     */
    public PageResult<Prize> pagePrize(PrizeSearchVo searchVo) {
        return PageUtils.commonPageResult(searchVo, () -> getBaseMapper().countPrize(searchVo), () -> getBaseMapper().listPrize(searchVo));
    }

    /**
     * 扣减奖品数量
     * @param id
     */
    public boolean reduceRemainingNum(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return getBaseMapper().reduceRemainingNum(id) > 0;
    }

    /**
     * 检验活动是否开启
     * @param activityId
     */
    private void checkActivityValid(Long activityId) {
        Activity activity = activityService.findById(activityId);
        if (activity == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "活动未找到！");
        }
        if (LocalDateTime.now().isAfter(activity.getStartTime())
                && LocalDateTime.now().isBefore(activity.getEndTime())) {
            throw new BusinessException("活动已开始！");
        }
    }
}
