<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.user.mapper.EnterpriseRealtyMapper">

    <!-- 根据物业编号重置别名 -->
    <update id="resetAliasByRealtySerial" parameterType="java.util.List">
        UPDATE u_enterprise_realty
        <set>
            realty_alias = ''
            , modified_time = NOW()
        </set>
        WHERE realty_serial IN <foreach collection="realtySerials" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </update>

    <!-- 根据企业id获取物业关系 -->
    <select id="listVoByEnterpriseIds" parameterType="java.lang.Long" resultType="com.senox.user.vo.EnterpriseRealtyVo">
        SELECT er.enterprise_id, er.realty_serial, r.name AS realty_name, er.realty_alias
        FROM u_enterprise_realty er
            INNER JOIN r_realty r ON r.serial_no = er.realty_serial
        WHERE er.enterprise_id IN <foreach collection="enterpriseIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </select>

    <!-- 合租但仅有1个经营户的物业列表 -->
    <select id="listUniqueEnterpriseRealtyWithAlias" resultType="java.lang.String">
        SELECT realty_serial
        FROM u_enterprise_realty
        WHERE LENGTH(realty_alias) > 0
        GROUP BY realty_serial
        HAVING COUNT(*) = 1
    </select>
</mapper>