<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.user.mapper.ReservationRecordMapper">

    <resultMap id="ReservationRecordVo_Result" type="com.senox.user.vo.ReservationRecordVo">
        <result property="id" column="id"/>
        <result property="visitorName" column="visitor_name"/>
        <result property="contact" column="contact"/>
        <result property="togetherNum" column="together_num"/>
        <result property="visitTimeStart" column="visit_time_start"/>
        <result property="visitTimeEnd" column="visit_time_end"/>
        <result property="type" column="type"/>
        <collection property="carNoList" column="id" select="selectReservationRecordItemById"/>
    </resultMap>

    <select id="findInfoById" parameterType="java.lang.Long" resultMap="ReservationRecordVo_Result">
        select r.id, r.visitor_name, r.contact, r.together_num, r.visit_time_start
             , r.visit_time_end, r.type
            from u_reservation_record r
        where r.id = #{id}
    </select>

    <select id="selectReservationRecordItemById" resultType="java.lang.String">
        select car_no from u_reservation_record_item
        where reservation_record_id = #{id}
    </select>

    <select id="countReservationRecord" parameterType="com.senox.user.vo.ReservationRecordSearchVo" resultType="int">
        select count(DISTINCT r.id)
        from u_reservation_record r
            left join u_reservation_record_item ri on ri.reservation_record_id = r.id
        <where>
            <if test="visitorName != null and visitorName != ''">
                and r.visitor_name = #{visitorName}
            </if>
            <if test="visitTimeStart != null">
                and r.visit_time_start >= #{visitTimeStart}
            </if>
            <if test="visitTimeEnd != null">
                and r.visit_time_end <![CDATA[<=]]> #{visitTimeEnd}
            </if>
            <if test="createOpenid != null and createOpenid != ''">
                and r.create_openid = #{createOpenid}
            </if>
            <if test="carNo != null and carNo != ''">
                and ri.car_no = #{carNo}
            </if>
            <if test="type != null">
                and r.type = #{type}
            </if>
            and r.is_disabled = 0
        </where>
    </select>

    <select id="listReservationRecord" parameterType="com.senox.user.vo.ReservationRecordSearchVo" resultMap="ReservationRecordVo_Result">
        select r.id, r.visitor_name, r.contact, r.together_num, r.visit_time_start
             , r.visit_time_end, r.type
        from u_reservation_record r
        <where>
            <if test="visitorName != null and visitorName != ''">
                and r.visitor_name = #{visitorName}
            </if>
            <if test="visitTimeStart != null">
                and r.visit_time_start >= #{visitTimeStart}
            </if>
            <if test="visitTimeEnd != null">
                and r.visit_time_end <![CDATA[<=]]> #{visitTimeEnd}
            </if>
            <if test="createOpenid != null and createOpenid != ''">
                and r.create_openid = #{createOpenid}
            </if>
            <if test="carNo != null and carNo != ''">
                AND EXISTS (
                    SELECT 1 FROM u_reservation_record_item ri
                    WHERE  r.id = ri.reservation_record_id
                    and ri.car_no = #{carNo}
                )
            </if>
            <if test="type != null">
                and r.type = #{type}
            </if>
            and r.is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY r.id DESC
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

    <select id="sumReservationRecord" parameterType="com.senox.user.vo.ReservationRecordSearchVo" resultType="com.senox.user.vo.ReservationRecordVo">
        select sum(r.together_num) as together_num
        from u_reservation_record r
            left join u_reservation_record_item ri on ri.reservation_record_id = r.id
        <where>
            <if test="visitorName != null and visitorName != ''">
                and r.visitor_name = #{visitorName}
            </if>
            <if test="visitTimeStart != null">
                and r.visit_time_start >= #{visitTimeStart}
            </if>
            <if test="visitTimeEnd != null">
                and r.visit_time_end <![CDATA[<=]]> #{visitTimeEnd}
            </if>
            <if test="createOpenid != null and createOpenid != ''">
                and r.create_openid = #{createOpenid}
            </if>
            <if test="carNo != null and carNo != ''">
                and ri.car_no = #{carNo}
            </if>
            <if test="type != null">
                and r.type = #{type}
            </if>
            and r.is_disabled = 0
        </where>
    </select>

</mapper>
