package com.senox.user.controller;

import com.senox.user.BaseControllerTest;
import com.senox.user.vo.ProfessionVo;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/12/31 17:13
 */
public class ProfessionControllerTest extends BaseControllerTest {

    @Test
    public void testAddAndUpdate() {
        ProfessionVo professionVo1 = mockProfessionVo();
        HttpEntity<ProfessionVo> entity1 = new HttpEntity<>(professionVo1);
        ResponseEntity<Long> responseEntity1 = restTemplate.exchange("/profession/add", HttpMethod.POST,
                entity1, new ParameterizedTypeReference<Long>() {
                });
        Assertions.assertEquals(HttpStatus.OK, responseEntity1.getStatusCode());
        Long result1 = responseEntity1.getBody();
        Assertions.assertTrue(result1 > 0);

        ResponseEntity<ProfessionVo> responseEntity2 = restTemplate.exchange("/profession/get/" + result1,
                HttpMethod.GET, null, new ParameterizedTypeReference<ProfessionVo>() {
                });
        ProfessionVo result2 = responseEntity2.getBody();
        Assertions.assertEquals(professionVo1.getName(), result2.getName());

        ProfessionVo professionVo3 = new ProfessionVo();
        professionVo3.setId(result1);
        professionVo3.setName(randStr(20));
        HttpEntity<ProfessionVo> entity3 = new HttpEntity<>(professionVo3);
        ResponseEntity<Long> responseEntity3 = restTemplate.exchange("/profession/update", HttpMethod.POST,
                entity3, new ParameterizedTypeReference<Long>() {
                });
        Assertions.assertEquals(HttpStatus.OK, responseEntity3.getStatusCode());

        ResponseEntity<ProfessionVo> responseEntity4 = restTemplate.exchange("/profession/get/" + result1,
                HttpMethod.GET, null, new ParameterizedTypeReference<ProfessionVo>() {
                });
        ProfessionVo result4 = responseEntity4.getBody();
        Assertions.assertEquals(professionVo3.getName(), result4.getName());
    }

    @Test
    public void testList() {
        for (int i = 0; i < 20; i++) {
            ProfessionVo professionVo = mockProfessionVo();
            HttpEntity<ProfessionVo> entity = new HttpEntity<>(professionVo);
            restTemplate.exchange("/profession/add", HttpMethod.POST, entity, new ParameterizedTypeReference<Long>() {
            });
        }

        ResponseEntity<List<ProfessionVo>> responseEntity = restTemplate.exchange("/profession/list", HttpMethod.POST,
                null, new ParameterizedTypeReference<List<ProfessionVo>>() {
                });
        Assertions.assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
        List<ProfessionVo> result = responseEntity.getBody();
        Assertions.assertTrue(result.size() >= 20);
    }

    private ProfessionVo mockProfessionVo() {
        ProfessionVo result = new ProfessionVo();
        result.setName(randStr(30));
        return result;
    }
}
