package com.senox.user.controller;

import com.senox.common.vo.PageResult;
import com.senox.user.service.IcCardService;
import com.senox.user.vo.IcCardSearchVo;
import com.senox.user.vo.IcCardVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2021/3/16 10:04
 */
@Api(tags = "IC卡管理")
@RestController
@RequestMapping("/iccard")
public class IcCardController {

    @Autowired
    private IcCardService icCardService;

    @ApiOperation("IC卡列表")
    @PostMapping("/list")
    public PageResult<IcCardVo> listIcCardPage(@RequestBody IcCardSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return icCardService.listIcCardPage(searchVo);
    }

}
