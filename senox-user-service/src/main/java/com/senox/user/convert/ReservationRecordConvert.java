package com.senox.user.convert;

import com.senox.user.domain.ReservationRecord;
import com.senox.user.vo.ReservationRecordVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023/12/27 18:09
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface ReservationRecordConvert {

    /**
     * 对象 转视图
     * @param domain
     * @return
     */
    ReservationRecordVo toVo(ReservationRecord domain);

    /**
     * 视图 转对象
     * @param vo
     * @return
     */
    ReservationRecord toDo(ReservationRecordVo vo);
}
