package com.senox.user.convert;

import com.senox.user.domain.VoteResources;
import com.senox.user.vo.VoteResourcesVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/16 11:09
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface VoteResourcesConvertor {

    VoteResources toDo(VoteResourcesVo vo);

    List<VoteResources> toDo(List<VoteResourcesVo> voList);

    VoteResourcesVo toVo(VoteResources domain);

    List<VoteResourcesVo> toVo(List<VoteResources> domainList);
}
