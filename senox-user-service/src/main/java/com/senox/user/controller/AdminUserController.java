package com.senox.user.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminUserDto;
import com.senox.user.constant.UserConst;
import com.senox.user.domain.AdminUser;
import com.senox.user.domain.Department;
import com.senox.user.service.AdminUserService;
import com.senox.user.service.DepartmentService;
import com.senox.user.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2020/12/30 8:15
 */
@Api(tags = "管理用户")
@RestController
@RequestMapping("/adminUser")
public class AdminUserController extends BaseController {

    @Autowired
    private AdminUserService adminUserService;
    @Autowired
    private DepartmentService departmentService;

    @ApiOperation("添加管理用户")
    @PostMapping("/add")
    public Long addAdminUser(@Validated(Add.class) @RequestBody AdminUserVo adminUser) {
        AdminUser user = adminUserVo2AdminUser(adminUser);
        initEntityCreator(user);
        initEntityModifier(user);

        // 部门列表
        List<Long> departments = adminUser.getDepartments();
        if (WrapperClassUtils.biggerThanLong(adminUser.getDepartmentId(), 0L)) {
            if (departments == null) {
                departments = Collections.singletonList(adminUser.getDepartmentId());
            } else if (!departments.contains(adminUser.getDepartmentId())) {
                departments.add(adminUser.getDepartmentId());
            }
        }
        return adminUserService.addAdminUser(user, adminUser.getRoles(), departments);
    }

    @ApiOperation("更新管理用户信息")
    @PostMapping("/update")
    public void updateAdminUser(@Validated(Update.class) @RequestBody AdminUserVo adminUser) {
        if (adminUser.getId() < 1) {
            throw new InvalidParameterException();
        }
        AdminUser user = adminUserVo2AdminUser(adminUser);
        initEntityModifier(user);

        // 部门列表
        List<Long> departments = adminUser.getDepartments();
        if (WrapperClassUtils.biggerThanLong(adminUser.getDepartmentId(), 0L)) {
            if (departments == null) {
                departments = Collections.singletonList(adminUser.getDepartmentId());
            } else if (!departments.contains(adminUser.getDepartmentId())) {
                departments.add(adminUser.getDepartmentId());
            }
        }
        adminUserService.updateAdminUser(user, adminUser.getRoles(), departments);
    }


    @ApiOperation("修改收据编号")
    @PostMapping("/tollInfo/modify")
    public void modifyAdminTollInfo(@Validated @RequestBody TollManSerialVo tollManSerial) {
        adminUserService.modifyAdminTollInfo(tollManSerial, getUserInContext());
    }


    @ApiOperation("修改密码")
    @PostMapping("/password/modify")
    public void modifyPassword(@Validated @RequestBody AdminUserChangePwdVo pwdVo) {
        pwdVo.setModifierId(getUserInContext().getUserId());
        pwdVo.setModifierName(getUserInContext().getUsername());
        adminUserService.changePassword(pwdVo);
    }

    @ApiOperation("校验密码")
    @PostMapping("/password/check")
    public AdminUserVo checkUsernameAndPassword(@Validated @RequestBody AdminUserLoginVo loginVo) {
        AdminUser adminUser = adminUserService.findByUsernameAndPassword(loginVo.getUsername(), loginVo.getPassword());
        if (adminUser == null) {
            return null;
        }
        AdminUserVo result = adminUser2Vo(adminUser);
        result.setToken(adminUserService.getUserToken(adminUser.getId(), adminUser.getUsername()));
        //角色编码列表
        List<String> codeList = adminUserService.listAdminUserRoleCode(result.getId());
        // 更新缓存
        AdminUserDto cachedUser = new AdminUserDto();
        cachedUser.setUserId(result.getId());
        cachedUser.setUsername(result.getUsername());
        cachedUser.setRealName(result.getRealName());
        cachedUser.setTollMan(BooleanUtils.isTrue(result.getTollMan()));
        cachedUser.setCosList(adminUserService.listAdminUserCos(result.getId()));
        cachedUser.setRoleCodes(CollectionUtils.isEmpty(codeList)
                ? Collections.emptyList()
                : codeList.stream().filter(x -> !StringUtils.isBlank(x)).collect(Collectors.toList()));
        cachedUser.setToken(result.getToken());
        if (WrapperClassUtils.biggerThanLong(adminUser.getDepartmentId(), 0L)) {
            Department department = departmentService.findById(adminUser.getDepartmentId());
            if (department != null) {
                cachedUser.setDepartmentId(adminUser.getDepartmentId());
                cachedUser.setDepartmentName(StringUtils.isBlank(department.getFullName()) ? department.getName() : department.getFullName());
            }
        }
        result.setRoleCodes(cachedUser.getRoleCodes());
        RedisUtils.set(String.format(UserConst.Cache.KEY_USER, result.getToken()), cachedUser, UserConst.Cache.TTL_2H);
        return result;
    }

    @ApiOperation("获取票据号")
    @GetMapping("/tollInfo/get")
    public TollManSerialVo findAdminTollInfo() {
        return adminUserService.findAdminTollInfo(getUserInContext().getUserId());
    }

    @ApiOperation("获取管理用户信息")
    @GetMapping("/get/{id}")
    public AdminUserVo getAdminUser(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException();
        }
        AdminUserVo result = adminUserService.findById(id);
        if (result != null) {
            result.setRoles(adminUserService.listAdminUserRole(id));
            List<String> codeList = adminUserService.listAdminUserRoleCode(id);
            result.setRoleCodes(CollectionUtils.isEmpty(codeList)
                    ? Collections.emptyList()
                    : codeList.stream().filter(x -> !StringUtils.isBlank(x)).collect(Collectors.toList()));
            result.setDepartments(adminUserService.listAdminUserDepartment(id));
        }
        return result;
    }

    @ApiOperation("根据用户姓名获取用户信息")
    @GetMapping("/getByRealName")
    public AdminUserVo getAdminUserByRealName(@RequestParam String realName) {
        if (StringUtils.isBlank(realName)) {
            throw new InvalidParameterException();
        }
        AdminUser result = adminUserService.findByRealName(realName);
        return result == null ? null : adminUser2Vo(result);
    }

    @ApiOperation("根据联系方式获取用户信息")
    @GetMapping("/getByTelephone")
    public AdminUserVo getAdminUserByTelephone(@RequestParam String telephone) {
        if (StringUtils.isBlank(telephone)) {
            throw new InvalidParameterException("无效的手机号码");
        }
        AdminUser result = adminUserService.findByTelephone(telephone);
        return result == null ? null : adminUser2Vo(result);
    }

    @ApiOperation("校验签名")
    @PostMapping("/token/check")
    public AdminUserDto checkToken(@RequestParam String token) {
        if (StringUtils.isEmpty(token)) {
            throw new InvalidParameterException();
        }
        return adminUserService.getUserFromToken(token);
    }

    @ApiOperation("失效签名")
    @PostMapping("/token/invalid")
    public void invalidToken(@RequestParam String token) {
        adminUserService.removeToken(token);
    }

    @ApiOperation("校验feign签名")
    @PostMapping("/feignToken/check")
    public AdminUserDto checkFeignToken(@RequestParam String feignToken) {
        if (StringUtils.isEmpty(feignToken)) {
            throw new InvalidParameterException();
        }
        return adminUserService.getUserFromFeignToken(feignToken);
    }

    @ApiOperation("失效feign签名")
    @PostMapping("/feignToken/invalid")
    public void invalidFeignToken(@RequestParam String feignToken) {
        adminUserService.removeFeignToken(feignToken);
    }

    @ApiOperation("校验微信签名")
    @PostMapping("/wechatToken/check")
    public AdminUserDto checkWechatToken(@RequestParam(required = false) String appId, @RequestParam String wechatToken) {
        if (StringUtils.isEmpty(wechatToken)) {
            throw new InvalidParameterException();
        }
        return adminUserService.getUserFromWechatToken(appId, wechatToken);
    }

    @ApiOperation("管理员列表")
    @PostMapping("/list")
    public PageResult<AdminUserVo> listAdminUserPage(@Validated @RequestBody AdminUserSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return adminUserService.listAdminUserPage(searchVo);
    }

    @ApiOperation("用户部门")
    @PostMapping("/department/list/{userId}")
    public List<Long> listDepartment(@PathVariable Long userId) {
        if (userId < 1L) {
            throw new InvalidParameterException();
        }
        return adminUserService.listAdminUserDepartment(userId);
    }

    @ApiOperation("用户角色")
    @PostMapping("/role/list/{userId}")
    public List<Long> listRole(@PathVariable Long userId) {
        if (userId < 1L) {
            throw new InvalidParameterException();
        }
        return adminUserService.listAdminUserRole(userId);
    }

    @ApiOperation("用户权限")
    @PostMapping("/cos/list/{userId}")
    public List<String> listCos(@PathVariable Long userId) {
        if (userId < 1L) {
            throw new InvalidParameterException();
        }
        return adminUserService.listAdminUserCos(userId);
    }

    @ApiOperation("权限用户")
    @GetMapping("/user/list/{roleId}")
    public List<AdminUserVo> listUser(@PathVariable Long roleId) {
        return adminUserService.listRoleAdminUser(roleId);
    }

    private AdminUser adminUserVo2AdminUser(AdminUserVo userVo) {
        AdminUser result = new AdminUser();
        BeanUtils.copyProperties(userVo, result);
        return result;
    }

    private AdminUserVo adminUser2Vo(AdminUser user) {
        AdminUserVo result = new AdminUserVo();
        BeanUtils.copyProperties(user, result);
        return result;
    }

}
