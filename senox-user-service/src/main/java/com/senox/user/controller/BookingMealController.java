package com.senox.user.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.StringUtils;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.user.domain.BookingMeal;
import com.senox.user.domain.BookingMealCompanyDayReport;
import com.senox.user.service.BookingMealCompanyDayReportService;
import com.senox.user.service.BookingMealService;
import com.senox.user.vo.BookingMealCompanyDayReportVo;
import com.senox.user.vo.BookingMealDayReportSearchVo;
import com.senox.user.vo.BookingMealSearchVo;
import com.senox.user.vo.BookingMealVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/4/6 10:25
 */
@Api(tags = "订餐")
@RestController
@RequestMapping("/bookingMeal")
public class BookingMealController {

    private static final Logger logger = LoggerFactory.getLogger(BookingMealController.class);

    @Autowired
    private BookingMealService bookingMealService;
    @Autowired
    private BookingMealCompanyDayReportService reportService;

    @ApiOperation("订餐结果列表查询")
    @PostMapping("/list")
    public PageResult<BookingMealVo> listBookingPage(@RequestBody BookingMealSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        PageResult<BookingMeal> bookingPage = bookingMealService.listBookingPage(searchVo);
        return PageResult.convertPage(bookingPage, this::bookingMeal2Vo);
    }

    @ApiOperation("日报列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header")
    })
    @PostMapping("/dayReport")
    public PageResult<BookingMealCompanyDayReportVo> listBookingDayReport(@RequestBody BookingMealDayReportSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        PageResult<BookingMealCompanyDayReport> reportPage = reportService.listBookingMealDayReport(searchVo);
        return PageResult.convertPage(reportPage, this::bookingMealCompanyDayReport2Vo);
    }

    @ApiOperation("日报详情")
    @GetMapping("/dayReportDetail")
    public List<BookingMealCompanyDayReportVo> listBookingDayReportDetail(@RequestParam String mealDate) {
        if (StringUtils.isBlank(mealDate)) {
            throw new InvalidParameterException("无效的参数");
        }
        LocalDate date = null;
        try {
            date = LocalDate.parse(mealDate);
        } catch (Exception e) {
            logger.warn("Invalid mealDate {}", mealDate);
        }
        if (date == null) {
            throw new InvalidParameterException("无效的参数");
        }
        List<BookingMealCompanyDayReport> resultList = reportService.listBookingMealDayReportDetail(date);
        return resultList.stream().map(this::bookingMealCompanyDayReport2Vo).collect(Collectors.toList());
    }

    /**
     * 订餐实体转视图对象
     * @param booking
     * @return
     */
    private BookingMealVo bookingMeal2Vo(BookingMeal booking) {
        BookingMealVo result = new BookingMealVo();
        BeanUtils.copyProperties(booking, result);
        return result;
    }

    /**
     * 报餐日报转视图对象
     * @param report
     * @return
     */
    private BookingMealCompanyDayReportVo bookingMealCompanyDayReport2Vo(BookingMealCompanyDayReport report) {
        BookingMealCompanyDayReportVo result = new BookingMealCompanyDayReportVo();
        BeanUtils.copyProperties(report, result);
        return result;
    }
}
