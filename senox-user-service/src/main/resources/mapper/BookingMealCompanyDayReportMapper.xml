<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.user.mapper.BookingMealCompanyDayReportMapper">

    <resultMap id="Result_BookingMealCompanyDayReport" type="com.senox.user.domain.BookingMealCompanyDayReport">
        <id property="id" jdbcType="BIGINT" column="id" />
        <result property="mealDate" jdbcType="TIMESTAMP" column="meal_date" />
        <result property="company" jdbcType="VARCHAR" column="company" />
        <result property="bookedCount" jdbcType="INTEGER" column="booked_count" />
        <result property="unbookedCount" jdbcType="INTEGER" column="unbooked_count" />
        <result property="mealYear" jdbcType="INTEGER" column="meal_year" />
        <result property="mealMonth" jdbcType="INTEGER" column="meal_month" />
        <result property="total" jdbcType="TINYINT" column="is_total" />
        <result property="createTime" jdbcType="TIMESTAMP" column="create_time" />
        <result property="modifiedTime" jdbcType="TIMESTAMP" column="modified_time" />
    </resultMap>

    <!-- 批量添加报餐公司日报 -->
    <insert id="batchAddDayReport" parameterType="java.util.List">
        INSERT INTO wx_booking_meal_company_day_report(
            meal_date, company, booked_count, unbooked_count, meal_year, meal_month, is_total, create_time, modified_time
        ) VALUES
        <foreach collection="dayReportList" item="item" separator=",">
        (
            #{item.mealDate}, #{item.company}, #{item.bookedCount}, #{item.unbookedCount}, #{item.mealYear}, #{item.mealMonth}, #{item.total}, NOW(), NOW()
        )
        </foreach>
    </insert>

    <!-- 批量更新报餐公司日报 -->
    <update id="batchUpdateDayReport" parameterType="java.util.List">
        UPDATE wx_booking_meal_company_day_report
        <trim prefix="SET" suffixOverrides=",">
            <trim prefix="meal_date = CASE" suffix="END,">
                <foreach collection="dayReportList" item="item">
                    <if test="item.mealDate != null">
                        WHEN id = #{item.id} THEN #{item.mealDate}
                    </if>
                </foreach>
            </trim>
            <trim prefix="company = CASE" suffix="END,">
                <foreach collection="dayReportList" item="item">
                    <if test="item.company != null">
                        WHEN id = #{item.id} THEN #{item.company}
                    </if>
                </foreach>
            </trim>
            <trim prefix="booked_count = CASE" suffix="END,">
                <foreach collection="dayReportList" item="item">
                    <if test="item.bookedCount != null">
                        WHEN id = #{item.id} THEN #{item.bookedCount}
                    </if>
                </foreach>
            </trim>
            <trim prefix="unbooked_count = CASE" suffix="END,">
                <foreach collection="dayReportList" item="item">
                    <if test="item.unbookedCount != null">
                        WHEN id = #{item.id} THEN #{item.unbookedCount}
                    </if>
                </foreach>
            </trim>
            <trim prefix="meal_year = CASE" suffix="END,">
                <foreach collection="dayReportList" item="item">
                    <if test="item.mealYear != null">
                        WHEN id = #{item.id} THEN #{item.mealYear}
                    </if>
                </foreach>
            </trim>
            <trim prefix="meal_month = CASE" suffix="END,">
                <foreach collection="dayReportList" item="item">
                    <if test="item.mealMonth != null">
                        WHEN id = #{item.id} THEN #{item.mealMonth}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_total = CASE" suffix="END,">
                <foreach collection="dayReportList" item="item">
                    <if test="item.total != null">
                        WHEN id = #{item.id} THEN #{item.total}
                    </if>
                </foreach>
            </trim>
            modified_time = NOW()
        </trim>
        WHERE id IN <foreach collection="dayReportList" item="item" open="(" close=")" separator=",">#{item.id}</foreach>
    </update>

    <!-- 批量删除日报 -->
    <update id="batchDeleteDayReport" parameterType="java.util.List">
        DELETE FROM wx_booking_meal_company_day_report
        WHERE id IN <foreach collection="idList" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </update>

    <!-- 根据订餐日期加载日报 -->
    <select id="listDayReportByMealDate" parameterType="java.util.List" resultMap="Result_BookingMealCompanyDayReport">
        SELECT id, meal_date, company, booked_count, unbooked_count, meal_year, meal_month, is_total, create_time, modified_time
        FROM wx_booking_meal_company_day_report
        WHERE meal_date IN <foreach collection="dateList" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </select>

    <!-- 报餐日报统计 -->
    <select id="countDayReport" parameterType="com.senox.user.vo.BookingMealDayReportSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM wx_booking_meal_company_day_report
        <where>
            <if test="startDate != null">
                AND meal_date >= #{startDate}
            </if>
            <if test="endDate != null">
                AND meal_date <![CDATA[<=]]> #{endDate}
            </if>
            <choose>
                <when test="company != null and company != ''">
                    AND company = #{company} AND is_total = 0
                </when>
                <otherwise>
                    AND is_total = 1
                </otherwise>
            </choose>
        </where>
    </select>

    <!-- 报餐日报列表 -->
    <select id="listDayReport" parameterType="com.senox.user.vo.BookingMealDayReportSearchVo"
            resultMap="Result_BookingMealCompanyDayReport">
        SELECT id, meal_date, company, booked_count, unbooked_count, meal_year, meal_month, is_total, modified_time
        FROM wx_booking_meal_company_day_report
        <where>
            <if test="startDate != null">
                AND meal_date >= #{startDate}
            </if>
            <if test="endDate != null">
                AND meal_date <![CDATA[<=]]> #{endDate}
            </if>
            <choose>
                <when test="company != null and company != ''">
                    AND company = #{company} AND is_total = 0
                </when>
                <otherwise>
                    AND is_total = 1
                </otherwise>
            </choose>
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}, id
            </when>
            <otherwise>
                ORDER BY meal_date DESC, id
            </otherwise>
        </choose>
        LIMIT ${offset}, ${pageSize}
    </select>

    <!-- 查看日报详情 -->
    <select id="listDayReportDetail" parameterType="java.time.LocalDate" resultMap="Result_BookingMealCompanyDayReport">
        SELECT id, meal_date, company, booked_count, unbooked_count, meal_year, meal_month, is_total, modified_time
        FROM wx_booking_meal_company_day_report
        WHERE meal_date = #{mealDate} AND is_total = 0
    </select>
</mapper>