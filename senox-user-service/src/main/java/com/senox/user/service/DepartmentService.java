package com.senox.user.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.user.constant.UserConst;
import com.senox.user.domain.Department;
import com.senox.user.mapper.DepartmentMapper;
import com.senox.user.vo.DepartmentNode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/9/15 11:02
 */
@Service
public class DepartmentService {

    @Autowired
    private DepartmentMapper departmentMapper;

    /**
     * 添加部门
     * @param department
     * @return
     */
    public Long addDepartment(Department department) {
        if (StringUtils.isBlank(department.getName())) {
            return 0L;
        }
        if (department.getParentId() == null || department.getParentId() < 0L) {
            department.setParentId(0L);
        }
        if (department.getOrderNo() == null) {
            department.setOrderNo(UserConst.ORDER_DF_50);
        }

        // 校验是否重名
        if (isDepartmentNameDuplicated(null, department.getParentId(), department.getName())) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR, "部门已存在");
        }

        // 新增部门
        int result = departmentMapper.addDepartment(department);
        // clear cache
        if (result > 0) {
            RedisUtils.del(String.format(UserConst.Cache.KEY_DEPARTMENT, department.getParentId()));
            RedisUtils.del(UserConst.Cache.KEY_DEPARTMENT_ALL);
        }
        return result > 0 ? department.getId() : 0L;
    }

    /**
     * 更新部门
     * @param department
     * @return
     */
    public boolean updateDepartment(Department department) {
        if (!WrapperClassUtils.biggerThanLong(department.getId(), 0L)) {
            return false;
        }

        // 删除部门
        if (BooleanUtils.isTrue(department.getDisabled())) {
            Department dbDept = findById(department.getId());
            if (dbDept != null) {
                department.setName(dbDept.getName() + "_" + department.getId());
                department.setParentId(dbDept.getParentId());
            }
        }

        // 校验是否重名
        if (isDepartmentNameDuplicated(department.getId(), department.getParentId(), department.getName())) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR, "部门已存在");
        }

        boolean result = departmentMapper.updateDepartment(department) > 0;

        // clear cache
        if (result) {
            RedisUtils.del(String.format(UserConst.Cache.KEY_DEPARTMENT, department.getParentId()));
            RedisUtils.del(UserConst.Cache.KEY_DEPARTMENT_ALL);
        }
        return result;
    }

    /**
     * 根据id查找部门
     * @param id
     * @return
     */
    public Department findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return departmentMapper.findById(id);
    }

    /**
     * 部门列表
     * @param parentId
     * @return
     */
    public List<Department> listDepartments(Long parentId) {
        parentId = parentId == null ? 0L : parentId;
        List<Department> resultList = null;

        // load from cache
        String cacheKey = String.format(UserConst.Cache.KEY_DEPARTMENT, parentId);
        String cacheValue = RedisUtils.get(cacheKey);
        if (!StringUtils.isBlank(cacheValue)) {
            resultList = JsonUtils.json2GenericObject(cacheValue, new TypeReference<List<Department>>() {});
        }

        // load from db
        if (CollectionUtils.isEmpty(resultList)) {
            resultList = departmentMapper.listByParentId(parentId);

            // set cache
            if (!CollectionUtils.isEmpty(resultList)) {
                RedisUtils.set(cacheKey, JsonUtils.object2Json(resultList), UserConst.Cache.TTL_7D);
            }
        }
        return resultList;
    }

    /**
     * 部门列表
     * @return
     */
    public List<Department> listDepartments() {
        List<Department> resultList = null;

        // load from cache
        String cacheValue = RedisUtils.get(UserConst.Cache.KEY_DEPARTMENT_ALL);
        if (!StringUtils.isBlank(cacheValue)) {
            resultList = JsonUtils.json2GenericObject(cacheValue, new TypeReference<List<Department>>() {});
        }

        // load from db
        if (CollectionUtils.isEmpty(resultList)) {
            resultList = departmentMapper.listAll();

            // set cache
            if (!CollectionUtils.isEmpty(resultList)) {
                RedisUtils.set(UserConst.Cache.KEY_DEPARTMENT_ALL, JsonUtils.object2Json(resultList), UserConst.Cache.TTL_7D);
            }
        }
        return resultList;
    }

    /**
     * 部门节点
     * @return
     */
    public List<DepartmentNode> listDepartmentNode() {
        List<Department> departments = listDepartments();
        return findChildDepartment(0L, departments);
    }

    /**
     * 查找子部门
     * @param parentId
     * @param departments
     * @return
     */
    private List<DepartmentNode> findChildDepartment(Long parentId, List<Department> departments) {
        List<Department> deptList = departments.stream().filter(x -> Objects.equals(parentId, x.getParentId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(deptList)) {
            return Collections.emptyList();
        }
        List<DepartmentNode> resultList = deptList.stream().map(this::department2Node).collect(Collectors.toList());
        for (DepartmentNode item : resultList) {
            item.setChildNodes(findChildDepartment(item.getId(), departments));
        }
        return resultList;
    }

    /**
     * 部门是否存在
     * @param id
     * @param parentId
     * @param name
     * @return
     */
    private boolean isDepartmentNameDuplicated(Long id, Long parentId, String name) {
        if (StringUtils.isBlank(name)) {
            return false;
        }
        parentId = parentId == null ? 0L : parentId;

        Department department = departmentMapper.findByName(parentId, name);
        if (department == null) {
            return false;
        }
        return !WrapperClassUtils.biggerThanLong(id, 0L) || !Objects.equals(id, department.getId());
    }

    /**
     * 部门实体转节点
     * @param department
     * @return
     */
    private DepartmentNode department2Node(Department department) {
        DepartmentNode result = new DepartmentNode();
        result.setId(department.getId());
        result.setName(department.getName());
        return result;
    }

}
