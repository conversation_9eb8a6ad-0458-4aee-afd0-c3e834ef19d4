<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.user.mapper.ResidentAccessMapper">


    <resultMap id="Result_ResidentAccess" type="com.senox.user.vo.ResidentAccessResultVo">
        <result property="id" column="id"/>
        <result property="residentNo" column="resident_no"/>
        <result property="residentType" column="resident_type"/>
        <result property="name" column="name"/>
        <result property="idNum" column="id_num"/>
        <result property="bornDate" column="born_date" />
        <result property="gender" column="gender" />
        <result property="nature" column="nature" />
        <result property="telephone" column="telephone"/>
        <result property="address" column="address"/>
        <result property="faceUrl" column="face_url"/>
        <result property="remark" column="remark"/>
        <collection property="residentAccessVoList" ofType="com.senox.user.vo.ResidentAccessVo">
            <result property="id" column="access_id"/>
            <result property="deviceId" column="device_id"/>
            <result property="realtySerial" column="realty_serial"/>
            <result property="contractNo" column="contract_no"/>
            <result property="access" column="access"/>
            <result property="state" column="state"/>
            <result property="disabled" column="disabled"/>
            <result property="realtyName" column="realty_name"/>
            <result property="deviceName" column="device_name"/>
            <result property="streetId" column="street_id"/>
            <result property="streetName" column="street_name"/>
            <result property="regionId" column="region_id"/>
            <result property="regionName" column="region_name"/>
        </collection>
    </resultMap>

    <select id="residentAccessResultByNo" resultMap="Result_ResidentAccess">
        SELECT  ur.id
            , ur.resident_no
            , ur.resident_type
            , ur.name
            , ur.id_num
            , ur.born_date
            , ur.gender
            , ur.nature
            , ur.telephone
            , ur.address
            , ur.face_url
            , ur.remark
        <if test="isDetail">
            , ura.id as access_id
            , ura.device_id
            , ura.realty_serial
            , ura.contract_no
            , ura.access
            , ura.state
            , ura.disabled
            , rr.name as realty_name
            , dac.device_name
            , dac.street_id
            , dac.street_name
            , dac.region_id
            , dac.region_name
        </if>
        FROM u_resident ur
            LEFT JOIN u_resident_access ura ON ura.resident_no = ur.resident_no
        <if test="isDetail">
            LEFT JOIN r_realty rr ON rr.serial_no = ura.realty_serial
            LEFT JOIN d_access_control dac ON dac.id = ura.device_id
        </if>
        WHERE ur.resident_no = #{residentNo}
            AND ur.disabled = 0
    </select>


    <select id="listResidentAccessByDeviceId" parameterType="java.lang.Long" resultType="com.senox.user.vo.ResidentAccessVo">
        SELECT ra.id
            , r.resident_no
            , ra.device_id
            , ra.realty_serial
            , ra.contract_no
            , ra.access
            , ra.state
            , ac.device_ip
            , r.name as resident_name
            , r.face_url
        FROM u_resident_access ra
            INNER JOIN u_resident r ON ra.resident_no = r.resident_no
            INNER JOIN d_access_control ac ON ac.id = ra.device_id
            WHERE ra.device_id = #{deviceId} AND ra.disabled = 0
    </select>

</mapper>
