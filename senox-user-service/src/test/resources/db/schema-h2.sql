/* 地区 */
DROP TABLE IF EXISTS dict_area;
CREATE TABLE dict_area(
    id             bigint unsigned   not null    auto_increment,
    serial_no      varchar(20)       not null    default '' comment '编号',
	name           varchar(30)       not null    default '' comment '地区名',
	brief_name     varchar(20)       not null    default '' comment '地区简称',
	category       tinyint unsigned  not null    default 0  comment '区域类别，1省；2直辖市；3市',
	parent_id      bigint unsigned   not null    default 0  comment '父id',
	is_disabled    tinyint(1)        not null    default 0  comment '禁用',
	creator_id     bigint unsigned   not null    default 0  comment '创建人id',
	creator_name   varchar(30)       not null    default '' comment '创建人姓名',
	create_time    datetime          not null    comment '创建时间',
    modifier_id    bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name  varchar(30)       not null    default '' comment '修改人姓名',
	modified_time  datetime          not null    comment '修改时间',
	primary key (id),
	unique key uk_area_serialNo(serial_no)
);
--  ALTER TABLE dict_area ADD INDEX idx_area_parentId (parent_id);

/* 行业 */
DROP TABLE IF EXISTS dict_profession;
CREATE TABLE dict_profession(
    id             bigint unsigned   not null    auto_increment,
    name           varchar(30)       not null    default '' comment '行业名',
    is_disabled    tinyint(1)        not null    default 0  comment '禁用',
    creator_id     bigint unsigned   not null    default 0  comment '创建人id',
    creator_name   varchar(30)       not null    default '' comment '创建人姓名',
    create_time    datetime          not null    comment '创建时间',
    modifier_id    bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name  varchar(30)       not null    default '' comment '修改人姓名',
    modified_time  datetime          not null    comment '修改时间',
    primary key (id),
    unique key uk_profession_name(name)
);


/* 员工用户 */
DROP TABLE IF EXISTS u_admin_user;
CREATE TABLE u_admin_user(
    id              bigint unsigned   not null    auto_increment,
    username        varchar(30)       not null    default '' comment '登录账号',
    real_name       varchar(30)       not null    default '' comment '姓名',
    password        varchar(60)       not null    default '' comment '密码',
    salt            varchar(20)       not null    default '' comment '盐',
    gender          tinyint unsigned  not null    default 1  comment '性别：1男；2女；3其他',
    email           varchar(30)       comment '邮箱',
    telephone       varchar(20)       comment '手机',
    avatar          varchar(100)      comment '头像',
    department_id   bigint unsigned   not null    default 0  comment '部门id',
    is_toll_man     tinyint(1)        not null    default 0  comment '是否收费员',
    maintain_man_type tinyint(1) NOT NULL DEFAULT '0' COMMENT '0：员工，1：维修员，2：维修主管',
    login_path      varchar(100)      comment '登录跳转路径',
    is_loginable    tinyint(1)        not null    default 1  comment '是否可以登录',
    bill_serial     int unsigned      comment '收据编号',
    pay_device_sn   varchar(30)       comment '支付终端序列号',
    is_disabled     tinyint(1)        not null    default 0  comment '禁用',
    creator_id      bigint unsigned   not null    default 0  comment '创建人id',
	creator_name    varchar(30)       not null    default '' comment '创建人姓名',
	create_time     datetime          not null    comment '创建时间',
    modifier_id     bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name   varchar(30)       not null    default '' comment '修改人姓名',
	modified_time   datetime          not null    comment '修改时间',
    primary key (id),
    unique key uk_adminUser_username(username)
);
-- ALTER TABLE u_admin_user CHANGE COLUMN is_maintain_man maintain_man_type tinyint(1) NOT NULL DEFAULT '0' COMMENT '0：员工，1：维修员，2：维修主管';
-- ALTER TABLE u_admin_user ADD COLUMN login_path      varchar(100)      comment '登录跳转路径' AFTER maintain_man_type;

/* 权限资源表 */
DROP TABLE IF EXISTS u_cos_item;
CREATE TABLE u_cos_item(
    id             bigint unsigned   not null    auto_increment,
    name           varchar(60)       not null    default '' comment '权限名',
    display_name   varchar(60)       not null    default '' comment '权限展示名',
    url            varchar(200)      comment '链接',
    parent_id      bigint unsigned   not null    default 0  comment '父id',
    order_no       tinyint unsigned  not null    default 50 comment '排序',
    is_disabled    tinyint(1)        not null    default 0  comment '禁用',
    creator_id     bigint unsigned   not null    default 0  comment '创建人id',
    creator_name   varchar(30)       not null    default '' comment '创建人姓名',
    create_time    datetime          not null    comment '创建时间',
    modifier_id    bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name  varchar(30)       not null    default '' comment '修改人姓名',
    modified_time  datetime          not null    comment '修改时间',
    primary key (id),
    unique key uk_cosItem_name(name)
);

/* 角色 */
DROP TABLE IF EXISTS u_role;
CREATE TABLE u_role(
    id             bigint unsigned   not null    auto_increment,
    name           varchar(60)       not null    default '' comment '角色名',
    code varchar(50)  COMMENT '角色编码',
    is_disabled    tinyint(1)        not null    default 0  comment '禁用',
    creator_id     bigint unsigned   not null    default 0  comment '创建人id',
    creator_name   varchar(30)       not null    default '' comment '创建人姓名',
    create_time    datetime          not null    comment '创建时间',
    modifier_id    bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name  varchar(30)       not null    default '' comment '修改人姓名',
    modified_time  datetime          not null    comment '修改时间',
    primary key (id),
    unique key uk_role_name(name)
);
-- ALTER TABLE u_role ADD COLUMN code varchar(50)  COMMENT '角色编码' AFTER name;


/* 角色权限 */
DROP TABLE IF EXISTS u_role_cos;
CREATE TABLE u_role_cos(
    role_id        bigint unsigned   not null    comment '角色id',
    cos_id         bigint unsigned   not null    comment '资源id',
    is_disabled    tinyint(1)        not null    default 0  comment '禁用',
    creator_id     bigint unsigned   not null    default 0  comment '创建人id',
    creator_name   varchar(30)       not null    default '' comment '创建人姓名',
    create_time    datetime          not null    comment '创建时间',
    modifier_id    bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name  varchar(30)       not null    default '' comment '修改人姓名',
    modified_time  datetime          not null    comment '修改时间',
    primary key (role_id, cos_id)
);

/* 用户角色 */
DROP TABLE IF EXISTS u_admin_user_role;
CREATE TABLE u_admin_user_role(
    user_id        bigint unsigned   not null    comment '用户id',
    role_id        bigint unsigned   not null    comment '角色id',
    is_disabled    tinyint(1)        not null    default 0  comment '禁用',
    creator_id     bigint unsigned   not null    default 0  comment '创建人id',
    creator_name   varchar(30)       not null    default '' comment '创建人姓名',
    create_time    datetime          not null    comment '创建时间',
    modifier_id    bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name  varchar(30)       not null    default '' comment '修改人姓名',
    modified_time  datetime          not null    comment '修改时间',
    primary key (user_id, role_id)
);

/* 用户部门 */
DROP TABLE IF EXISTS u_admin_user_department;
CREATE TABLE u_admin_user_department(
    user_id      bigint unsigned   not null    comment '用户id',
    department_id bigint unsigned   not null    default 0  comment '部门id',
    primary key (user_id, department_id)
);



/* 客户信息 */
DROP TABLE IF EXISTS u_customer;
CREATE TABLE u_customer(
    id             bigint unsigned   not null    auto_increment,
    serial_no      varchar(20)       not null    comment '编号',
    name           varchar(60)       not null    comment '客户名称',
    idcard         varchar(30)       not null    default '' comment '证件号码',
    idcard_type    tinyint unsigned  not null    default 0,
    gender         tinyint unsigned  comment '性别，1男；2女；3其他',
    nation         varchar(20)       comment '民族',
    born_date      date              comment '出生日期',
    native_place   varchar(50)       comment '籍贯',
    address        varchar(500)      comment '地址',
    telephone      varchar(20)       not null    default '' comment '手机',
    email          varchar(30)       comment '邮箱',
    province_id    bigint unsigned   not null default 0 comment '省/直辖市id',
    province_name  varchar(30)       comment '省/直辖市名',
    city_id        bigint unsigned   not null default 0 comment '市/区id',
    city_name      varchar(30)       comment '市/区名',
    workplace_region_id    bigint unsigned comment '工作地 - 经营区域id',
    workplace_region_name  varchar(200)    comment '工作地 - 经营区域名',
    workplace_street_id    bigint unsigned comment '工作地 - 街道id',
    workplace_street_name  varchar(200)    comment '工作地 - 街道名',
    workplace_address      varchar(200)    comment '工作地 - 地址',
    profession_id   bigint unsigned   not null    default 0  comment '行业id',
    profession_name varchar(30)       comment '行业名',
    job_title       varchar(20)       comment '工作职位',
    is_nat_tested           tinyint(1)   not null  default 0  comment '是否做核酸检测',
    is_covid19_vaccination  tinyint(1)   not null  default 0  comment '是否注射了新冠疫苗',
    is_znw_synced   tinyint(1)        not null    default 0  comment '中农网同步',
    is_disabled     tinyint(1)        not null    default 0  comment '禁用',
    creator_id      bigint unsigned   not null    default 0  comment '创建人id',
   	creator_name    varchar(30)       not null    default '' comment '创建人姓名',
   	create_time     datetime          not null    comment '创建时间',
    modifier_id     bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name   varchar(30)       not null    default '' comment '修改人姓名',
   	modified_time   datetime          not null    comment '修改时间',
    primary key (id)
);
-- ALTER TABLE u_customer ADD UNIQUE KEY uk_customer_serialNo(serial_no);
-- ALTER TABLE u_customer ADD INDEX idx_customer_name(name);
-- ALTER TABLE u_customer ADD INDEX idx_customer_idcard(idcard);
-- ALTER TABLE u_customer ADD INDEX idx_customer_telephone(telephone);
-- ALTER TABLE u_customer ADD INDEX idx_customer_workplace_regionId(workplace_region_id);
-- ALTER TABLE u_customer ADD INDEX idx_customer_workplace_streetId(workplace_street_id);

/* 客户扩展信息 */
DROP TABLE IF EXISTS u_customer_ext;
CREATE TABLE u_customer_ext(
    id             bigint unsigned   not null    auto_increment,
    customer_id    bigint unsigned   not null,
    bank_account   varchar(50)       comment '银行账户',
    remark         varchar(500)      comment '备注',
    avatar         text,
    is_disabled    tinyint(1)        not null    default 0  comment '禁用',
    creator_id     bigint unsigned   not null    default 0  comment '创建人id',
    creator_name   varchar(30)       not null    default '' comment '创建人姓名',
    create_time    datetime          not null    comment '创建时间',
    modifier_id    bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name  varchar(30)       not null    default '' comment '修改人姓名',
    modified_time  datetime          not null    comment '修改时间',
    primary key (id),
    unique key uk_customer_ext_customerId(customer_id)
);

/* 客户新冠防控信息 */
DROP TABLE IF EXISTS u_customer_covid19;
CREATE TABLE u_customer_covid19(
    id             bigint unsigned   not null    auto_increment,
    customer_id    bigint unsigned   not null    default 0  comment '客户id',
    category       tinyint           not null    default 0  comment '类型， 1核酸检测; 2疫苗注射',
    operate_date   date              not null    comment '操作日期',
    remark         varchar(200)      comment     '备注',
    is_disabled    tinyint(1)        not null    default 0  comment '禁用',
    creator_id     bigint unsigned   not null    default 0  comment '创建人id',
    creator_name   varchar(30)       not null    default '' comment '创建人姓名',
    create_time    datetime          not null    comment '创建时间',
    modifier_id    bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name  varchar(30)       not null    default '' comment '修改人姓名',
    modified_time  datetime          not null    comment '修改时间',
    primary key (id)
);
-- ALTER TABLE u_customer_covid19 ADD INDEX idx_customerCovid_customerId_category(customer_id, category);

/* ic卡管理 */
DROP TABLE IF EXISTS u_ic_card;
CREATE TABLE u_ic_card(
    id             bigint unsigned   not null    auto_increment,
    card_sn        varchar(20)       not null    default '' comment '卡sn码',
    card_no        varchar(20)       not null    default '' comment '卡号',
    foregift       decimal(6, 2)     not null    default 0  comment '工本费',
    balance        decimal(10, 2)    not null    default 0  comment '余额',
    account_no     varchar(20)       comment '账户编号',
    customer_id    bigint unsigned   not null    default 0  comment '客户id',
    grant_time     datetime          not null    comment '发放日期',
    retrieve_time  datetime          comment '回收日期',
    master         tinyint           not null    default 0  comment '是否主卡',
    status         tinyint unsigned  not null    default 0  comment '状态，0未用；1申领；2挂失；3作废',
    is_disabled    tinyint(1)        not null    default 0  comment '禁用',
    creator_id     bigint unsigned   not null    default 0  comment '创建人id',
    creator_name   varchar(30)       not null    default '' comment '创建人姓名',
    create_time    datetime          not null    comment '创建时间',
    modifier_id    bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name  varchar(30)       not null    default '' comment '修改人姓名',
    modified_time  datetime          not null    comment '修改时间',
    primary key (id),
    unique key uk_icCard_cardNo(card_no)
);
-- ALTER TABLE u_ic_card ADD INDEX idx_icCard_customer(customer_id);
-- ALTER TABLE u_ic_card ADD INDEX idx_icCard_grantTime(grant_time);

/* 公司 */
DROP TABLE IF EXISTS u_company;
CREATE TABLE u_company(
    id             bigint unsigned   not null    auto_increment,
    company_name   varchar(64)       not null    comment '公司名',
    order_no       smallint unsigned not null    default 50 comment '排序',
    is_disabled    tinyint(1)        not null    default 0  comment '禁用',
    creator_id     bigint unsigned   not null    default 0  comment '创建人id',
    creator_name   varchar(30)       not null    default '' comment '创建人姓名',
    create_time    datetime          not null    comment '创建时间',
    modifier_id    bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name  varchar(30)       not null    default '' comment '修改人姓名',
    modified_time  datetime          not null    comment '修改时间',
    primary key (id),
    unique key uk_company_name(company_name)
);

DROP TABLE IF EXISTS u_department;
CREATE TABLE u_department(
    id             bigint unsigned   not null    auto_increment,
    name           varchar(64)       not null    comment '部门名',
    full_name      varchar(200)      comment '部门全称',
    parent_id      bigint unsigned   not null    default 0  comment '父部门id',
    order_no       smallint unsigned not null    default 50 comment '排序',
    is_disabled    tinyint(1)        not null    default 0  comment '禁用',
    creator_id     bigint unsigned   not null    default 0  comment '创建人id',
    creator_name   varchar(30)       not null    default '' comment '创建人姓名',
    create_time    datetime          not null    comment '创建时间',
    modifier_id    bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name  varchar(30)       not null    default '' comment '修改人姓名',
    modified_time  datetime          not null    comment '修改时间',
    primary key (id),
    unique key uk_department_name(parent_id, name)
);

DROP TABLE IF EXISTS u_employee;
CREATE TABLE u_employee(
    id             bigint unsigned   not null    auto_increment,
    username       varchar(64)       not null    default '' comment '用户名',
    company_name   varchar(64)       not null    default '' comment '公司名',
    department_id bigint unsigned   not null    default 0  comment '部门id',
    mp_openid      varchar(64)       not null    default '' comment '用户微信id',
    default_booked smallint unsigned not null    default 1  comment '默认订餐人数',
    is_canteen_master tinyint(1)     not null    default 0  comment '是否是饭堂管理员',
    remark         varchar(200)      comment '备注',
    is_disabled    tinyint(1)        not null    default 0  comment '禁用',
    creator_id     bigint unsigned   not null    default 0  comment '创建人id',
    creator_name   varchar(30)       not null    default '' comment '创建人姓名',
    create_time    datetime          not null    comment '创建时间',
    modifier_id    bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name  varchar(30)       not null    default '' comment '修改人姓名',
    modified_time  datetime          not null    comment '修改时间',
    primary key (id),
    unique key uk_employee_company_username(company_name, username)
);
-- alter table u_employee add index idx_employee_mpOpenid(mp_openid);

DROP TABLE IF EXISTS u_employee_meal_delegate;
CREATE TABLE u_employee_meal_delegate(
    id             bigint unsigned   not null    auto_increment,
    employee_id    bigint unsigned   not null    default 0  comment '员工id',
    delegate_id    bigint unsigned   not null    default 0  comment '代理id',
    type           tinyint unsigned  not null    default 0  comment '1 员工；2公司',
    is_disabled    tinyint(1)        not null    default 0  comment '禁用',
    creator_id     bigint unsigned   not null    default 0  comment '创建人id',
    creator_name   varchar(30)       not null    default '' comment '创建人姓名',
    create_time    datetime          not null    comment '创建时间',
    modifier_id    bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name  varchar(30)       not null    default '' comment '修改人姓名',
    modified_time  datetime          not null    comment '修改时间',
    primary key (id),
    unique key uk_employee_meal_delegate(employee_id, delegate_id, type)
);


DROP TABLE IF EXISTS wx_booking_meal_company_day_report;
CREATE TABLE wx_booking_meal_company_day_report(
    id        bigint unsigned   not null    auto_increment,
    meal_date date              not null    comment '用餐日期',
    company   varchar(64)       not null    default '' comment '公司',
    booked_count   int unsigned not null    default 0 comment '订餐人数',
    unbooked_count int unsigned not null    default 0 comment '不订餐人数',
    meal_year    smallint unsigned not null default 0 comment '用餐年份',
    meal_month   tinyint unsigned  not null default 0 comment '用餐月份',
    is_total     tinyint unsigned  not null default 0 comment '是否总计',
    create_time    datetime     not null    comment '创建时间',
    modified_time  datetime     not null    comment '修改时间',
    primary key(id),
    unique key uk_booking_meal_company_day_report(meal_date, company)
);
-- alter table wx_booking_meal_company_day_report add index idx_booking_meal_company_day_report_company(company);

DROP TABLE IF EXISTS u_holiday;
CREATE TABLE u_holiday(
    holiday        date              not null    comment '假期日期',
    description    varchar(60)       comment '描述',
    is_disabled    tinyint(1)        not null    default 0  comment '禁用',
    creator_id     bigint unsigned   not null    default 0  comment '创建人id',
    creator_name   varchar(30)       not null    default '' comment '创建人姓名',
    create_time    datetime          not null    comment '创建时间',
    modifier_id    bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name  varchar(30)       not null    default '' comment '修改人姓名',
    modified_time  datetime          not null    comment '修改时间',
    primary key(holiday)
);

DROP TABLE IF EXISTS u_dining_information;
CREATE TABLE u_dining_information  (
    id bigint UNSIGNED NOT NULL AUTO_INCREMENT,
    employee_name varchar(64) NOT NULL COMMENT '用户',
    meal_date date NOT NULL COMMENT '就餐日期',
    meal_time time NOT NULL COMMENT '就餐时间',
    dining tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否就餐',
    disabled    tinyint(1)        not null    default 0  comment '禁用',
    creator_id     bigint unsigned   not null    default 0  comment '创建人id',
    creator_name   varchar(30)       not null    default '' comment '创建人姓名',
    create_time    datetime          not null    comment '创建时间',
    modifier_id    bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name  varchar(30)       not null    default '' comment '修改人姓名',
    modified_time  datetime          not null    comment '修改时间',
    PRIMARY KEY (id),
    unique key uk_dining_informationl_employee_date(employee_name, meal_date)
);

/* 住户 */
DROP TABLE IF EXISTS u_resident;
CREATE TABLE u_resident(
    id               bigint unsigned             not null auto_increment,
    resident_no      varchar(20)       not null   default '' comment '唯一编号',
    resident_type    tinyint unsigned  not null    default 0 comment '住户类型 0 住户 1 员工 2 其他',
    name             varchar(50)   default ''                  comment '姓名',
    id_num           varchar(20)   default ''                  comment '身份证号码',
    born_date        date                                      comment '出生日期',
    gender           tinyint unsigned                          comment '1 男; 2 女',
    nature           varchar(20)                               comment '民族',
    telephone        varchar(11)   default ''                  comment '电话号码',
    address          varchar(50)   default ''                  comment '具体住址',
    face_url         varchar(200)       not null    comment '人脸',
    remark         varchar(200)      comment     '备注',
    disabled    tinyint(1)        not null   default 0  comment '禁用',
    creator_id     bigint unsigned   not null   default 0  comment '创建人id',
    creator_name   varchar(30)       not null   default '' comment '创建人姓名',
    create_time    datetime          not null   comment '创建时间',
    modifier_id    bigint unsigned   not null   default 0  comment '修改人id',
    modifier_name  varchar(30)       not null   default '' comment '修改人姓名',
    modified_time  datetime          not null   comment '修改时间',
    primary key (id),
    unique key u_resident_resident_no(`resident_no`)
);

/* 住户权限 */
DROP TABLE IF EXISTS u_resident_access;
CREATE TABLE u_resident_access(
    id               bigint unsigned             not null auto_increment,
    resident_no      varchar(20)       not null   default '' comment '唯一编号',
    device_id      bigint unsigned   not null  comment '设备id',
    realty_serial      varchar(30)    not null   default ''  comment '物业编号',
    contract_no      varchar(30)        not null   default '' comment '合同编号',
    access         tinyint(1)        not null   default 0 comment '拥有权限',
    state       tinyint(1)      default 0                 comment '是否生效（0：未生效，1：已生效）',
    disabled    tinyint(1)        not null   default 0  comment '禁用',
    creator_id     bigint unsigned   not null   default 0  comment '创建人id',
    creator_name   varchar(30)       not null   default '' comment '创建人姓名',
    create_time    datetime          not null   comment '创建时间',
    modifier_id    bigint unsigned   not null   default 0  comment '修改人id',
    modifier_name  varchar(30)       not null   default '' comment '修改人姓名',
    modified_time  datetime          not null   comment '修改时间',
    primary key (id)
);


/* 意见反馈 */
DROP TABLE IF EXISTS u_feed_back;
CREATE TABLE u_feed_back(
    id               bigint unsigned             not null auto_increment,
    openid         varchar(64)       not null    comment '微信用户id',
    title            varchar(100)      not null        comment '标题',
    anonymous         tinyint(1)        not null   default 0 comment '是否匿名',
    name      varchar(30)        not null   default '' comment '姓名',
    contact         varchar(20)       not null    default '' comment '联系方式',
    content         varchar(255)      not null            comment '反馈内容',
    reply_state         tinyint(1)        not null  default 0 comment '回复状态',
    disabled    tinyint(1)        not null   default 0  comment '禁用',
    creator_id     bigint unsigned   not null   default 0  comment '创建人id',
    creator_name   varchar(30)       not null   default '' comment '创建人姓名',
    create_time    datetime          not null   comment '创建时间',
    modifier_id    bigint unsigned   not null   default 0  comment '修改人id',
    modifier_name  varchar(30)       not null   default '' comment '修改人姓名',
    modified_time  datetime          not null   comment '修改时间',
    primary key (id)
);

DROP TABLE IF EXISTS u_feed_back_media;
CREATE TABLE u_feed_back_media(
    id              bigint unsigned   not null    auto_increment,
    feed_back_id    bigint unsigned   not null    default 0 comment '意见反馈id',
    media_url       varchar(100)      comment '多媒体资料访问连接',
    modified_time   datetime          comment '修改时间',
    primary key(id)
);

/* 意见反馈回复 */
DROP TABLE IF EXISTS u_feed_back_reply;
CREATE TABLE u_feed_back_reply(
    id               bigint unsigned             not null auto_increment,
    openid         varchar(64)       not null default ''  comment '微信用户id',
    feed_back_id   bigint unsigned   not null comment '建议评论id',
    content        varchar(255)      not null default '' comment '评论内容',
    name      varchar(30)        not null   default '' comment '姓名',
    parent_id  bigint unsigned   default null comment '父级评论',
    disabled    tinyint(1)        not null   default 0  comment '禁用',
    creator_id     bigint unsigned   not null   default 0  comment '创建人id',
    creator_name   varchar(30)       not null   default '' comment '创建人姓名',
    create_time    datetime          not null   comment '创建时间',
    modifier_id    bigint unsigned   not null   default 0  comment '修改人id',
    modifier_name  varchar(30)       not null   default '' comment '修改人姓名',
    modified_time  datetime          not null   comment '修改时间',
    primary key (id),
    unique key uk_feed_back_id_parent_id(feed_back_id, parent_id)
);

DROP TABLE IF EXISTS u_auth_credentials;
CREATE TABLE u_auth_credentials
(
    app_key       varchar(18) primary key comment '公钥',
    app_secret    varchar(32)     not null default '' comment '私钥',
    user_id       bigint unsigned not null default 0 comment '用户id',
    creator_id    bigint unsigned not null default 0 comment '创建人id',
    creator_name  varchar(30)     not null default '' comment '创建人姓名',
    create_time   datetime        not null comment '创建时间',
    modifier_id   bigint unsigned not null default 0 comment '修改人id',
    modifier_name varchar(30)     not null default '' comment '修改人姓名',
    modified_time datetime        not null comment '修改时间'
);

drop table if exists u_merchant;
create table u_merchant (
    id                 bigint unsigned            not null auto_increment,
    name               varchar(30)                not null comment '商户名',
    contact            varchar(20)     default '' not null comment '联系方式',
    idcard             varchar(30)     default '' not null comment '身份证',
    address            varchar(500)               null comment '地址',
    rc_serial          varchar(20)     default '' not null comment '冷藏客户编号',
    rc_tax_header varchar(100) default '' NOT NULL COMMENT '冷藏发票抬头',
    is_duoduo          tinyint(1)      default 0  not null comment '多多客户',
    bicycle_auth       tinyint(1)      default 0  not null comment '三轮车权限',
    dry_auth           tinyint(1)      default 0  not null comment '干仓权限',
    referral_code      varchar(20)                comment '推荐码',
    bicycle_charges_id bigint unsigned default 0  not null comment '三轮车收费标准id',
    settle_period      tinyint(1)      default 0  not null comment '结算周期',
    remark             varchar(500)               comment '备注',
    is_disabled        tinyint(1)      default 0  not null comment '禁用',
    creator_id         bigint unsigned            not null default 0 comment '创建人id',
    creator_name       varchar(30)                not null default '' comment '创建人姓名',
    create_time        datetime                   not null comment '创建时间',
    modifier_id        bigint unsigned            not null default 0 comment '修改人id',
    modifier_name      varchar(30)                not null default '' comment '修改人姓名',
    modified_time      datetime                   not null comment '修改时间',
    primary key(id),
    unique key uk_merchant_name(`name`)
);
-- alter table u_merchant add index idx_merchant_rc_serial(`rc_serial`);
-- ALTER TABLE u_merchant ADD COLUMN referral_code varchar(20)  comment '推荐码' AFTER bicycle_auth;
-- ALTER TABLE u_merchant ADD COLUMN dry_auth  tinyint(1)  default 0  not null comment '干仓权限' AFTER bicycle_auth;
-- ALTER TABLE u_merchant ADD COLUMN rc_tax_header varchar(100) default '' NOT NULL COMMENT '冷藏发票抬头' AFTER rc_serial;


drop table if exists u_merchant_auth_apply;
create table u_merchant_auth_apply (
    id                 bigint unsigned            not null auto_increment,
    merchant_id        bigint unsigned default 0  not null comment '商户id',
    merchant_name      varchar(30)                not null comment '商户名',
    contact            varchar(20)     default '' not null comment '联系方式',
    idcard             varchar(30)     default '' not null comment '身份证',
    address            varchar(500)               comment '地址',
    rc_serial          varchar(20)                comment '冷藏客户编号',
    bicycle_auth       tinyint(1)                 comment '三轮车权限',
    dry_auth           tinyint(1)                 comment '干仓权限',
    referral_code      varchar(20)                comment '推荐码',
    remark             varchar(500)               comment '备注',
    apply_time         datetime                   not null comment '申请时间',
    audit_status       tinyint unsigned default 0 not null comment '审核结果 0 待审核，1 审核通过，99 审核不通过',
    audit_remark       varchar(500)               comment '审核备注',
    audit_id           bigint unsigned            not null default 0 comment '审核人',
    audit_time         datetime                   comment '审核时间',
    is_disabled        tinyint(1)      default 0  not null comment '禁用',
    create_openid      varchar(64)     default '' not null comment '申请人openid',
    creator_id         bigint unsigned            not null default 0 comment '创建人id',
    creator_name       varchar(30)                not null default '' comment '创建人姓名',
    create_time        datetime                   not null comment '创建时间',
    modifier_id        bigint unsigned            not null default 0 comment '修改人id',
    modifier_name      varchar(30)                not null default '' comment '修改人姓名',
    modified_time      datetime                   not null comment '修改时间',
    primary key (id)
);
-- alter table u_merchant_auth_apply add index idx_merchant_auth_apply_merchantId(`merchant_id`);
-- alter table u_merchant_auth_apply add index idx_merchant_auth_apply_merchantName(`merchant_name`);
-- ALTER TABLE u_merchant_auth_apply ADD COLUMN referral_code varchar(20)  comment '推荐码' AFTER bicycle_auth;
-- ALTER TABLE u_merchant_auth_apply ADD COLUMN dry_auth  tinyint(1) comment '干仓权限' AFTER bicycle_auth;


DROP TABLE IF EXISTS u_reservation_record;
create table u_reservation_record(
    id bigint unsigned   not null primary key   auto_increment,
    visitor_name     varchar(20)       not null   default '' comment '访客姓名',
    contact varchar(20) not null default ''  '访客电话',
    together_num   int   not null default 0 comment '随行人数',
    visit_time_start    datetime          not null    comment '拜访时间起',
    visit_time_end      datetime          not null    comment '拜访时间止',
    create_openid      varchar(64)     default '' not null comment '申请人openid',
    type      tinyint       unsigned default 0 not null comment '预约类别(0:年货节;1:开仓节;)',
    is_disabled   tinyint(1)       default 0   not null comment '禁用',
    creator_id    bigint unsigned  default '0' not null comment '创建人id',
    creator_name  varchar(30)      default ''  not null comment '创建人姓名',
    create_time   datetime                     not null comment '创建时间',
    modifier_id   bigint unsigned  default '0' not null comment '修改人id',
    modifier_name varchar(30)      default ''  not null comment '修改人姓名',
    modified_time datetime                     not null comment '修改时间'
);
-- ALTER TABLE u_reservation_record ADD COLUMN type      tinyint       unsigned default 0 not null comment '预约类别(0:年货节;1:开仓节;)' AFTER create_openid;
-- ALTER TABLE u_reservation_record ADD INDEX idx_reservation_record_type(type);

DROP TABLE IF EXISTS u_reservation_record_item;
create table u_reservation_record_item(
    id bigint unsigned   not null primary key   auto_increment,
    reservation_record_id bigint unsigned not null comment '预约记录id',
    car_no   varchar(20)      default ''  not null comment '车牌',
    is_disabled   tinyint(1)       default 0   not null comment '禁用',
    creator_id    bigint unsigned  default '0' not null comment '创建人id',
    creator_name  varchar(30)      default ''  not null comment '创建人姓名',
    create_time   datetime                     not null comment '创建时间',
    modifier_id   bigint unsigned  default '0' not null comment '修改人id',
    modifier_name varchar(30)      default ''  not null comment '修改人姓名',
    modified_time datetime                     not null comment '修改时间'
);
-- ALTER TABLE u_reservation_record_item ADD INDEX idx_reservation_record_id(reservation_record_id);


DROP TABLE IF EXISTS u_enterprise;
CREATE TABLE u_enterprise(
    id             bigint unsigned   not null   auto_increment,
    name           varchar(100)      not null   default '' comment '企业名称',
    full_name      varchar(100)                            comment '营业执照名',
    charge_man     varchar(60)       not null   default '' comment '负责人',
    contact1       varchar(30)       not null   default '' comment '联系方式1',
    contact2       varchar(30)       not null   default '' comment '联系方式2',
    category       varchar(30)       not null   default '' comment '经营类别',
    other_category varchar(100)                            comment '其他经营类别',
    is_firefighting_emphasis  tinyint(1) unsigned default 0 comment '消防重点',
    address        varchar(100)                            comment '地址',
    remark         varchar(200)                            comment '备注',
    is_disabled    tinyint(1)        not null   default 0  comment '禁用',
    creator_id     bigint unsigned   not null   default 0  comment '创建人id',
    creator_name   varchar(30)       not null   default '' comment '创建人姓名',
    create_time    datetime          not null   comment '创建时间',
    modifier_id    bigint unsigned   not null   default 0  comment '修改人id',
    modifier_name  varchar(30)       not null   default '' comment '修改人姓名',
    modified_time  datetime          not null   comment '修改时间',
    primary key (id),
    unique key uk_enterprise_name(name, full_name)
);


DROP TABLE IF EXISTS u_enterprise_realty;
CREATE TABLE u_enterprise_realty(
    id             bigint unsigned   not null   auto_increment,
    enterprise_id  bigint unsigned   not null   default 0  comment '企业id',
    realty_serial  varchar(30)       not null   default '' comment '物业编号',
    realty_alias   varchar(30)       not null   default '' comment '物业别名',
    is_disabled    tinyint(1)        not null   default 0  comment '禁用',
    creator_id     bigint unsigned   not null   default 0  comment '创建人id',
    creator_name   varchar(30)       not null   default '' comment '创建人姓名',
    create_time    datetime          not null   comment '创建时间',
    modifier_id    bigint unsigned   not null   default 0  comment '修改人id',
    modifier_name  varchar(30)       not null   default '' comment '修改人姓名',
    modified_time  datetime          not null   comment '修改时间',
    primary key (id),
    unique key uk_enterprise_realty(realty_serial, realty_alias)
);
-- ALTER TABLE u_enterprise_realty ADD INDEX idx_enterprise_realty_enterprise(enterprise_id);

DROP TABLE IF EXISTS dict_business_category;
CREATE TABLE dict_business_category(
    id             bigint unsigned   not null   auto_increment,
    code           varchar(30)       not null   default '' comment '经营范围编码',
    description    varchar(100)                            comment '经营范围描述',
    order_num      smallint unsigned not null   default 99 comment '排序号',
    is_disabled    tinyint(1)        not null   default 0  comment '禁用',
    creator_id     bigint unsigned   not null   default 0  comment '创建人id',
    creator_name   varchar(30)       not null   default '' comment '创建人姓名',
    create_time    datetime          not null   comment '创建时间',
    modifier_id    bigint unsigned   not null   default 0  comment '修改人id',
    modifier_name  varchar(30)       not null   default '' comment '修改人姓名',
    modified_time  datetime          not null   comment '修改时间',
    primary key (id),
    unique key uk_business_category(code)
);

DROP TABLE IF EXISTS u_activity;
create table u_activity(
    id bigint unsigned   not null primary key   auto_increment,
    name     varchar(50)       not null   default '' comment '活动名称',
    uuid  varchar(50)  not null default '' comment 'uuid',
    url     varchar(60)       not null   default '' comment '活动链接',
    start_time datetime          not null   comment '开始时间',
    end_time   datetime          not null   comment '结束时间',
    bg_url    varchar(120)      not null default '' '背景图片',
    bg_color  varchar(50)  not null default ''  comment '背景颜色',
    font_color  varchar(50)  not null default ''  comment '字体颜色',
    remark    varchar(50)    comment '备注',
    share_title varchar(50)  not null default ''  comment '分享标题',
    share_blurb varchar(50)  not null default ''  comment '分享简介',
    share_url varchar(120)  not null default ''  comment '分享图片',
    blurb  text  comment '活动简介',
    stop_time datetime        comment '中止时间',
    limit_num      smallint unsigned not null   default 0 comment '限制数量',
    status    tinyint unsigned default 0 not null comment '状态(0:初始化;1:生效;2:未生效)',
    category      tinyint       unsigned default 0 not null comment '活动类别(0:投票;1:抽奖;)',
    is_disabled   tinyint(1)       default 0   not null comment '禁用',
    creator_id    bigint unsigned  default '0' not null comment '创建人id',
    creator_name  varchar(30)      default ''  not null comment '创建人姓名',
    create_time   datetime                     not null comment '创建时间',
    modifier_id   bigint unsigned  default '0' not null comment '修改人id',
    modifier_name varchar(30)      default ''  not null comment '修改人姓名',
    modified_time datetime                     not null comment '修改时间'
);

DROP TABLE IF EXISTS u_vote_category;
create table u_vote_category(
    id bigint unsigned   not null primary key   auto_increment,
    name     varchar(50)       not null   default '' comment '类别名',
    activity_id  bigint unsigned not null default 0 comment '投票活动id',
    is_disabled   tinyint(1)       default 0   not null comment '禁用',
    modified_time datetime                     not null comment '修改时间'
);

DROP TABLE IF EXISTS u_vote_resources;
create table u_vote_resources(
    id bigint unsigned   not null primary key   auto_increment,
    activity_id  bigint unsigned not null comment '投票活动id',
    name     varchar(50)       not null   default '' comment '名称',
    serial   smallint unsigned not null   default 0 comment '编号',
    category  bigint unsigned not null comment '类别',
    description     varchar(255)       not null   default '' comment '简介',
    numbers  smallint unsigned not null   default 0 comment '票数',
    thumbnail varchar(100)  not null default '' comment '缩略图',
    original  varchar(100)  not null default '' comment '原图',
    is_disabled   tinyint(1)       default 0   not null comment '禁用',
    creator_id    bigint unsigned  default '0' not null comment '创建人id',
    creator_name  varchar(30)      default ''  not null comment '创建人姓名',
    create_time   datetime                     not null comment '创建时间',
    modifier_id   bigint unsigned  default '0' not null comment '修改人id',
    modifier_name varchar(30)      default ''  not null comment '修改人姓名',
    modified_time datetime                     not null comment '修改时间'
);
-- ALTER TABLE u_vote_resources ADD INDEX idx_vote_resources_activity(activity_id);
-- ALTER TABLE u_vote_resources ADD INDEX idx_vote_resources_category(category);


DROP TABLE IF EXISTS u_vote_records;
create table u_vote_records(
    id bigint unsigned   not null primary key   auto_increment,
    activity_id  bigint unsigned not null comment '投票活动id',
    resources_id  bigint unsigned not null comment '资源id',
    openid varchar(64)     default '' not null comment 'openid',
    numbers  smallint unsigned not null   default 0 comment '票数',
    remark     varchar(100)       not null   default '' comment '备注',
    is_disabled   tinyint(1)       default 0   not null comment '禁用',
    creator_id    bigint unsigned  default '0' not null comment '创建人id',
    creator_name  varchar(30)      default ''  not null comment '创建人姓名',
    create_time   datetime                     not null comment '创建时间',
    modifier_id   bigint unsigned  default '0' not null comment '修改人id',
    modifier_name varchar(30)      default ''  not null comment '修改人姓名',
    modified_time datetime                     not null comment '修改时间'
);
-- ALTER TABLE u_vote_records ADD INDEX idx_vote_records_activity(activity_id);
-- ALTER TABLE u_vote_records ADD INDEX idx_vote_records_resources(resources_id);

DROP TABLE IF EXISTS u_prize;
create table u_prize(
     id bigint unsigned   not null primary key   auto_increment,
     activity_id  bigint unsigned not null comment '抽奖活动id',
     name varchar(30) not null default '' comment '奖品名称',
     description varchar(100) default '' comment '奖品描述',
     media_url varchar(100)  not null default '' comment '媒体资源',
     total_num int not null default 0 comment '奖品总数量',
     remaining_num int not null default 0 comment '剩余奖品数量',
     probability int not null default 0 comment '中奖概率(0-100)',
     is_disabled   tinyint(1)       default 0   not null comment '禁用',
     creator_id    bigint unsigned  default '0' not null comment '创建人id',
     creator_name  varchar(30)      default ''  not null comment '创建人姓名',
     create_time   datetime                     not null comment '创建时间',
     modifier_id   bigint unsigned  default '0' not null comment '修改人id',
     modifier_name varchar(30)      default ''  not null comment '修改人姓名',
     modified_time datetime                     not null comment '修改时间'
);
-- ALTER TABLE u_prize ADD INDEX idx_prize_activity(activity_id);

DROP TABLE IF EXISTS u_prize_records;
create table u_prize_records(
    id bigint unsigned   not null primary key   auto_increment,
    uuid  varchar(50)  not null default '' comment 'uuid',
    activity_id  bigint unsigned not null comment '投票活动id',
    prize_id  bigint  comment '奖品id，NULL表示未中奖',
    prize_name  varchar(30) not null default '' comment '奖品名称',
    openid varchar(64)     default '' not null comment 'openid',
    is_win tinyint(1) not null default 0 comment '是否中奖',
    is_verify   tinyint(1) not null default 0 comment '是否核销',
    is_disabled   tinyint(1)       default 0   not null comment '禁用',
    creator_id    bigint unsigned  default '0' not null comment '创建人id',
    creator_name  varchar(30)      default ''  not null comment '创建人姓名',
    create_time   datetime                     not null comment '创建时间',
    modifier_id   bigint unsigned  default '0' not null comment '修改人id',
    modifier_name varchar(30)      default ''  not null comment '修改人姓名',
    modified_time datetime                     not null comment '修改时间'
);
-- ALTER TABLE u_prize_records ADD INDEX idx_prize_records_activity(activity_id);
-- ALTER TABLE u_prize_records ADD INDEX idx_prize_records(prize_id);

DROP TABLE IF EXISTS u_prize_draw_number;
create table u_prize_draw_number(
    id bigint unsigned   not null primary key   auto_increment,
    activity_id  bigint unsigned not null comment '投票活动id',
    openid varchar(64)     default '' not null comment 'openid',
    type      tinyint       unsigned default 0 not null comment '获取票数方式(0:关注;1:转发;)',
    modified_time datetime                     not null comment '修改时间'
);
