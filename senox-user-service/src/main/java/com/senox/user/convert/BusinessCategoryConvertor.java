package com.senox.user.convert;

import com.senox.user.domain.BusinessCategory;
import com.senox.user.vo.BusinessCategoryVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/8 11:20
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface BusinessCategoryConvertor {

    /**
     * 经营范围视图转实体
     * @param vo
     * @return
     */
    BusinessCategory toDo(BusinessCategoryVo vo);

    /**
     *经营范围实体转视图
     * @param domain
     * @return
     */
    BusinessCategoryVo toVo(BusinessCategory domain);

    /**
     * 经营范围实体转视图
     * @param list
     * @return
     */
    List<BusinessCategoryVo> toVo(List<BusinessCategory> list);
}
