<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.user.mapper.AuthCredentialsMapper">

    <resultMap id="resultMap" type="com.senox.user.vo.AuthCredentialsVo">
        <result property="appKey" column="app_key"/>
        <result property="appSecret" column="app_secret"/>
        <result property="userId" column="user_id"/>
        <collection property="userDto" ofType="com.senox.context.AdminUserDto">
            <id property="userId" column="user_id"/>
            <result property="username" column="username"/>
            <result property="realName" column="real_name"/>
            <result property="loginable" column="loginable"/>
        </collection>
    </resultMap>

    <insert id="add">
        insert into u_auth_credentials(app_key, app_secret, user_id, creator_id, creator_name, create_time, modifier_id,
                                       modifier_name, modified_time)
        values (#{appKey},#{appSecret},#{userId},#{creatorId},#{creatorName},now(),#{modifierId},#{modifierName},now())
    </insert>

    <select id="getByAppKey" resultMap="resultMap" resultType="com.senox.user.vo.AuthCredentialsVo">
        select uac.app_key,
               uac.app_secret,
               uau.id as user_id,
               uau.username,
               uau.real_name,
               uau.is_loginable as loginable
        from u_auth_credentials uac
                 inner join u_admin_user uau on uac.user_id = uau.id
        <where>
            and uau.is_disabled = false
            and uau.is_loginable = true
            and uac.app_key = #{appKey}
        </where>

    </select>

    <select id="list" resultMap="resultMap" resultType="com.senox.user.vo.AuthCredentialsVo">
        select ac.app_key,
               ac.app_secret,
               au.id as user_id,
               au.username,
               au.real_name
        from u_auth_credentials ac
                 inner join u_admin_user au on au.id = ac.user_id
    </select>

    <select id="countList" resultType="int">
        select count(ac.app_key)
        from u_auth_credentials ac
                 inner join u_admin_user au on au.id = ac.user_id
    </select>
</mapper>
