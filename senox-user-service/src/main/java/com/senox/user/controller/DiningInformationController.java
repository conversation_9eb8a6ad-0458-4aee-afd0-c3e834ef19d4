package com.senox.user.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.vo.PageResult;
import com.senox.user.convert.DiningInformationConvertor;
import com.senox.user.domain.DiningInformation;
import com.senox.user.service.DiningInformationService;
import com.senox.user.vo.DiningInformationSearchVo;
import com.senox.user.vo.DiningInformationVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "报餐管理")
@RestController
@RequestMapping("/diningInformation")
public class DiningInformationController extends BaseController {

    @Autowired
    private DiningInformationService diningInformationService;

    @Autowired
    private DiningInformationConvertor convertor;

    @ApiOperation("批量添加报餐")
    @PostMapping("/addBatch")
    public void addBatchDiningInformation(@RequestBody List<DiningInformationVo> diningInformationVoList) {
        List<DiningInformation> list = convertor.toDo(diningInformationVoList);
        for (DiningInformation item : list) {
            initEntityCreator(item);
            initEntityModifier(item);
        }
        diningInformationService.saveDiningInformationBatch(list);
    }

    @ApiOperation("添加报餐")
    @PostMapping("/add")
    public void addDiningInformation(@RequestBody DiningInformationVo diningInformationVo) {
        DiningInformation diningInformation = convertor.toDo(diningInformationVo);
        initEntityCreator(diningInformation);
        initEntityModifier(diningInformation);
        diningInformationService.saveDiningInformation(diningInformation);
    }

    @ApiOperation("更新报餐数据")
    @PostMapping("/update")
    public void updateDiningInformation(@RequestBody DiningInformationVo diningInformationVo) {
        DiningInformation diningInformation = convertor.toDo(diningInformationVo);
        initEntityModifier(diningInformation);
        diningInformationService.updateDiningInformation(diningInformation);
    }

    @ApiOperation("根据id获取报餐信息")
    @GetMapping("/get/{id}")
    public DiningInformationVo getDiningInformation(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        DiningInformationVo informationVo = diningInformationService.findById(id);
        return informationVo == null ? null : informationVo;
    }

    @ApiOperation("删除报餐数据")
    @PostMapping("/delete/{id}")
    public void deleteDiningInformation(@PathVariable Long id) {
        diningInformationService.deleteDiningInformation(id);
    }

    @ApiOperation("查询报餐数据列表")
    @PostMapping("/list")
    public PageResult<DiningInformationVo> listDiningInformation(@RequestBody DiningInformationSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return diningInformationService.informationList(search);
    }


}
