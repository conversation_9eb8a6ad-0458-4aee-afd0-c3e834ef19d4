package com.senox.user.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.DateUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.utils.StringUtils;
import com.senox.user.constant.UserConst;
import com.senox.user.domain.Activity;
import com.senox.user.domain.VoteRecords;
import com.senox.user.mapper.VoteRecordsMapper;
import com.senox.user.utils.ContextUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/10/16 15:19
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class VoteRecordsService extends ServiceImpl<VoteRecordsMapper, VoteRecords> {

    private final ActivityService activityService;
    private final VoteResourcesService resourcesService;

    /**
     * 新增投票记录
     * @param records
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveVoteRecords(VoteRecords records) {
        Activity activity = activityService.findById(records.getActivityId());
        activityService.checkStatus(activity);
        if (!StringUtils.isBlank(records.getOpenid())) {
            //用户可以次数限制
            Long val = usedNumbers(records.getOpenid(), records.getActivityId(), activityService.limitNumber(records.getActivityId()));
            log.info("{} ---【已投票次数】:{}", records.getOpenid(), val);
        }
        records.setCreateTime(LocalDateTime.now());
        records.setModifiedTime(LocalDateTime.now());
        ContextUtils.initEntityCreator(records);
        ContextUtils.initEntityModifier(records);
        if (save(records)) {
            resourcesService.incrNumbers(records.getResourcesId(), records.getNumbers());
        }
    }

    /**
     * 用户可用次数
     * @param activityId
     * @param openid
     * @return
     */
    public Integer availableNumbers(Long activityId, String openid) {
        String key = String.format(UserConst.Cache.VOTE_NUMBERS, activityId, DateUtils.formatDateTime(LocalDateTime.now(), DateUtils.PATTERN_COMPACT_DATE), openid);
        RedisUtils.incr(key, 1);
        Long val = RedisUtils.incr(key, -1);
        log.info("{}：用户投票次数加一再减一，次数：{}", openid, val);
        Integer limitNumber = activityService.limitNumber(activityId);
        return limitNumber - val.intValue();
    }

    /**
     * 用户已投次数
     * @param openid
     * @param activityId
     * @param voteLimitNum
     * @return
     */
    public Long usedNumbers(String openid, Long activityId, long voteLimitNum) {
        String key = String.format(UserConst.Cache.VOTE_NUMBERS, activityId, DateUtils.formatYearMonth(LocalDate.now(), DateUtils.PATTERN_COMPACT_DATE), openid);
        Long val = RedisUtils.incr(key, 1);
        log.info("{}：用户投票次数加一，次数：{}", openid, val);
        if (val > voteLimitNum) {
            log.info("{}：用户投票次数减一，次数：{}", openid, RedisUtils.incr(key, -1));
            throw new BusinessException("投票次数不够了！");
        }
        return val;
    }
}
