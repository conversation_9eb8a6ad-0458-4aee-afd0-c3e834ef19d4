<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.user.mapper.MerchantMapper">

    <resultMap id="merchantVoResultMap" type="com.senox.user.vo.MerchantVo">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="contact" column="contact"/>
        <result property="idcard" column="idcard"/>
        <result property="address" column="address"/>
        <result property="rcSerial" column="rc_serial"/>
        <result property="rcTaxHeader" column="rc_tax_header"/>
        <result property="bicycleAuth" column="bicycle_auth"/>
        <result property="duoduo" column="is_duoduo"/>
        <result property="dryAuth" column="dry_auth"/>
        <result property="bicycleChargesId" column="bicycle_charges_id"/>
        <result property="settlePeriod" column="settle_period" typeHandler="com.senox.user.handler.MerchantBillSettlePeriodTypeHandler"/>
    </resultMap>

    <!-- 商户合计 -->
    <select id="countMerchant" parameterType="com.senox.user.vo.MerchantSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM u_merchant m
        <where>
            <if test="ids != null and ids.size() > 0">
                AND m.id IN <foreach collection="ids" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="name != null and name != ''">
                AND m.name = #{name}
            </if>
            <if test="rcSerial != null and rcSerial != ''">
                AND m.rc_serial = #{rcSerial}
            </if>
            <if test="null != chargesId">
                AND m.bicycle_charges_id = #{chargesId}
            </if>
            <if test="rc != null">
                <choose>
                    <when test="rc">AND m.rc_serial != ''</when>
                    <otherwise>AND m.rc_serial = ''</otherwise>
                </choose>
            </if>
            <if test="duoduo != null">
                <choose>
                    <when test="duoduo">AND m.is_duoduo = 1</when>
                    <otherwise>AND m.is_duoduo = 0</otherwise>
                </choose>
            </if>
            <if test="bicycleAuth != null">
                <choose>
                    <when test="bicycleAuth">AND m.bicycle_auth = 1</when>
                    <otherwise>AND m.bicycle_auth = 0</otherwise>
                </choose>
            </if>
            <if test="dryAuth != null">
                <choose>
                    <when test="dryAuth">AND m.dry_auth = 1</when>
                    <otherwise>AND m.dry_auth = 0</otherwise>
                </choose>
            </if>
            <if test="keyword != null">
                AND (m.name LIKE CONCAT('%', #{keyword}, '%') OR m.contact LIKE CONCAT('%', #{keyword}, '%') OR m.idcard LIKE CONCAT('%', #{keyword}, '%') OR m.rc_serial LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="settlePeriod != null">
                AND m.settle_period = #{settlePeriod}
            </if>
            AND m.is_disabled = 0
        </where>
    </select>

    <!-- 商户列表 -->
    <select id="listMerchant" parameterType="com.senox.user.vo.MerchantSearchVo" resultType="com.senox.user.domain.Merchant">
        SELECT m.id, m.name, m.contact, m.idcard, m.address, m.rc_serial, m.rc_tax_header, m.bicycle_auth, m.is_duoduo as duoduo, m.dry_auth, m.referral_code, m.bicycle_charges_id, m.settle_period
        FROM u_merchant m
        <where>
            <if test="ids != null and ids.size() > 0">
                AND m.id IN <foreach collection="ids" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="name != null and name != ''">
                AND m.name = #{name}
            </if>
            <if test="rcSerial != null and rcSerial != ''">
                AND m.rc_serial = #{rcSerial}
            </if>
            <if test="null != chargesId">
                AND m.bicycle_charges_id = #{chargesId}
            </if>
            <if test="rc != null">
                <choose>
                    <when test="rc">AND m.rc_serial != ''</when>
                    <otherwise>AND m.rc_serial = ''</otherwise>
                </choose>
            </if>
            <if test="duoduo != null">
                <choose>
                    <when test="duoduo">AND m.is_duoduo = 1</when>
                    <otherwise>AND m.is_duoduo = 0</otherwise>
                </choose>
            </if>
            <if test="bicycleAuth != null">
                <choose>
                    <when test="bicycleAuth">AND m.bicycle_auth = 1</when>
                    <otherwise>AND m.bicycle_auth = 0</otherwise>
                </choose>
            </if>
            <if test="dryAuth != null">
                <choose>
                    <when test="dryAuth">AND m.dry_auth = 1</when>
                    <otherwise>AND m.dry_auth = 0</otherwise>
                </choose>
            </if>
            <if test="keyword != null">
                AND (m.name LIKE CONCAT('%', #{keyword}, '%') OR m.contact LIKE CONCAT('%', #{keyword}, '%') OR m.idcard LIKE CONCAT('%', #{keyword}, '%') OR m.rc_serial LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="settlePeriod != null">
                AND m.settle_period = #{settlePeriod}
            </if>
            AND m.is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY m.create_time DESC
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

    <!-- 商户列表 -->
    <select id="listMerchantView" parameterType="com.senox.user.vo.MerchantSearchVo" resultType="com.senox.user.vo.MerchantVo" resultMap="merchantVoResultMap">
        SELECT m.id, m.name, m.contact, m.idcard, m.address, m.rc_serial, m.rc_tax_header, m.bicycle_auth, m.is_duoduo, m.dry_auth, m.referral_code, m.bicycle_charges_id, m.settle_period
        FROM u_merchant m
        <where>
            <if test="ids != null and ids.size() > 0">
                AND m.id IN <foreach collection="ids" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="name != null and name != ''">
                AND m.name = #{name}
            </if>
            <if test="rcSerial != null and rcSerial != ''">
                AND m.rc_serial = #{rcSerial}
            </if>
            <if test="rc != null">
                <choose>
                    <when test="rc">AND m.rc_serial != ''</when>
                    <otherwise>AND m.rc_serial = ''</otherwise>
                </choose>
            </if>
            <if test="duoduo != null">
                <choose>
                    <when test="duoduo">AND m.is_duoduo = 1</when>
                    <otherwise>AND m.is_duoduo = 0</otherwise>
                </choose>
            </if>
            <if test="bicycleAuth != null">
                <choose>
                    <when test="bicycleAuth">AND m.bicycle_auth = 1</when>
                    <otherwise>AND m.bicycle_auth = 0</otherwise>
                </choose>
            </if>
            <if test="dryAuth != null">
                <choose>
                    <when test="dryAuth">AND m.dry_auth = 1</when>
                    <otherwise>AND m.dry_auth = 0</otherwise>
                </choose>
            </if>
            <if test="keyword != null">
                AND (m.name LIKE CONCAT('%', #{keyword}, '%') OR m.contact LIKE CONCAT('%', #{keyword}, '%') OR m.idcard LIKE CONCAT('%', #{keyword}, '%') OR m.rc_serial LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="settlePeriod != null">
                AND m.settle_period = #{settlePeriod}
            </if>
            AND m.is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY m.create_time DESC
            </otherwise>
        </choose>
    </select>

    <update id="updateChargesBatch">
         update u_merchant
         set bicycle_charges_id = #{chargesId},
             modified_time = now()
         <where>
             <choose>
                 <when test="fullData">
                     and bicycle_charges_id not in (#{chargesId})
                 </when>
                 <otherwise>
                    and id in
                    <foreach collection="ids" item="item" open="(" close=")" separator=",">#{item}</foreach>
                 </otherwise>
             </choose>
         </where>
    </update>
</mapper>
