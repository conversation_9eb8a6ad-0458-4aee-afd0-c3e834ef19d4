package com.senox.user.convert;

import com.senox.user.domain.MerchantAuthApply;
import com.senox.user.vo.MerchantAuthApplyEditVo;
import com.senox.user.vo.MerchantAuthApplyVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023-11-1
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface MerchantApplyConvert {


    /**
     * editVo to domain
     *
     * @param editVo vo
     * @return do domain
     */
    MerchantAuthApply toDo(MerchantAuthApplyEditVo editVo);

    /**
     * do to editVo
     *
     * @param domain do
     * @return vo
     */
    MerchantAuthApplyEditVo toEditVo(MerchantAuthApply domain);

    /**
     * do to vo
     * @param domain
     * @return
     */
    MerchantAuthApplyVo toVo(MerchantAuthApply domain);
}
