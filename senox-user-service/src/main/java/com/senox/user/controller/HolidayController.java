package com.senox.user.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.user.domain.Holiday;
import com.senox.user.service.HolidayService;
import com.senox.user.vo.HolidaySearchVo;
import com.senox.user.vo.HolidayVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 假期接口
 * <AUTHOR>
 * @date 2021/4/19 10:23
 */
@Api(tags = "假期")
@RestController
@RequestMapping("/holiday")
public class HolidayController extends BaseController {

    @Autowired
    private HolidayService holidayService;

    @ApiOperation("新增假期")
    @PostMapping("/save")
    public void saveHolidays(@RequestBody List<HolidayVo> holidays) {
        if (CollectionUtils.isEmpty(holidays)) {
            throw new InvalidParameterException("无效的参数");
        }
        if (holidays.stream().anyMatch(x -> x.getHoliday() == null)) {
            throw new InvalidParameterException("无效的参数，假期日期不能为空");
        }
        if (holidays.stream().map(HolidayVo::getHoliday).distinct().count() < holidays.size()) {
            throw new InvalidParameterException("无效的参数，假期日期不能重复");
        }

        List<Holiday> list = holidays.stream().map(this::holidayVo2Entity).collect(Collectors.toList());
        holidayService.batchSaveHoliday(list);
    }

    @ApiOperation("删除假期")
    @PostMapping("/delete")
    public void deleteHolidays(@RequestBody List<LocalDate> dateList) {
        if (CollectionUtils.isEmpty(dateList)) {
            throw new InvalidParameterException("无效的日期");
        }
        holidayService.batchDelHoliday(dateList);
    }

    @ApiOperation("假期列表")
    @PostMapping("/list")
    public List<HolidayVo> listHolidays(@RequestBody HolidaySearchVo searchVo) {
        LocalDate startDate = searchVo.getStartDate();
        LocalDate endDate = searchVo.getEndDate();
        if (startDate == null || endDate == null) {
            throw new InvalidParameterException("无效的参数");
        }
        if (startDate.isAfter(endDate)) {
            return Collections.emptyList();
        }
        List<Holiday> resultList = holidayService.listHoliday(startDate, endDate);
        return resultList == null ? Collections.emptyList()
                : resultList.stream().map(this::holiday2Vo).collect(Collectors.toList());
    }

    /**
     * 假期视图对象转实体对象
     * @param vo
     * @return
     */
    private Holiday holidayVo2Entity(HolidayVo vo) {
        Holiday result = new Holiday();
        BeanUtils.copyProperties(vo, result);
        initEntityCreator(result);
        initEntityModifier(result);
        return result;
    }

    /**
     * 假期实体对象转视图对象
     * @param holiday
     * @return
     */
    private HolidayVo holiday2Vo(Holiday holiday) {
        HolidayVo result = new HolidayVo();
        BeanUtils.copyProperties(holiday, result);
        return result;
    }
}
