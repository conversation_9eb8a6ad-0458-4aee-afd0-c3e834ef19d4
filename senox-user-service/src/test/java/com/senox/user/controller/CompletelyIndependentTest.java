package com.senox.user.controller;

import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 完全独立的测试类
 * 
 * 特点：
 * 1. 不继承任何基类（不继承BaseTest、BaseControllerTest）
 * 2. 不使用@SpringBootTest注解
 * 3. 不使用任何Spring相关注解
 * 4. 不注入任何Bean
 * 5. 纯粹的JUnit测试
 * 
 * 预期结果：
 * - 不应该启动Spring容器
 * - 不应该加载Spring配置
 * - 不应该触发Redis容器启动
 * - 应该看到TestcontainersManager类加载日志，但不应该看到容器启动日志
 */
public class CompletelyIndependentTest {

    private static final Logger logger = LoggerFactory.getLogger(CompletelyIndependentTest.class);

    @Test
    public void testPureJavaLogic() {
        logger.info("=== 完全独立测试开始 ===");
        logger.info("这是一个纯Java测试，不使用Spring，不使用Redis");
        
        // 纯Java逻辑测试
        String message = "Hello World";
        int length = message.length();
        
        logger.info("测试字符串: {}", message);
        logger.info("字符串长度: {}", length);
        
        // 断言
        assert message != null : "消息不能为空";
        assert length == 11 : "长度应该是11";
        
        logger.info("纯Java逻辑测试通过");
        logger.info("=== 完全独立测试结束 ===");
    }

    @Test
    public void testMathOperations() {
        logger.info("=== 数学运算测试开始 ===");
        
        // 数学运算测试
        int a = 10;
        int b = 20;
        int sum = a + b;
        int product = a * b;
        
        logger.info("计算: {} + {} = {}", a, b, sum);
        logger.info("计算: {} * {} = {}", a, b, product);
        
        // 断言
        assert sum == 30 : "加法计算错误";
        assert product == 200 : "乘法计算错误";
        
        logger.info("数学运算测试通过");
        logger.info("=== 数学运算测试结束 ===");
    }

    @Test
    public void testStringOperations() {
        logger.info("=== 字符串操作测试开始 ===");
        
        // 字符串操作测试
        String original = "TestContainers";
        String lowercase = original.toLowerCase();
        String uppercase = original.toUpperCase();
        boolean contains = original.contains("Container");
        
        logger.info("原始字符串: {}", original);
        logger.info("小写: {}", lowercase);
        logger.info("大写: {}", uppercase);
        logger.info("包含'Container': {}", contains);
        
        // 断言
        assert "testcontainers".equals(lowercase) : "小写转换错误";
        assert "TESTCONTAINERS".equals(uppercase) : "大写转换错误";
        assert contains : "应该包含'Container'";
        
        logger.info("字符串操作测试通过");
        logger.info("=== 字符串操作测试结束 ===");
    }

    @Test
    public void testArrayOperations() {
        logger.info("=== 数组操作测试开始 ===");
        
        // 数组操作测试
        int[] numbers = {1, 2, 3, 4, 5};
        int sum = 0;
        for (int num : numbers) {
            sum += num;
        }
        
        logger.info("数组: {}", java.util.Arrays.toString(numbers));
        logger.info("数组长度: {}", numbers.length);
        logger.info("数组元素总和: {}", sum);
        
        // 断言
        assert numbers.length == 5 : "数组长度应该是5";
        assert sum == 15 : "数组元素总和应该是15";
        
        logger.info("数组操作测试通过");
        logger.info("=== 数组操作测试结束 ===");
    }

    @Test
    public void testExceptionHandling() {
        logger.info("=== 异常处理测试开始 ===");
        
        try {
            // 故意触发异常
            int result = 10 / 0;
            logger.error("不应该执行到这里");
            assert false : "应该抛出异常";
        } catch (ArithmeticException e) {
            logger.info("成功捕获预期的异常: {}", e.getMessage());
            assert "/ by zero".equals(e.getMessage()) : "异常消息不正确";
        }
        
        logger.info("异常处理测试通过");
        logger.info("=== 异常处理测试结束 ===");
    }

    @Test
    public void testPerformance() {
        logger.info("=== 性能测试开始 ===");
        
        long startTime = System.currentTimeMillis();
        
        // 简单的性能测试
        int iterations = 1000000;
        long sum = 0;
        for (int i = 0; i < iterations; i++) {
            sum += i;
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        logger.info("执行{}次循环", iterations);
        logger.info("计算结果: {}", sum);
        logger.info("耗时: {}ms", duration);
        
        // 断言
        assert sum > 0 : "计算结果应该大于0";
        assert duration >= 0 : "耗时应该大于等于0";
        
        logger.info("性能测试通过");
        logger.info("=== 性能测试结束 ===");
    }
}
