package com.senox.user.service;

import com.senox.user.BaseTest;
import com.senox.user.domain.Profession;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/12/31 15:57
 */
public class ProfessionServiceTest extends BaseTest {

    @Autowired
    private ProfessionService professionService;

    @Test
    public void testAddAndUpdate() {
        Profession profession1 = mockProfession();
        Long result1 = professionService.addProfession(profession1);

        Profession profession2 = professionService.getProfession(result1);
        Assertions.assertEquals(profession1, profession2);

        Profession profession3 = new Profession();
        profession3.setId(result1);
        profession3.setName(randStr(3));
        profession3.setModifierId(randLong(1, 100));
        profession3.setModifierName(randStr(10));
        Assertions.assertTrue(professionService.updateProfession(profession3));

        Profession profession4 = professionService.getProfession(result1);
        Assertions.assertEquals(profession3, profession4);
    }

    @Test
    public void testListAll() {
        for (int i = 0; i < 30; i++) {
            Profession profession = mockProfession();
            professionService.addProfession(profession);
        }

        List<Profession> list1 = professionService.listAll();
        Assertions.assertTrue(list1.size() >= 30);
    }

    private Profession mockProfession() {
        Profession result = new Profession();
        result.setName(randStr(20));
        result.setCreatorId(randLong(1, 100));
        result.setCreatorName(randStr(20));
        result.setModifierId(randLong(1, 100));
        result.setModifierName(randStr(20));
        return result;
    }
}
