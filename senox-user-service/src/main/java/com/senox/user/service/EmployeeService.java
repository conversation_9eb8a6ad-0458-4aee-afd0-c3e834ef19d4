package com.senox.user.service;

import com.senox.common.utils.PageUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.common.vo.PageResult;
import com.senox.user.constant.DelegateType;
import com.senox.user.constant.UserConst;
import com.senox.user.domain.Company;
import com.senox.user.domain.Employee;
import com.senox.user.domain.EmployeeMealDelegate;
import com.senox.user.mapper.EmployeeMapper;
import com.senox.user.mapper.EmployeeMealDelegateMapper;
import com.senox.user.vo.EmployeeSearchVo;
import com.senox.user.vo.EmployeeVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2021/4/1 13:37
 */
@Service
public class EmployeeService {

    @Autowired
    private EmployeeMapper employeeMapper;
    @Autowired
    private EmployeeMealDelegateMapper delegateMapper;
    @Autowired
    private CompanyService companyService;

    /**
     * 添加员工信息
     * @param employee
     * @param delegateEmployees
     * @param delegateCompany
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addEmployee(Employee employee, List<Long> delegateEmployees, Long delegateCompany) {
        if (employee.getCanteenMaster() == null) {
            employee.setCanteenMaster(Boolean.FALSE);
        }
        if (employee.getMpOpenid() == null) {
            employee.setMpOpenid(StringUtils.EMPTY);
        }
        if (employee.getDefaultBooked() == null) {
            employee.setDefaultBooked(0);
        }

        // 保存员工信息
        int result = employeeMapper.addEmployee(employee);

        // 保存代理关系
        if (result > 0) {
            // 员工代理
            if (!CollectionUtils.isEmpty(delegateEmployees)) {
                List<EmployeeMealDelegate> list = delegateEmployees.stream()
                        .map(x -> this.newMealDelegate(employee, x, DelegateType.EMPLOYEE))
                        .collect(Collectors.toList());
                delegateMapper.batchAddDelegate(list);
            }

            // 公司代理
            if (WrapperClassUtils.biggerThanLong(delegateCompany, 0L)) {
                delegateMapper.batchAddDelegate(Collections.singletonList(newMealDelegate(employee, delegateCompany, DelegateType.COMPANY)));
            }
        }
        return result > 0 ? employee.getId() : 0L;
    }

    /**
     * 更新员工信息
     * @param employee
     * @param delegateEmployees
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateEmployee(Employee employee, List<Long> delegateEmployees, Long delegateCompany) {
        if (!WrapperClassUtils.biggerThanLong(employee.getId(), 0L)) {
            return false;
        }

        EmployeeVo dbEmployee = findById(employee.getId());
        if (dbEmployee == null) {
            return false;
        }
        // 删除员工
        if (employee.getDisabled() != null && employee.getDisabled()) {
            employee.setUsername(dbEmployee.getUsername() + "_" + employee.getId());
        }

        // 更新员工信息
        boolean result = employeeMapper.updateEmployee(employee) > 0;

        // 更新代理人信息
        if (result && delegateEmployees != null) {
            List<EmployeeMealDelegate> srcList = listDelegateRelations(employee.getId());
            // 员工代理
            List<EmployeeMealDelegate> targetList = delegateEmployees.stream()
                    .map(x -> this.newMealDelegate(employee, x, DelegateType.EMPLOYEE))
                    .collect(Collectors.toList());
            // 企业代理
            if (WrapperClassUtils.biggerThanLong(delegateCompany, 0L)) {
                EmployeeMealDelegate companyDelegate = newMealDelegate(employee, delegateCompany, DelegateType.COMPANY);
                targetList.add(companyDelegate);
            }

            // 比较结果集，得到新增及删除的数据
            DataSepDto<EmployeeMealDelegate> sepData = sepMealDelegate(srcList, targetList);
            if (!CollectionUtils.isEmpty(sepData.getAddList())) {
                delegateMapper.batchAddDelegate(sepData.getAddList());
            }
            if (!CollectionUtils.isEmpty(sepData.getRemoveList())) {
                List<Long> ids = sepData.getRemoveList().stream().map(EmployeeMealDelegate::getId).collect(Collectors.toList());
                delegateMapper.batchDeleteDelegator(ids);
            }
        }

        // clear cache
        if (!StringUtils.isBlank(dbEmployee.getMpOpenid())) {
            RedisUtils.del(String.format(UserConst.Cache.KEY_BIND_EMPLOYEE, dbEmployee.getMpOpenid()));
        }
        return result;
    }

    /**
     * 根据id查找员工信息
     * @param id
     * @return
     */
    public EmployeeVo findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return employeeMapper.findById(id);
    }

    public Employee findByName(String name) {
        return StringUtils.isBlank(name) ? null : employeeMapper.findByName(name);
    }

    /**
     * 查找代理企业的员工
     * @param delegateCompany
     * @return
     */
    public Employee findDelegateCompanyEmployee(Long delegateCompany) {
        if (!WrapperClassUtils.biggerThanLong(delegateCompany, 0L)) {
            return null;
        }
        return employeeMapper.findDelegateCompanyEmployee(delegateCompany);
    }

    /**
     * 员工列表
     * @param search
     * @return
     */
    public List<EmployeeVo> listEmployee(EmployeeSearchVo search) {
        return employeeMapper.listEmployee(search);
    }

    /**
     * 员工列表页
     * @param search
     * @return
     */
    public PageResult<EmployeeVo> listEmployeePage(EmployeeSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }

        // 总记录数
        int totalSize = employeeMapper.countEmployee(search);
        search.prepare();

        if (totalSize <= search.getOffset()) {
            return PageResult.emptyPage();
        }

        // list
        List<EmployeeVo> resultList = listEmployee(search);
        return PageUtils.resultPage(search, totalSize, resultList);
    }

    /**
     * 加载尚未报餐的员工列表
     * @param mealDate
     * @param offset
     * @param rows
     * @return
     */
    public List<Employee> listNoBookedEmployee(LocalDate mealDate, int offset, int rows) {
        if (mealDate == null || rows < 1) {
            return Collections.emptyList();
        }

        offset = Math.max(offset, 0);

        // 排除代理的企业
        List<Company> delegateCompanyList = companyService.listDelegateCompany();
        List<String> excludeCompanyList = CollectionUtils.isEmpty(delegateCompanyList) ? null
                : delegateCompanyList.stream().map(Company::getCompanyName).collect(Collectors.toList());
        return employeeMapper.listNoBookedEmployee(mealDate, excludeCompanyList, offset, rows);
    }

    /**
     * 代理列表
     * @param employeeId
     * @return
     */
    public List<EmployeeMealDelegate> listDelegateRelations(Long employeeId) {
        if (!WrapperClassUtils.biggerThanLong(employeeId, 0L)) {
            return Collections.emptyList();
        }
        return delegateMapper.listByEmployee(employeeId);
    }

    /**
     * 根据代理id查找被代理人信息
     * @param employeeId
     * @return
     */
    public List<Employee> listDelegateEmployee(Long employeeId) {
        if (!WrapperClassUtils.biggerThanLong(employeeId, 0L)) {
            return Collections.emptyList();
        }
        return delegateMapper.listDelegateEmployee(employeeId);
    }

    /**
     * 代理公司信息
     * @param employeeId
     * @return
     */
    public Company findDelegateCompany(Long employeeId) {
        if (!WrapperClassUtils.biggerThanLong(employeeId, 0L)) {
            return null;
        }
        return delegateMapper.findDelegateCompany(employeeId);
    }

    /**
     * 代理对象转换
     * @param employee
     * @param delegateEmployee
     * @param delegateType
     * @return
     */
    private EmployeeMealDelegate newMealDelegate(Employee employee, Long delegateEmployee, DelegateType delegateType) {
        EmployeeMealDelegate result = new EmployeeMealDelegate();
        result.setEmployeeId(employee.getId());
        result.setDelegateId(delegateEmployee);
        result.setType(delegateType.getValue());
        result.setCreatorId(employee.getModifierId());
        result.setCreatorName(employee.getModifierName());
        result.setModifierId(employee.getModifierId());
        result.setModifierName(employee.getModifierName());
        return result;
    }

    /**
     * 比较代理关系列表，计算添加的记录与删除的记录
     * @param srcList
     * @param targetList
     * @return
     */
    private DataSepDto<EmployeeMealDelegate> sepMealDelegate(List<EmployeeMealDelegate> srcList, List<EmployeeMealDelegate> targetList) {
        DataSepDto<EmployeeMealDelegate> result = new DataSepDto<>();
        if (CollectionUtils.isEmpty(srcList)) {
            result.setAddList(targetList);
        } else if (CollectionUtils.isEmpty(targetList)) {
            result.setRemoveList(srcList);
        } else {
            List<EmployeeMealDelegate> addList = targetList.stream().filter(x -> !srcList.contains(x)).collect(Collectors.toList());
            List<EmployeeMealDelegate> removeList = srcList.stream().filter(x -> !targetList.contains(x)).collect(Collectors.toList());
            result.setAddList(addList);
            result.setRemoveList(removeList);
        }
        return result;
    }
}
