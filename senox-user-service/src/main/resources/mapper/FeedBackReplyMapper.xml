<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.user.mapper.FeedBackReplyMapper">

    <resultMap id="FeedBackReply_Result" type="com.senox.user.vo.FeedBackReplyVo">
        <result property="id" column="id"/>
        <result property="openid" column="openid"/>
        <result property="title" column="title"/>
        <result property="feedBackId" column="feed_back_id"/>
        <result property="content" column="content"/>
        <result property="name" column="name"/>
        <result property="parentId" column="parent_id"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <select id="findFeedBackReplyById" parameterType="long" resultMap="FeedBackReply_Result">
        select br.id, br.openid, b.title, br.feed_back_id, br.content, br.name, br.parent_id, br.create_time
        from u_feed_back_reply br INNER JOIN u_feed_back b ON br.feed_back_id = b.id
        where br.id = #{id}
    </select>

</mapper>