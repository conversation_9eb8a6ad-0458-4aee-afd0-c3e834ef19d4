package com.senox.user.component;

import com.senox.common.utils.FeignUtils;
import com.senox.tms.api.clients.BicycleChargesClient;
import com.senox.tms.vo.BicycleChargesVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/6/7 13:50
 */
@Component
@RequiredArgsConstructor
public class BicycleChargesComponent {

    private final BicycleChargesClient bicycleChargesClient;

    /**
     * 获取当前默认收费标准
     * @return
     */
    public BicycleChargesVo getCurrentEffectiveCharges() {
        try {
            return bicycleChargesClient.getCurrentEffectiveCharges();
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }
}
