package com.senox.user.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.domain.VoteCategory;
import com.senox.user.mapper.VoteCategoryMapper;
import com.senox.user.vo.VoteCategorySearchVo;
import com.senox.user.vo.VoteCategoryVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/16 10:08
 */
@Service
@Slf4j
public class VoteCategoryService extends ServiceImpl<VoteCategoryMapper, VoteCategory> {

    /**
     * 添加投票类别
     * @param category
     */
    public void addVoteCategory(VoteCategory category) {
        category.setModifiedTime(LocalDateTime.now());
        save(category);
    }

    /**
     * 更新投票类别
     * @param category
     */
    public void updateVoteCategory(VoteCategory category) {
        category.setModifiedTime(LocalDateTime.now());
        updateById(category);
    }

    /**
     * 保存投票类别
     * @param category
     */
    public void saveVoteCategory(VoteCategory category) {
        if (!WrapperClassUtils.biggerThanLong(category.getId(), 0L)) {
            addVoteCategory(category);
        } else {
            updateVoteCategory(category);
        }
    }

    /**
     * 根据id获取投票类别
     * @param id
     * @return
     */
    public VoteCategory findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return getById(id);
    }

    /**
     * 根据id删除投票类别
     * @param id
     */
    public void deleteVoteCategoryById(Long id) {
        VoteCategory category = findById(id);
        if (category == null){
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        category.setName(category.getName().concat("_").concat(category.getId().toString()));
        category.setModifiedTime(LocalDateTime.now());
        category.setDisabled(Boolean.TRUE);
        updateById(category);
    }

    /**
     * 统计数量
     * @param searchVo
     * @return
     */
    public int count(VoteCategorySearchVo searchVo) {
        return getBaseMapper().count(searchVo);
    }

    /**
     * 列表
     * @param searchVo
     * @return
     */
    public List<VoteCategoryVo> list(VoteCategorySearchVo searchVo) {
        return getBaseMapper().list(searchVo);
    }

    /**
     * 投票活动分页
     * @param searchVo
     * @return
     */
    public PageResult<VoteCategoryVo> page(VoteCategorySearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return PageUtils.commonPageResult(searchVo, () -> getBaseMapper().count(searchVo), () -> getBaseMapper().list(searchVo));
    }

}
