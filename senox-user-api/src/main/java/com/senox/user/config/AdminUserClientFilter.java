package com.senox.user.config;


import com.senox.context.AdminContext;
import com.senox.context.AdminUserDto;
import com.senox.user.component.AdminUserComponent;
import com.senox.user.vo.WxUserVo;
import feign.FeignException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2021/1/11 9:23
 */
public class AdminUserClientFilter implements Filter {

    private static final Logger logger = LoggerFactory.getLogger(AdminUserClientFilter.class);

    @Value("${senox.adminFilter.open:true}")
    private Boolean adminFilterOpen;
    @Value("#{'${senox.adminFilter.excludeUrls:}'.split(',')}")
    private List<String> excludeUrls;
    /**
     * 必须要过拦截器的url
     */
    @Value("${senox.adminFilter.wechatAdmin:0}")
    private Long wechatAdmin;
    @Value("#{'${senox.adminFilter.mustUrls:}'.split(',')}")
    private List<String> mustUrls;
    @Value("#{'${senox.adminFilter.tokens:}'.split(',')}")
    private List<String> ignoreTokens;

    @Autowired
    private AdminUserComponent adminUserComponent;

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain)
            throws IOException, ServletException {
        // 拦截器配置绕过部分url
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        String requestUri = request.getRequestURI();

        // 是否需要过adminFilter
        if (!Objects.equals(adminFilterOpen, Boolean.TRUE) && !isUrlMustFilter(requestUri)) {
            filterChain.doFilter(servletRequest, servletResponse);
            return;
        }
        if (!CollectionUtils.isEmpty(excludeUrls)){
            for (String excludeUrl : excludeUrls) {
                if (requestUri.contains(excludeUrl)) {
                    filterChain.doFilter(servletRequest, servletResponse);
                    return;
                }
            }
        }

        logger.info("header {} : {}", AdminContext.HEADER_AUTHORIZATION, request.getHeader(AdminContext.HEADER_AUTHORIZATION));
        logger.info("header {} : {}", AdminContext.HEADER_WECHAT_AUTH, request.getHeader(AdminContext.HEADER_WECHAT_AUTH));
        logger.info("header {} : {}", AdminContext.HEADER_WECHAT_MP, request.getHeader(AdminContext.HEADER_WECHAT_MP));
        logger.info("header {} : {}", AdminContext.HEADER_AUTH_CREDENTIALS, request.getHeader(AdminContext.HEADER_AUTH_CREDENTIALS));
        logger.info("header {} : {}", AdminContext.HEADER_ACCESS_TOKEN, request.getHeader(AdminContext.HEADER_ACCESS_TOKEN));
        logger.info("header {} : {}", AdminContext.HEADER_FEIGN_TOKEN, request.getHeader(AdminContext.HEADER_FEIGN_TOKEN));

        // token
        prepareContextUserFromToken(request.getHeader(AdminContext.HEADER_AUTHORIZATION));

        // wechat token
        prepareContextUserFromWechatToken(request.getHeader(AdminContext.HEADER_WECHAT_MP), request.getHeader(AdminContext.HEADER_WECHAT_AUTH));

        //auth credentials
        prepareContextUserFromCredentials(request.getHeader(AdminContext.HEADER_AUTH_CREDENTIALS));

        //auth token
        prepareContextUserFromAccessToken(request.getHeader(AdminContext.HEADER_ACCESS_TOKEN));

        //feign token
        prepareContextUserFromFeignToken(request.getHeader(AdminContext.HEADER_FEIGN_TOKEN));

        filterChain.doFilter(servletRequest, servletResponse);
    }

    /**
     * 链接是否必须过拦截器
     * @param url
     * @return
     */
    private boolean isUrlMustFilter(String url) {
        if (!CollectionUtils.isEmpty(mustUrls)) {
            for (String includeUrl : mustUrls) {
                if (includeUrl.contains(url)) {
                    return true;
                }
            }
        }
        return false;
    }


    /**
     * 设置上下文feignToken用户
     * @param feignToken
     */
    private void prepareContextUserFromFeignToken(String feignToken) {
        if (feignToken == null || feignToken.length() < 1) {
            return;
        }
        // 设置上下文对象
        try {
            logger.info("Invoke with feign token {}", feignToken);
            AdminUserDto adminUser = adminUserComponent.getUserFromFeignToken(feignToken);
            if (adminUser != null) {
                adminUser.setFeignToken(feignToken);
            }
            AdminContext.setUser(adminUser);
        } catch (FeignException e) {
            logger.error("校验用户feignToken异常 {}", e.getMessage());
        }
    }

    /**
     * 设置上下文token用户
     * @param token
     */
    private void prepareContextUserFromToken(String token) {
        if (token == null || token.length() < 1) {
            return;
        }

        // ignore checking
        if (ignoreTokens != null && ignoreTokens.contains(token)) {
            logger.info("Invoke with ignore token {}", token);
            AdminContext.setUser(mockFakeAdmin(token, false));
            return;
        }

        // 设置上下文对象
        try {
            logger.info("Invoke with token {}", token);
            AdminUserDto adminUser = adminUserComponent.getUserFromToken(token);
            if (adminUser != null) {
                adminUser.setToken(token);
            }
            AdminContext.setUser(adminUser);
        } catch (FeignException e) {
            logger.error("校验用户token异常 {}", e.getMessage());
        }
    }

    /**
     * 设置上下文微信token用户
     * @param token
     */
    private void prepareContextUserFromWechatToken(String appId, String token) {
        if (token == null || token.length() < 1) {
            return;
        }

        // 设置上下文对象
        try {
            logger.info("invoke with wechat appid {}, token {}", appId, token);
            AdminUserDto adminUser = adminUserComponent.getUserFromWechatToken(appId, token);
            if (adminUser != null) {
                logger.info("Admin user from wechat: {}", adminUser);
                adminUser.setMpAppId(appId);
                adminUser.setWechatToken(token);

            } else {
                // 是否普通微信用户
                WxUserVo wxUser = adminUserComponent.getWxUserFromWechatToken(appId, token);
                logger.info("normal wxuser: {}", wxUser);
                if (wxUser != null) {
                    adminUser = mockFakeAdmin(token, true);
                    adminUser.setMpAppId(appId);
                }
            }
            AdminContext.setUser(adminUser);
        } catch (FeignException e) {
            logger.error("校验用户token异常 {}", e.getMessage());
        }
    }

    /**
     * 设置上下文凭证
     * @param credentials 凭证
     */
    private void prepareContextUserFromCredentials(String credentials) {
        if (!StringUtils.hasLength(credentials)) {
            return;
        }
        try {
            logger.info("invoke with credentials {}", credentials);
            AdminUserDto adminUser = adminUserComponent.getUserFromAuthCredentials(credentials);
            if (null != adminUser) {
                adminUser.setAuthCredentials(credentials);
            }
            AdminContext.setUser(adminUser);
        } catch (FeignException e) {
            logger.error("校验凭证异常 {}", e.getMessage());
        }
    }

    private void prepareContextUserFromAccessToken(String accessToken) {
        if (!StringUtils.hasLength(accessToken)) {
            return;
        }
        try {
            logger.info("invoke with access token {}", accessToken);
            AdminUserDto adminUser = adminUserComponent.getUserFromAuthAccessToken(accessToken);
            if (null != adminUser) {
                adminUser.setAccessToken(accessToken);
            }
            AdminContext.setUser(adminUser);
        } catch (FeignException e) {
            logger.error("校验访问令牌异常 {}", e.getMessage());
        }
    }
    /**
     * 伪造登录用户
     * @param token
     * @return
     */
    private AdminUserDto mockFakeAdmin(String token, boolean isWechatUser) {
        AdminUserDto adminUserDto = new AdminUserDto();
        if (isWechatUser || isWechatInvoke(token)) {
            adminUserDto.setUserId(wechatAdmin);
            adminUserDto.setUsername("weixin");
            adminUserDto.setRealName("微信");
            adminUserDto.setWechatToken(token);
        } else {
            adminUserDto.setUserId(1L);
            adminUserDto.setUsername("MockAdmin");
            adminUserDto.setRealName("MockAdmin");
            adminUserDto.setToken(token);
        }
        adminUserDto.setWechatToken(token);
        return adminUserDto;
    }

    private boolean isWechatInvoke(String token) {
        String t = token.toLowerCase();
        return t.startsWith("w") && t.endsWith("x");
    }


}
