package com.senox.user.service;

import com.senox.common.vo.PageResult;
import com.senox.user.BaseTest;
import com.senox.user.constant.Covid19Category;
import com.senox.user.constant.Gender;
import com.senox.user.domain.Customer;
import com.senox.user.domain.CustomerCovid19;
import com.senox.user.domain.CustomerExt;
import com.senox.user.vo.CustomerSearchVo;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/12/31 10:19
 */
public class CustomerServiceTest extends BaseTest {

    private static final Logger logger = LoggerFactory.getLogger(CustomerServiceTest.class);

    @Autowired
    private CustomerService customerService;

    @Test
    public void testAddAndUpdate() {
        Customer customer1 = mockCustomer();
        CustomerExt ext1 = mockCustomerExt();
        List<CustomerCovid19> covid19List1 = Lists.newArrayList(
                mockCustomerCovid19(Covid19Category.NAT), mockCustomerCovid19(Covid19Category.VACCINE));
        Long customerId1 = customerService.addCustomer(customer1, ext1, covid19List1);

        Customer dbCustomer1 = customerService.findById(customerId1);
        CustomerExt dbExt1 = customerService.findExtByCustomerId(customerId1);
        List<CustomerCovid19> dbCovid19List1 = customerService.listCustomerCovid19(customerId1);
        Assertions.assertEquals(customer1, dbCustomer1);
        Assertions.assertEquals(ext1.getBankAccount(), dbExt1.getBankAccount());
        Assertions.assertEquals(ext1.getRemark(), dbExt1.getRemark());
        Assertions.assertEquals(ext1.getAvatar(), dbExt1.getAvatar());
        Assertions.assertEquals(covid19List1.size(), dbCovid19List1.size());
        Assertions.assertTrue(dbCovid19List1.stream().allMatch(x -> x.getCustomerId().equals(customerId1)));
        for (CustomerCovid19 item : dbCovid19List1) {
            Assertions.assertTrue(covid19List1.stream().anyMatch(x -> x.getCategory().equals(item.getCategory()) && x.getOperateDate().equals(item.getOperateDate())));
        }

        Customer customer2 = new Customer();
        customer2.setId(customerId1);
        customer2.setTelephone("15" + randNumStr(9));
        customer2.setNativePlace(randStr(10));
        customer2.setProvinceId(randLong(1L, 100L));
        customer2.setProvinceName(randStr(20));
        customer2.setCityId(randLong(1L, 100L));
        customer2.setCityName(randStr(20));
        customer2.setProfessionId(randLong(1L, 100L));
        customer2.setProfessionName(randStr(20));
        customer2.setModifierId(randLong(5, 200));
        customer2.setModifierName(randStr(30));

        CustomerExt ext2 = new CustomerExt();
        ext2.setBankAccount(randStr(20));
        ext2.setRemark(randStr(20));
        ext2.setAvatar(randStr(1000));

        List<CustomerCovid19> covid19List2 = Lists.newArrayList(
                mockCustomerCovid19(Covid19Category.NAT, LocalDate.now().minusDays(3)),
                mockCustomerCovid19(Covid19Category.NAT, LocalDate.now().minusDays(10)),
                mockCustomerCovid19(Covid19Category.VACCINE));
        Assertions.assertTrue(customerService.updateCustomer(customer2, ext2, covid19List2));

        Customer dbCustomer2 = customerService.findById(customerId1);
        CustomerExt dbExt2 = customerService.findExtByCustomerId(customerId1);
        List<CustomerCovid19> dbCovid19List2 = customerService.listCustomerCovid19(customerId1);
        Assertions.assertEquals(customer2.getTelephone(), dbCustomer2.getTelephone());
        Assertions.assertEquals(customer2.getNativePlace(), dbCustomer2.getNativePlace());
        Assertions.assertEquals(customer2.getProvinceId(), dbCustomer2.getProvinceId());
        Assertions.assertEquals(customer2.getProvinceName(), dbCustomer2.getProvinceName());
        Assertions.assertEquals(customer2.getCityId(), dbCustomer2.getCityId());
        Assertions.assertEquals(customer2.getCityName(), dbCustomer2.getCityName());
        Assertions.assertEquals(customer2.getProfessionId(), dbCustomer2.getProfessionId());
        Assertions.assertEquals(customer2.getProfessionName(), dbCustomer2.getProfessionName());
        Assertions.assertEquals(ext2.getBankAccount(), dbExt2.getBankAccount());
        Assertions.assertEquals(ext2.getRemark(), dbExt2.getRemark());
        Assertions.assertEquals(ext2.getAvatar(), dbExt2.getAvatar());
        Assertions.assertEquals(covid19List2.size(), dbCovid19List2.size());
        Assertions.assertTrue(covid19List2.stream().allMatch(x -> x.getCustomerId().equals(customerId1)));
        for (CustomerCovid19 item : dbCovid19List2) {
            Assertions.assertTrue(covid19List2.stream().anyMatch(x -> x.getCategory().equals(item.getCategory()) && x.getOperateDate().equals(item.getOperateDate())));
        }
    }

    @Test
    public void testListPage() {
        for (int i = 0; i < 30; i++) {
            Customer customer = mockCustomer();
            CustomerExt ext = mockCustomerExt();
            customerService.addCustomer(customer, ext, null);
        }

        CustomerSearchVo searchVo = new CustomerSearchVo();
        searchVo.setPageNo(2);
        searchVo.setPageSize(10);
        PageResult<Customer> pageResult = customerService.listCustomerPage(searchVo);
        logger.info("pageResult: {}", pageResult);
        Assertions.assertTrue(pageResult.getTotalPages() >= 3);
        Assertions.assertTrue(pageResult.getTotalSize() >= 30);
        Assertions.assertEquals(searchVo.getPageSize(), pageResult.getPageSize());
    }


    private Customer mockCustomer() {
        Customer result = new Customer();
        result.setName(randStr(20));
        result.setIdcard(randStr(18));
        result.setIdcardType(0);
        result.setGender(Gender.fromValue(randInt(1, Gender.values().length)).getValue());
        result.setNation(randStr(10));
        result.setBornDate(LocalDate.now().minusYears(randInt(18, 60)));
        result.setNativePlace(randStr(10));
        result.setAddress(randStr(100));
        result.setTelephone("13" + randNumStr(9));
        result.setEmail(randStr(10) + "@" + randStr(4) + ".com");
        result.setProvinceId(randLong(1L, 100L));
        result.setProvinceName(randStr(20));
        result.setCityId(randLong(1L, 100L));
        result.setCityName(randStr(20));
        result.setWorkplaceRegionId(randLong(1, 100));
        result.setWorkplaceRegionName(randStr(20));
        result.setWorkplaceStreetId(randLong(1, 100));
        result.setWorkplaceStreetName(randStr(20));
        result.setWorkplaceAddress(randStr(20));
        result.setProfessionId(randLong(1L, 100L));
        result.setProfessionName(randStr(20));
        result.setJobTitle(randStr(20));
        result.setNatTested(Boolean.TRUE);
        result.setCovid19Vaccination(Boolean.TRUE);
        result.setCreatorId(randLong(1, 100));
        result.setCreatorName(randStr(20));
        result.setModifierId(randLong(1, 100));
        result.setModifierName(randStr(20));
        return result;
    }

    private CustomerExt mockCustomerExt() {
        CustomerExt ext = new CustomerExt();
        ext.setBankAccount(randStr(12));
        ext.setRemark(randStr(20));
        ext.setAvatar(randStr(2000));
        return ext;
    }

    private CustomerCovid19 mockCustomerCovid19(Covid19Category category) {
        return mockCustomerCovid19(category, LocalDate.now());
    }

    private CustomerCovid19 mockCustomerCovid19(Covid19Category category, LocalDate date) {
        CustomerCovid19 result = new CustomerCovid19();
        result.setCategory(category.getValue());
        result.setOperateDate(date);
        return result;
    }
}
