<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.user.mapper.BookingMealMapper">

    <resultMap id="Result_BookingMeal" type="com.senox.user.domain.BookingMeal">
        <id property="id" jdbcType="BIGINT" column="id" />
        <result property="mpid" jdbcType="VARCHAR" column="mpid" />
        <result property="openid" jdbcType="VARCHAR" column="openid" />
        <result property="company" jdbcType="VARCHAR" column="company" />
        <result property="employee" jdbcType="VARCHAR" column="employee" />
        <result property="delegateCompany" jdbcType="BIGINT" column="delegate_company" />
        <result property="mealDate" jdbcType="TIMESTAMP" column="meal_date" />
        <result property="mealBook" jdbcType="INTEGER" column="meal_book" />
        <result property="createTime" jdbcType="TIMESTAMP" column="create_time" />
        <result property="modifiedTime" jdbcType="TIMESTAMP" column="modified_time" />
    </resultMap>

    <resultMap id="Result_BookingMealCompanyDayReport" type="com.senox.user.domain.BookingMealCompanyDayReport">
        <id property="id" jdbcType="BIGINT" column="id" />
        <result property="mealDate" jdbcType="TIMESTAMP" column="meal_date" />
        <result property="company" jdbcType="VARCHAR" column="company" />
        <result property="bookedCount" jdbcType="INTEGER" column="booked_count" />
        <result property="unbookedCount" jdbcType="INTEGER" column="unbooked_count" />
        <result property="total" jdbcType="TINYINT" column="is_total" />
    </resultMap>

    <!-- 统计订餐数 -->
    <select id="countBooking" parameterType="com.senox.user.vo.BookingMealSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(id) FROM wx_booking_meal
        <where>
            <if test="company != null and company != ''">
                AND company = #{company}
            </if>
            <if test="employee != null and employee != ''">
                AND employee = #{employee}
            </if>
            <if test="bookingDateStart != null">
                AND meal_date >= #{bookingDateStart}
            </if>
            <if test="bookingDateEnd != null">
                AND meal_date <![CDATA[<=]]> #{bookingDateEnd}
            </if>
            <if test="mealBook != null">
                <choose>
                    <when test="mealBook">
                        AND meal_book > 0
                    </when>
                    <otherwise>
                        AND meal_book = 0
                    </otherwise>
                </choose>
            </if>
        </where>
    </select>

    <!-- 订餐列表 -->
    <select id="listBooking" parameterType="com.senox.user.vo.BookingMealSearchVo" resultMap="Result_BookingMeal">
        SELECT id, mpid, openid, company, employee, delegate_company, meal_date, meal_book, modified_time
        FROM wx_booking_meal
        <where>
            <if test="company != null and company != ''">
                AND company = #{company}
            </if>
            <if test="employee != null and employee != ''">
                AND employee = #{employee}
            </if>
            <if test="bookingDateStart != null">
                AND meal_date >= #{bookingDateStart}
            </if>
            <if test="bookingDateEnd != null">
                AND meal_date <![CDATA[<=]]> #{bookingDateEnd}
            </if>
            <if test="mealBook != null">
                <choose>
                    <when test="mealBook">
                        AND meal_book > 0
                    </when>
                    <otherwise>
                        AND meal_book = 0
                    </otherwise>
                </choose>
            </if>
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}, id
            </when>
            <otherwise>
                ORDER BY meal_date DESC, id
            </otherwise>
        </choose>
        LIMIT ${offset}, ${pageSize}
    </select>

    <!-- 代公司报餐 -->
    <select id="listDelegateBooking" parameterType="com.senox.user.vo.BookingMealDayReportSearchVo" resultMap="Result_BookingMealCompanyDayReport">
        SELECT m.id, m.meal_date, c.company_name AS company, meal_book AS booked_count, 0 AS unbooked_count, 0 AS is_total
        FROM wx_booking_meal m
            LEFT JOIN u_company c ON m.delegate_company = c.id
        <where>
            <if test="company != null and company != ''">
                AND m.company = #{company}
            </if>
            <if test="startDate != null">
                AND m.meal_date >= #{startDate}
            </if>
            <if test="endDate != null">
                AMD m.meal_date <![CDATA[<=]]> #{endDate}
            </if>
            <if test="dateList != null and dateList.size() > 0">
                AND m.meal_date IN <foreach collection="dateList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="delegateCompanies != null and delegateCompanies.size() > 0">
                AND m.delegate_company IN <foreach collection="delegateCompanies" item="item" open="(" close=")" separator=",">#{item.id}</foreach>
            </if>
        </where>
    </select>

    <!-- 批量添加就餐预定 -->
    <insert id="batchAddBookings" parameterType="java.util.List">
        INSERT INTO wx_booking_meal(
            mpid, openid, company, employee, delegate_company, meal_date, meal_book, create_time, modified_time
        ) VALUES
        <foreach collection="bookingList" item="item" separator=",">
        (
            #{item.mpid}, #{item.openid}, #{item.company}, #{item.employee}, #{item.delegateCompany}, #{item.mealDate}, #{item.mealBook}, now(), now()
        )
        </foreach>
    </insert>

    <!-- 公司日报 -->
    <select id="listCompanyDayReport" parameterType="com.senox.user.vo.BookingMealDayReportSearchVo" resultMap="Result_BookingMealCompanyDayReport">
        SELECT MAX(id) AS id
            , meal_date
            , company
            , SUM(meal_book) AS booked_count
            , SUM(CASE meal_book WHEN 0 THEN 1 ELSE 0 END) AS unbooked_count
            , 0 AS is_total
        FROM wx_booking_meal
        <where>
            <if test="company != null and company != ''">
                AND company = #{company}
            </if>
            <if test="startDate != null">
                AND meal_date >= #{startDate}
            </if>
            <if test="endDate != null">
                AMD meal_date <![CDATA[<=]]> #{endDate}
            </if>
            <if test="dateList != null and dateList.size() > 0">
                AND meal_date IN <foreach collection="dateList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="delegateCompanies != null and delegateCompanies.size() > 0">
                AND company NOT IN <foreach collection="delegateCompanies" item="item" open="(" close=")" separator=",">#{item.companyName}</foreach>
            </if>
            AND  delegate_company = 0
        </where>
        GROUP BY meal_date, company
    </select>
</mapper>