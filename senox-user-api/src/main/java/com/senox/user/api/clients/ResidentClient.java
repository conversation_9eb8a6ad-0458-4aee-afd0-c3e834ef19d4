package com.senox.user.api.clients;

import com.senox.common.validation.groups.Add;
import com.senox.common.vo.PageResult;
import com.senox.user.api.UserServiceUrl;
import com.senox.user.vo.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/5/22 14:54
 */
@FeignClient(UserServiceUrl.SERVICE_NAME)
public interface ResidentClient {

    /**
     * 添加住户
     *
     * @param residentVo
     * @return
     */
    @PostMapping(UserServiceUrl.RESIDENT_ADD)
    String addResident(@RequestBody ResidentVo residentVo);

    /**
     * 修改住户,不修改人脸
     *
     * @param residentVo
     */
    @PostMapping(UserServiceUrl.RESIDENT_UPDATE)
    void updateResident(@RequestBody ResidentVo residentVo);

    /**
     * 只修改人脸
     *
     * @param residentFaceUrlVo
     */
    @PostMapping(UserServiceUrl.RESIDENT_UPDATE_FACE)
    void updateFaceUrl(@RequestBody ResidentFaceUrlVo residentFaceUrlVo);

    /**
     * 删除住户
     *
     * @param id
     */
    @PostMapping(UserServiceUrl.RESIDENT_DELETE)
    void deleteResident(@PathVariable Long id);

    /**
     * 获取住户
     *
     * @param id
     * @return
     */
    @GetMapping(UserServiceUrl.RESIDENT_GET)
    ResidentVo findResidentById(@PathVariable Long id);

    /**
     * 根据住户编号查找住户
     *
     * @param residentNo
     * @return
     */
    @GetMapping(UserServiceUrl.RESIDENT_GETBYNO)
    ResidentVo findResidentByResidentNo(@RequestParam String residentNo);

    /**
     * 根据身份证查找住户
     *
     * @param idNum
     * @return
     */
    @GetMapping(UserServiceUrl.RESIDENT_GETBYIDNUM)
    ResidentVo findResidentByIdNum(@RequestParam String idNum);

    /**
     * 住户列表
     *
     * @param search
     * @return
     */
    @PostMapping(UserServiceUrl.RESIDENT_LIST)
    PageResult<ResidentVo> residentList(@RequestBody ResidentSearchVo search);

    /**
     * 添加住户权限
     *
     * @param residentAccessVo
     */
    @PostMapping(UserServiceUrl.RESIDENT_ACCESS_ADD)
    void addResidentAccess(@RequestBody ResidentAccessVo residentAccessVo);

    /**
     * 根据id列表删除住户权限
     *
     * @param ids
     */
    @PostMapping(UserServiceUrl.RESIDENT_ACCESS_DELETE)
    void deleteResidentAccess(@RequestBody List<Long> ids);

    /**
     * 根据住户编号查询住户权限
     *
     * @param residentNo
     * @return
     */
    @GetMapping(UserServiceUrl.RESIDENT_ACCESS_BYNO)
    ResidentAccessResultVo residentAccessResultByNo(@RequestParam String residentNo);

    /**
     * 检验住户
     *
     * @param residentCheckVo
     * @return
     */
    @PostMapping(UserServiceUrl.RESIDENT_CHECK)
    ResidentVo checkResident(@Validated(Add.class) @RequestBody ResidentCheckVo residentCheckVo);


    /**
     * 根据合同号删除住户权限
     * @param contractNo
     */
    @GetMapping(UserServiceUrl.RESIDENT_ACCESS_DELETE_BY_CONTRACT_NO)
    void deleteAccessByContractNo(@RequestParam String contractNo);

    /**
     * 续签恢复用户门禁权限
     * @param oldContractNo
     * @param newContractNo
     */
    @GetMapping(UserServiceUrl.RESIDENT_RENEWAL_ACCESS)
    void renewalAccess(@RequestParam String oldContractNo, @RequestParam String newContractNo);

    /**
     * 门禁权限同步
     * @param deviceId
     * @param targetDeviceId
     */
    @GetMapping(UserServiceUrl.RESIDENT_ACCESS_SYNC)
    void residentAccessSync(@PathVariable Long deviceId, @RequestParam Long targetDeviceId);
}
