package com.senox.user.api;

/**
 * <AUTHOR>
 * @date 2023/12/27 18:22
 */
public class ReservationRecordServiceUrl {

    private ReservationRecordServiceUrl() {
    }

    public static final String RESERVATION_RECORD_ADD = "/reservation/record/add";
    public static final String RESERVATION_RECORD_UPDATE = "/reservation/record/update";
    public static final String RESERVATION_RECORD_GET = "/reservation/record/findById/{id}";
    public static final String RESERVATION_RECORD_LIST = "/reservation/record/list";
    public static final String RESERVATION_RECORD_SUM = "/reservation/record/sum";
}
