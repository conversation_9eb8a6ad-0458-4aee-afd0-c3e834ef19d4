package com.senox.user.api.clients;

import com.senox.common.vo.PageResult;
import com.senox.user.api.UserServiceUrl;
import com.senox.user.vo.BusinessCategoryVo;
import com.senox.user.vo.EnterpriseEditVo;
import com.senox.user.vo.EnterpriseRealtyVo;
import com.senox.user.vo.EnterpriseSearchVo;
import com.senox.user.vo.EnterpriseViewVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

import static com.senox.user.api.UserServiceUrl.*;

/**
 * <AUTHOR>
 * @date 2024/8/14 13:57
 */
@FeignClient(UserServiceUrl.SERVICE_NAME)
public interface EnterpriseClient {

    /**
     * 新增经营范围
     * @param category
     * @return
     */
    @PostMapping(BUSINESS_CATEGORY_ADD)
    Long addBusinessCategory(@RequestBody BusinessCategoryVo category);

    /**
     * 更新经营范围
     * @param category
     */
    @PostMapping(BUSINESS_CATEGORY_UPDATE)
    void updateBusinessCategory(@RequestBody BusinessCategoryVo category);

    /**
     * 删除经营范围
     * @param id
     */
    @PostMapping(BUSINESS_CATEGORY_DELETE)
    void deleteBusinessCategory(@PathVariable Long id);

    /**
     * 更新经营范围列表
     * @return
     */
    @PostMapping(BUSINESS_CATEGORY_LIST)
    List<BusinessCategoryVo> listBusinessCategory();

    /**
     * 保存企业
     * @param enterprise
     * @return
     */
    @PostMapping(ENTERPRISE_SAVE)
    Long saveEnterprise(@RequestBody EnterpriseEditVo enterprise);

    /**
     * 删除企业
     * @param id
     */
    @PostMapping(ENTERPRISE_DELETE)
    void deleteEnterprise(@PathVariable Long id);

    /**
     * 设定经营户消防重点场所
     * @param enterpriseIds
     */
    @PostMapping(ENTERPRISE_FIREFIGHTING_EMPHASIS_SAVE)
    void saveEnterpriseFirefightingEmphasis(@RequestBody List<Long> enterpriseIds);

    /**
     * 取消经营户消防重点场所
     * @param enterpriseIds
     */
    @PostMapping(ENTERPRISE_FIREFIGHTING_EMPHASIS_CANCEL)
    void cancelEnterpriseFirefightingEmphasis(@RequestBody List<Long> enterpriseIds);

    /**
     * 获取经营户
     * @param id
     * @return
     */
    @GetMapping(ENTERPRISE_GET)
    EnterpriseViewVo findEnterpriseById(@PathVariable Long id);

    /**
     * 经营户列表
     * @param search
     * @return
     */
    @PostMapping(ENTERPRISE_LIST)
    List<EnterpriseViewVo> listEnterprise(@RequestBody EnterpriseSearchVo search);

    /**
     * 经营户列表页
     * @param search
     * @return
     */
    @PostMapping(ENTERPRISE_PAGE)
    PageResult<EnterpriseViewVo> listEnterprisePage(@RequestBody EnterpriseSearchVo search);

    /**
     * 经营户物业列表
     * @param enterpriseIds
     * @return
     */
    @PostMapping(ENTERPRISE_REALTY_LIST)
    List<EnterpriseRealtyVo> ListEnterpriseRealities(@RequestBody List<Long> enterpriseIds);
}
