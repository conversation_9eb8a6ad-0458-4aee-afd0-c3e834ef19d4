package com.senox.user;

import com.senox.common.spring.SenoxBaseConfigure;
import com.senox.common.spring.SenoxWebConfigure;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Import;

/**
 * 用户服务
 * <AUTHOR>
 * @Date 2020/12/22 15:52
 */
@SpringBootApplication
@Import({SenoxBaseConfigure.class, SenoxWebConfigure.class})
public class UserApplication {

    public static void main(String[] args) {
        SpringApplication.run(UserApplication.class, args);
    }

}
