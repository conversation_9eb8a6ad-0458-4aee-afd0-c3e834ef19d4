package com.senox.user.api;

/**
 * <AUTHOR>
 * @date 2024/10/17 9:12
 */
public class ActivityServiceUrl {

    private ActivityServiceUrl(){

    }

    public static final String ACTIVITY_ADD = "/activity/add";
    public static final String ACTIVITY_UPDATE = "/activity/update";
    public static final String ACTIVITY_GENERATE = "/activity/generate/{id}";
    public static final String ACTIVITY_GET = "/activity/get/{id}";
    public static final String ACTIVITY_GET_BY_UUID = "/activity/getByUuid/{uuid}";
    public static final String ACTIVITY_DELETE = "/activity/delete/{id}";
    public static final String ACTIVITY_PAGE = "/activity/page";


    public static final String VOTE_CATEGORY_SAVE = "/vote/category/save";
    public static final String VOTE_CATEGORY_GET = "/vote/category/get/{id}";
    public static final String VOTE_CATEGORY_DELETE = "/vote/category/delete/{id}";
    public static final String VOTE_CATEGORY_PAGE = "/vote/category/page";
    public static final String VOTE_RESOURCES_SAVE = "/vote/resources/save";
    public static final String VOTE_RESOURCES_GET = "/vote/resources/get/{id}";
    public static final String VOTE_RESOURCES_DELETE = "/vote/resources/delete/{id}";
    public static final String VOTE_RESOURCES_PAGE = "/vote/resources/page";
    public static final String VOTE_RECORDS_SAVE = "/vote/records/save";
    public static final String VOTE_RECORDS_AVAILABLE_NUMBERS = "/vote/records/availableNumbers/{activityId}";

}
