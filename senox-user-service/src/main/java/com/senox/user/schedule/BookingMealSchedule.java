package com.senox.user.schedule;

import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.StringUtils;
import com.senox.user.domain.BookingMeal;
import com.senox.user.domain.BookingMealCompanyDayReport;
import com.senox.user.domain.Company;
import com.senox.user.domain.Employee;
import com.senox.user.service.*;
import com.senox.user.vo.BookingMealDayReportSearchVo;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/4/15 15:59
 */
@Component
public class BookingMealSchedule {

    /**
     * 一次加载的记录数
     */
    private static final int ONCE_LOAD_SIZE = 1000;

    @Value("${senox.bookingMeal.mpid:gh_a4088892cdad}")
    private String defaultMpId;

    @Autowired
    private EmployeeService employeeService;
    @Autowired
    private BookingMealService bookingMealService;
    @Autowired
    private HolidayService holidayService;
    @Autowired
    private CompanyService companyService;
    @Autowired
    private BookingMealCompanyDayReportService companyDayReportService;

    /**
     * 按默认报餐设置，定期生成未报餐人员的报餐信息
     */
    @XxlJob("mealBookingJob")
    public void bookingMealDefaultJob() {
        LocalDate bookingDate = prepareBookingMealDate(XxlJobHelper.getJobParam());
        if (holidayService.isHoliday(bookingDate)) {
            XxlJobHelper.log("{} 非工作日,跳过", bookingDate);
            return;
        }

        XxlJobHelper.log("按默认报餐设置，生成未报餐人员的报餐信息，用餐时间：{}", bookingDate);
        long execTime = System.currentTimeMillis();

        // 员工报餐
        int offset = 0;
        List<Employee> employees = employeeService.listNoBookedEmployee(bookingDate, offset, ONCE_LOAD_SIZE);
        while (!CollectionUtils.isEmpty(employees)) {
            List<BookingMeal> bookingList = employees.stream()
                    .map(x -> this.newBookingMeal(x, bookingDate))
                    .collect(Collectors.toList());
            bookingMealService.batchAddBookings(bookingList);

            offset += employees.size();
            employees = employeeService.listNoBookedEmployee(bookingDate, offset, ONCE_LOAD_SIZE);
        }

        // 企业报餐
        List<Company> companies = companyService.listNoBookedDelegateCompany(bookingDate);
        for (Company item : companies) {
            bookingMealService.batchAddBookings(Collections.singletonList(newBookingMeal(item, bookingDate)));
        }

        XxlJobHelper.log("生成报餐时间结束，耗时：{}", System.currentTimeMillis() - execTime);
    }

    /**
     * 报餐公司日报
     * 每天凌晨会把昨天之后的日报都生成一遍
     */
    @XxlJob("mealBookingCompanyDayReport")
    public void bookingMealCompanyDayReport() {
        long execTime = System.currentTimeMillis();
        // 报告日期
        LocalDate startReportDate = prepareReportDate(XxlJobHelper.getJobParam());
        XxlJobHelper.log("生成公司日报餐报表，日期 {}", startReportDate);

        // 报餐日报
        BookingMealDayReportSearchVo searchVo = new BookingMealDayReportSearchVo();
        searchVo.setStartDate(startReportDate);
        List<BookingMealCompanyDayReport> reportList = bookingMealService.listCompanyDayReportRealTime(searchVo);
        if (reportList == null) {
            return;
        }
        XxlJobHelper.log("reportList: {}", JsonUtils.object2Json(reportList));
        companyDayReportService.batchSaveCompanyDayReport(reportList);

        XxlJobHelper.log("生成公司日报餐报表结束，耗时：{}", System.currentTimeMillis() - execTime);
    }

    /**
     * 员工对象转报餐对象
     * @param employee
     * @param mealDate
     * @return
     */
    private BookingMeal newBookingMeal(Employee employee, LocalDate mealDate) {
        BookingMeal result = new BookingMeal();
        result.setMpid(defaultMpId);
        result.setOpenid(StringUtils.trimToEmpty(employee.getMpOpenid()));
        result.setCompany(StringUtils.trimToEmpty(employee.getCompanyName()));
        result.setEmployee(StringUtils.trimToEmpty(employee.getUsername()));
        result.setDelegateCompany(0L);
        result.setMealDate(mealDate);
        result.setMealBook(employee.getDefaultBooked() == null ? 0 : employee.getDefaultBooked());
        return result;
    }

    /**
     * 企业报餐
     * @param company
     * @param mealDate
     * @return
     */
    private BookingMeal newBookingMeal(Company company, LocalDate mealDate) {
        Employee delegateEmployee = employeeService.findDelegateCompanyEmployee(company.getId());

        BookingMeal result = new BookingMeal();
        result.setMpid(defaultMpId);
        result.setCompany(company.getCompanyName());
        result.setOpenid(delegateEmployee == null || delegateEmployee.getMpOpenid() == null ? StringUtils.EMPTY : delegateEmployee.getMpOpenid());
        result.setEmployee(delegateEmployee == null ? StringUtils.EMPTY : delegateEmployee.getUsername());
        result.setDelegateCompany(company.getId());
        result.setMealDate(mealDate);
        result.setMealBook(delegateEmployee == null || delegateEmployee.getDefaultBooked() == null ? 0 : delegateEmployee.getDefaultBooked());
        return result;
    }

    private LocalDate prepareBookingMealDate(String dateStr) {
        if (!StringUtils.isBlank(dateStr)) {
            try {
                return LocalDate.parse(dateStr);
            } catch (Exception e) {
                XxlJobHelper.log("parse date param " + dateStr + " error", e);
            }
        }
        return LocalDate.now().plusDays(1L);
    }

    /**
     * 获取报告日期
     * @param dateStr
     * @return
     */
    private LocalDate prepareReportDate(String dateStr) {
        if (!StringUtils.isBlank(dateStr)) {
            try {
                return LocalDate.parse(dateStr);
            } catch (Exception e) {
                XxlJobHelper.log("parse date param " + dateStr + " error", e);
            }
        }
        return LocalDate.now().minusDays(1L);
    }
}
