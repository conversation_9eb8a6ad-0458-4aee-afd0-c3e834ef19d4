<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.user.mapper.HolidayMapper">

    <resultMap id="Result_Holiday" type="com.senox.user.domain.Holiday">
        <result property="holiday" jdbcType="TIMESTAMP" column="holiday" />
        <result property="description" jdbcType="VARCHAR" column="description" />
        <result property="modifiedTime" jdbcType="TIMESTAMP" column="modified_time" />
    </resultMap>

    <!-- 批量添加假期 -->
    <insert id="batchAddHoliday" parameterType="java.util.List">
        INSERT INTO u_holiday(
            holiday, description, creator_id, creator_name, create_time, modifier_id, modifier_name, modified_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.holiday}, #{item.description}, #{item.creatorId}, #{item.creatorName}, NOW(), #{item.modifierId}, #{item.modifierName}, NOW()
        )
        </foreach>
    </insert>

    <!-- 批量更新假期 -->
    <update id="batchUpdateHoliday" parameterType="java.util.List">
        UPDATE u_holiday
        <trim prefix="SET" suffixOverrides=",">
            <trim prefix="description = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    <if test="item.description != null">
                        WHEN holiday = #{item.holiday} THEN #{item.description}
                    </if>
                </foreach>
            </trim>
            <trim prefix="modifier_id = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    <if test="item.modifierId != null">
                        WHEN holiday = #{item.holiday} THEN #{item.modifierId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="modifier_name = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    <if test="item.modifierName != null">
                        WHEN holiday = #{item.holiday} THEN #{item.modifierName}
                    </if>
                </foreach>
            </trim>
            modified_time = NOW()
        </trim>
        WHERE holiday IN <foreach collection="list" item="item" open="(" close=")" separator=",">#{item.holiday}</foreach>
    </update>

    <!-- 批量删除假期 -->
    <update id="batchDeleteHoliday" parameterType="java.util.List">
        DELETE FROM u_holiday WHERE holiday IN <foreach collection="list" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </update>

    <!-- 查找日期内假期列表 -->
    <select id="listHolidayInDate" parameterType="java.util.List" resultMap="Result_Holiday">
        SELECT holiday, description, modified_time
        FROM u_holiday
        WHERE holiday IN <foreach collection="dateList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            AND is_disabled = 0
        ORDER BY holiday
    </select>

    <!-- 查找日期范围内的假期 -->
    <select id="listHolidayInRange" resultMap="Result_Holiday">
        SELECT holiday, description, modified_time
        FROM u_holiday
        WHERE holiday >= #{startDate}
            AND holiday <![CDATA[<=]]> #{endDate}
            AND is_disabled = 0
        ORDER BY holiday
    </select>
</mapper>