package com.senox.user.config;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCache;
import org.springframework.cache.support.SimpleCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2021/1/8 16:39
 */
@EnableCaching
@Configuration
public class CaffeineCacheConfig {

    /**
     * 定义cache名称、超时时长（秒）、最大容量
     */
    public enum Caches {
        /**
         * 管理用户 （10分钟）
         */
        ADMIN_USER(600, 5000),
        ADMIN_WECHAT_USER(600, 5000),
        WECHAT_USER(600, 5000),
        AUTH_CREDENTIALS(600, 5000)
        ;

        /**
         * 有效时间
         */
        private final int ttl;
        private final int maxSize;

        Caches(int ttl, int maxSize) {
            this.ttl = ttl;
            this.maxSize = maxSize;
        }

        public int getTtl() {
            return ttl;
        }

        public int getMaxSize() {
            return maxSize;
        }
    }

    @Bean
    public CacheManager caffeineCacheManager() {
        List<CaffeineCache> caches = new ArrayList<>(Caches.values().length);
        for (Caches item : Caches.values()) {
            Cache cacheItem = Caffeine.newBuilder().recordStats()
                    .expireAfterWrite(item.getTtl(), TimeUnit.SECONDS)
                    .maximumSize(item.getMaxSize())
                    .build();
            caches.add(new CaffeineCache(item.name(), cacheItem));
        }

        SimpleCacheManager cacheManager = new SimpleCacheManager();
        cacheManager.setCaches(caches);
        return cacheManager;
    }
}
