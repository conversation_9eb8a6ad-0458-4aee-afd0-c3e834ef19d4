package com.senox.user.controller;

import com.senox.common.utils.RedisUtils;
import com.senox.user.BaseControllerTest;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 多个测试类演示 - 第二个类
 */
public class MultipleClassesDemo2 extends BaseControllerTest {

    private static final Logger logger = LoggerFactory.getLogger(MultipleClassesDemo2.class);

    @Test
    public void testClass2_ConfigCache() {
        logger.info("=== 测试类2：配置缓存场景 ===");
        
        String configKey = "senox:config:class2";
        String configValue = "{\"maxFileSize\":20971520,\"class\":\"demo2\"}";
        
        // 设置配置缓存，1天过期
        RedisUtils.set(configKey, configValue, 24 * 60 * 60);
        logger.info("设置配置缓存: {} = {}", configKey, configValue);
        
        // 读取验证
        String result = RedisUtils.get(configKey);
        assert configValue.equals(result) : "配置缓存读取失败";
        
        logger.info("配置缓存验证通过");
        
        // 清理
        RedisUtils.del(configKey);
        
        logger.info("测试类2完成");
    }

    @Test
    public void testClass2_CounterOperations() {
        logger.info("=== 测试类2：计数器操作场景 ===");
        
        String counterKey = "senox:counter:class2:api-calls";
        
        // 计数器操作
        Long count1 = RedisUtils.incr(counterKey);
        Long count2 = RedisUtils.incr(counterKey);
        Long count3 = RedisUtils.incr(counterKey);
        
        logger.info("计数器测试: {} -> {} -> {}", count1, count2, count3);
        
        assert count2 > count1 : "计数器递增失败";
        assert count3 > count2 : "计数器递增失败";
        assert count1 + 1 == count2 : "计数器递增不正确";
        assert count2 + 1 == count3 : "计数器递增不正确";
        
        logger.info("计数器操作验证通过");
        
        // 清理
        RedisUtils.del(counterKey);
        
        logger.info("测试类2计数器测试完成");
    }

    @Test
    public void testClass2_DataIsolation() {
        logger.info("=== 测试类2：数据隔离验证 ===");
        
        // 验证不会看到其他测试类的数据
        String[] otherClassKeys = {
            "senox:user:class1-user456",
            "senox:session:class1-session-abc123"
        };
        
        for (String key : otherClassKeys) {
            boolean exists = RedisUtils.hasKey(key);
            if (exists) {
                logger.warn("发现其他测试类的残留数据: {}", key);
            } else {
                logger.debug("其他测试类数据已清理: {}", key);
            }
            // 注意：这里不做断言，因为测试执行顺序可能不同
        }
        
        // 设置本测试类的数据
        String testKey = "senox:test:class2:isolation";
        String testValue = "隔离测试数据-class2";
        
        RedisUtils.set(testKey, testValue);
        String result = RedisUtils.get(testKey);
        assert testValue.equals(result) : "数据隔离测试失败";
        
        logger.info("数据隔离验证通过");
        
        // 清理
        RedisUtils.del(testKey);
        
        logger.info("测试类2隔离测试完成");
    }
}
