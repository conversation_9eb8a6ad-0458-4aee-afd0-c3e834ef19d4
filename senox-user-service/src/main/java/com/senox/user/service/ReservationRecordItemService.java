package com.senox.user.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.SeparateUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.user.domain.ReservationRecordItem;
import com.senox.user.mapper.ReservationRecordItemMapper;
import com.senox.user.utils.ContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/27 17:10
 */
@Slf4j
@Service
public class ReservationRecordItemService extends ServiceImpl<ReservationRecordItemMapper, ReservationRecordItem> {

    /**
     * 添加预约记录车牌信息
     * @param recordId
     * @param recordItems
     */
    @Transactional(rollbackFor = Exception.class)
    public void addReservationRecordItem(Long recordId, List<ReservationRecordItem> recordItems) {
        if (!WrapperClassUtils.biggerThanLong(recordId, 0L)) {
            throw new InvalidParameterException();
        }
        recordItems.forEach(x -> x.setReservationRecordId(recordId));

        List<ReservationRecordItem> dbItems = listRecordItemByRecordId(recordId);
        DataSepDto<ReservationRecordItem> sepData = SeparateUtils.separateData(dbItems, recordItems);
        if (!CollectionUtils.isEmpty(sepData.getAddList())) {
            log.info("添加的预约记录车牌信息集合 === :{}", JsonUtils.object2Json(sepData.getAddList()));
            sepData.getAddList().forEach(x -> {
                ContextUtils.initEntityCreator(x);
                ContextUtils.initEntityModifier(x);
                x.setCreateTime(LocalDateTime.now());
                x.setModifiedTime(LocalDateTime.now());
            });
            saveBatch(sepData.getAddList());
        }
        if (!CollectionUtils.isEmpty(sepData.getUpdateList())) {
            log.info("更新的预约记录车牌信息集合 === :{}", JsonUtils.object2Json(sepData.getUpdateList()));
            sepData.getUpdateList().forEach(x -> {
                ContextUtils.initEntityModifier(x);
                x.setModifiedTime(LocalDateTime.now());
            });
            updateBatchById(sepData.getUpdateList());
        }
        if(!CollectionUtils.isEmpty(sepData.getRemoveList())) {
            log.info("删除的预约记录车牌信息集合 === :{}", JsonUtils.object2Json(sepData.getUpdateList()));
            removeByIds(sepData.getRemoveList().stream().map(ReservationRecordItem::getId).collect(Collectors.toList()));
        }
    }

    /**
     * 根据预约记录id查询列表
     * @param recordId
     * @return
     */
    public List<ReservationRecordItem> listRecordItemByRecordId(Long recordId) {
        return list(new QueryWrapper<ReservationRecordItem>().lambda()
                .eq(ReservationRecordItem::getReservationRecordId, recordId));
    }
}
