<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.user.mapper.DepartmentMapper">

    <resultMap id="Result_Department" type="com.senox.user.domain.Department">
        <id property="id" jdbcType="BIGINT" column="id" />
        <result property="name" jdbcType="VARCHAR" column="name" />
        <result property="fullName" jdbcType="VARCHAR" column="full_name" />
        <result property="parentId" jdbcType="BIGINT" column="parent_id" />
        <result property="orderNo" jdbcType="INTEGER" column="order_no" />
        <result property="disabled" jdbcType="TINYINT" column="is_disabled" />
        <result property="creatorId" jdbcType="BIGINT" column="creator_id" />
        <result property="creatorName" jdbcType="VARCHAR" column="creator_name" />
        <result property="createTime" jdbcType="TIMESTAMP" column="create_time" />
        <result property="modifierId" jdbcType="BIGINT" column="modifier_id" />
        <result property="modifierName" jdbcType="VARCHAR" column="modifier_name" />
        <result property="modifiedTime" jdbcType="TIMESTAMP" column="modified_time" />
    </resultMap>

    <!-- 添加部门 -->
    <insert id="addDepartment" parameterType="com.senox.user.domain.Department" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO u_department(
            name, full_name, parent_id, order_no, creator_id, creator_name, create_time, modifier_id, modifier_name, modified_time
        ) VALUES(
            #{name}, #{fullName}, #{parentId}, #{orderNo}, #{creatorId}, #{creatorName}, NOW(), #{modifierId}, #{modifierName}, NOW()
        )
    </insert>

    <!-- 更新部门 -->
    <update id="updateDepartment" parameterType="com.senox.user.domain.Department">
        UPDATE u_department
        <set>
            <if test="name != null and name != ''">
                , name = #{name}
            </if>
            <if test="fullName != null and fullName != ''">
                , full_name = #{fullName}
            </if>
            <if test="parentId != null">
                , parent_id = #{parentId}
            </if>
            <if test="orderNo != null">
                , order_no = #{orderNo}
            </if>
            <if test="disabled != null">
                , is_disabled = #{disabled}
            </if>
            , modifier_id = #{modifierId}
            , modifier_name = #{modifierName}
            , modified_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据id查找部门 -->
    <select id="findById" parameterType="java.lang.Long" resultMap="Result_Department">
        SELECT id, name, full_name, parent_id, order_no, is_disabled FROM u_department WHERE id = #{id}
    </select>

    <!-- 根据名字查找部门 -->
    <select id="findByName" resultMap="Result_Department">
        SELECT id, name, full_name, parent_id, order_no, is_disabled FROM u_department WHERE parent_id = #{parentId} AND name = #{name}
    </select>

    <!-- 部门列表 -->
    <select id="listByParentId" parameterType="java.lang.Long" resultMap="Result_Department">
        SELECT id, name, full_name, modified_time FROM u_department
        WHERE parent_id = #{parentId} AND is_disabled = 0
        ORDER BY order_no, id DESC
    </select>

    <select id="listAll" resultMap="Result_Department">
        SELECT id, name, full_name, parent_id, modified_time FROM u_department
        WHERE is_disabled = 0
        ORDER BY order_no, id DESC
    </select>
</mapper>