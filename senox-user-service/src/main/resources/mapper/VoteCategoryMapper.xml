<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.user.mapper.VoteCategoryMapper">


    <select id="count" parameterType="com.senox.user.vo.VoteCategorySearchVo" resultType="int">
        select count(1) from u_vote_category
        <where>
            <if test="name != null and name != ''">
                and name like concat('%', #{name}, '%')
            </if>
            <if test="activityId != null">
                and activity_id = #{activityId}
            </if>
            and is_disabled = 0
        </where>
    </select>

    <select id="list" parameterType="com.senox.user.vo.VoteCategorySearchVo"  resultType="com.senox.user.vo.VoteCategoryVo">
        select id, name, activity_id
        from u_vote_category
        <where>
            <if test="name != null and name != ''">
                and name like concat('%', #{name}, '%')
            </if>
            <if test="activityId != null">
                and activity_id = #{activityId}
            </if>
            and is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY id DESC
            </otherwise>
        </choose>
        <if test="page">
            limit ${offset}, ${pageSize}
        </if>
    </select>

</mapper>
