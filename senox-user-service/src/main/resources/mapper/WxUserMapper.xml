<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.user.mapper.WxUserMapper">

    <!-- 微信用户 -->
    <resultMap id="Result_WxUser" type="com.senox.user.vo.WxUserVo">
        <id property="id" jdbcType="BIGINT" column="id" />
        <result property="openid" jdbcType="VARCHAR" column="openid" />
        <result property="nickname" jdbcType="VARCHAR" column="nickname" />
        <result property="sex" jdbcType="INTEGER" column="sex" />
        <result property="city" jdbcType="VARCHAR" column="city" />
        <result property="province" jdbcType="VARCHAR" column="province" />
        <result property="country" jdbcType="VARCHAR" column="country" />
        <result property="avatar" jdbcType="VARCHAR" column="avatar" />
        <result property="gray" jdbcType="TINYINT" column="is_gray" />
        <result property="bindRealtyCount" jdbcType="INTEGER" column="bind_realty_count" />
        <result property="modifiedTime" jdbcType="TIMESTAMP" column="modified_time" />
    </resultMap>

    <!-- 微信用户绑定物业 -->
    <resultMap id="Result_WxUserRealty" type="com.senox.user.vo.WxUserRealtyVo">
        <result property="userId" column="user_id" jdbcType="BIGINT" />
        <result property="realtyId" column="realty_id" jdbcType="BIGINT" />
        <result property="realtySerial" column="realty_serial" jdbcType="VARCHAR" />
        <result property="realtyName" column="realty_name" jdbcType="VARCHAR" />
        <result property="contractNo" column="contract_no" jdbcType="VARCHAR" />
        <result property="bindTime" column="bind_time" jdbcType="TIMESTAMP" />
        <result property="startDate" column="start_date" jdbcType="TIMESTAMP" />
        <result property="endDate" column="end_date" jdbcType="TIMESTAMP" />
    </resultMap>

    <!-- 更新微信用户 -->
    <update id="updateWxUser" parameterType="com.senox.user.vo.WxUserVo">
        UPDATE wx_user
        <set>
            <if test="gray != null">
                , is_gray = #{gray}
            </if>
            , modified_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据id查找用户 -->
    <select id="findById" parameterType="java.lang.Long" resultMap="Result_WxUser">
        SELECT id, openid, nickname, sex, city, province, country, avatar, app_id, is_gray
        FROM wx_user
        WHERE id = #{id}
    </select>

    <!-- 根据openid查找用户 -->
    <select id="findByOpenid" resultMap="Result_WxUser">
        SELECT id, openid, nickname, sex, city, province, country, avatar, is_gray, app_id
        FROM wx_user
        WHERE openid = #{openid}
            AND is_disabled = 0
        <if test="appId != null and appId != ''">
            AND app_id = #{appId}
        </if>
    </select>

    <!-- 微信用户统计 -->
    <select id="countWxUser" parameterType="com.senox.user.vo.WxUserSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(u.id)
        FROM wx_user u
        <if test="realtySerial != null and realtySerial != ''">
            LEFT JOIN wx_user_realty ur ON ur.user_id = u.id AND ur.is_disabled = 0
            LEFT JOIN r_realty r ON r.id = ur.realty_id AND r.is_disabled = 0
        </if>
        WHERE u.is_disabled = 0
        <if test="realtySerial != null and realtySerial != ''">
            AND r.serial_no LIKE CONCAT(#{realtySerial}, '%')
        </if>
        <if test="keyword != null and keyword != ''">
            AND u.nickname LIKE CONCAT('%', #{keyword}, '%')
        </if>
        <if test="remark != null and remark != ''">
            AND u.remark LIKE CONCAT('%', #{remark}, '%')
        </if>
        <if test="contact != null and contact != ''">
            AND u.contact LIKE CONCAT('%', #{contact}, '%')
        </if>
        <if test="containRemark != null">
            <choose>
                <when test="containRemark">AND LENGTH(u.remark) > 0</when>
                <otherwise>AND LENGTH(u.remark) = 0</otherwise>
            </choose>
        </if>
    </select>

    <!-- 微信用户列表 -->
    <select id="listWxUser" parameterType="com.senox.user.vo.WxUserSearchVo" resultMap="Result_WxUser">
        SELECT u.id, u.openid, u.nickname, u.sex, u.city, u.province, u.country, u.avatar, u.is_gray, u.modified_time, u.remark, u.contact
            , (SELECT COUNT(1) FROM wx_user_realty ur1 WHERE ur1.user_id = u.id AND ur1.is_disabled = 0) AS bind_realty_count
        FROM wx_user u
        <if test="realtySerial != null and realtySerial != ''">
            LEFT JOIN wx_user_realty ur ON ur.user_id = u.id AND ur.is_disabled = 0
            LEFT JOIN r_realty r ON r.id = ur.realty_id AND r.is_disabled = 0
        </if>
        WHERE u.is_disabled = 0
        <if test="realtySerial != null and realtySerial != ''">
            AND r.serial_no LIKE CONCAT(#{realtySerial}, '%')
        </if>
        <if test="keyword != null and keyword != ''">
            AND u.nickname LIKE CONCAT('%', #{keyword}, '%')
        </if>
        <if test="remark != null and remark != ''">
            AND u.remark LIKE CONCAT('%', #{remark}, '%')
        </if>
        <if test="contact != null and contact != ''">
            AND u.contact LIKE CONCAT('%', #{contact}, '%')
        </if>
        <if test="containRemark != null">
            <choose>
                <when test="containRemark">AND LENGTH(u.remark) > 0</when>
                <otherwise>AND LENGTH(u.remark) = 0</otherwise>
            </choose>
        </if>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}, id DESC
            </when>
            <otherwise>
                ORDER BY id DESC
            </otherwise>
        </choose>
        LIMIT ${offset}, ${pageSize}
    </select>

    <!-- 删除微信用户绑定记录 -->
    <delete id="deleteWxUserRealty">
        DELETE FROM wx_user_realty WHERE user_id = #{userId} AND realty_id = #{realtyId} AND is_disabled = 0
    </delete>

    <!-- 查找用户绑定记录 -->
    <select id="findWxUserRealty" resultMap="Result_WxUserRealty">
        SELECT ur.user_id, ur.realty_id, r.serial_no AS realty_serial, ur.realty_name, ur.contract_no, ur.bind_time, ur.start_date, ur.end_date
        FROM wx_user_realty ur
            INNER JOIN r_realty r ON ur.realty_id = r.id
        WHERE ur.user_id = #{userId}
            AND ur.realty_id = #{realtyId}
            AND ur.is_disabled = 0
    </select>

    <!-- 微信用户绑定的物业列表 -->
    <select id="listWxUserRealty" parameterType="java.lang.Long" resultMap="Result_WxUserRealty">
        SELECT ur.user_id, ur.realty_id, r.serial_no AS realty_serial, ur.realty_name, ur.contract_no, ur.bind_time, ur.start_date, ur.end_date
        FROM wx_user_realty ur
            INNER JOIN r_realty r ON ur.realty_id = r.id
        WHERE ur.user_id = #{userId}
            AND ur.is_disabled = 0
    </select>

    <insert id="addWxUserRealty" parameterType="com.senox.user.vo.WxUserRealtyVo">
        INSERT INTO wx_user_realty(
            user_id, realty_id, realty_name, contract_no, start_date, end_date, bind_time, is_admin_bind, create_time, modified_time
        ) VALUES (
            #{userId}, #{realtyId}, #{realtyName}, #{contractNo}, #{startDate}, #{endDate}, NOW(), 1, NOW(), NOW()
        )
    </insert>

    <!-- 添加解绑历史 -->
    <insert id="addUnbindHistory" parameterType="com.senox.user.vo.WxUserRealtyVo">
        INSERT INTO wx_user_realty_history(
            user_id, realty_id, realty_name, contract_no, start_date, end_date, bind_time, unbind_time, is_admin_unbind, create_time, modified_time
        ) VALUES (
            #{userId}, #{realtyId}, #{realtyName}, #{contractNo}, #{startDate}, #{endDate}, #{bindTime}, NOW(), 1, NOW(), NOW()
        )
    </insert>

    <update id="updateWxUserRemark" parameterType="com.senox.user.vo.WxUserRemarkVo">
        UPDATE wx_user
        <set>
            <if test="remark != null and remark != ''">
                , remark = #{remark}
            </if>
            <if test="contact != null and contact != ''">
                , contact = #{contact}
            </if>
            , modified_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 解绑骑手 -->
    <update id="unbindRider" parameterType="java.lang.String">
        UPDATE wx_user SET rider_id = 0, modified_time = NOW() WHERE openid = #{openid}
    </update>

    <!-- 根据绑定的骑手id查找用户 -->
    <select id="findByRiderId" parameterType="java.lang.Long" resultType="com.senox.user.vo.WxUserVo">
        SELECT id, openid, nickname, sex, city, province, country, avatar, app_id, remark, contact
        FROM wx_user
        WHERE rider_id = #{riderId} AND is_disabled = 0
    </select>

</mapper>
