package com.senox.user.controller;

import com.senox.user.constant.AreaCategory;
import com.senox.user.domain.Area;
import com.senox.user.service.AreaService;
import com.senox.user.vo.AreaVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.security.InvalidParameterException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2020/12/30 15:14
 */
@Api(tags = "地区")
@RestController
@RequestMapping("/area")
public class AreaController extends BaseController {

    @Autowired
    private AreaService areaService;

    @ApiOperation("添加地区")
    @PostMapping("/add")
    public Long addArea(@Validated @RequestBody AreaVo areaVo) {
        Area area = areaVo2Area(areaVo);
        initEntityCreator(area);
        initEntityModifier(area);
        return areaService.addArea(area);
    }

    @ApiOperation("更新地区")
    @PostMapping("/update")
    public void updateArea(@Validated @RequestBody AreaVo areaVo) {
        if (areaVo.getId() < 1L) {
            throw new InvalidParameterException("无效的id");
        }

        Area area = areaVo2Area(areaVo);
        initEntityModifier(area);
        areaService.updateArea(area);
    }

    @ApiOperation("获取地区信息")
    @GetMapping("/get/{id}")
    public AreaVo getArea(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        Area area = areaService.findById(id);
        return area2Vo(area);
    }

    @ApiOperation("省份列表")
    @PostMapping("/province/list")
    public List<AreaVo> listProvinces() {
        List<Area> provinceList = areaService.listByParentIdAndCategory(0L, Arrays.asList(AreaCategory.PROVINCE, AreaCategory.CITY_DIRECTLY));
        return provinceList == null ? Collections.emptyList()
                : provinceList.stream().map(this::area2Vo).collect(Collectors.toList());
    }

    @ApiOperation("市列表")
    @PostMapping("/city/list/{parentId}")
    public List<AreaVo> listCities(@PathVariable Long parentId) {
        if (parentId < 1L) {
            throw new InvalidParameterException("无效的省份id");
        }
        List<Area> cityList = areaService.listByParentIdAndCategory(parentId, Collections.singletonList(AreaCategory.CITY));
        return cityList == null ? Collections.emptyList()
                : cityList.stream().map(this::area2Vo).collect(Collectors.toList());
    }

    private Area areaVo2Area(AreaVo areaVo) {
        Area result = new Area();
        BeanUtils.copyProperties(areaVo, result);
        return result;
    }

    private AreaVo area2Vo(Area area) {
        AreaVo result = new AreaVo();
        BeanUtils.copyProperties(area, result);
        return result;
    }
}
