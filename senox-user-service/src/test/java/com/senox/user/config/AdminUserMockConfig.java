package com.senox.user.config;

import com.senox.context.AdminContext;
import com.senox.context.AdminUserDto;
import com.senox.user.BaseTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import javax.servlet.*;
import java.io.IOException;

/**
 * <AUTHOR>
 * @Date 2021/1/11 14:37
 */
@Configuration
public class AdminUserMockConfig {

    @Bean
    @Profile("test")
    public AdminUserMockFilter adminUserMockFilter() {
        return new AdminUserMockFilter();
    }

    public class AdminUserMockFilter implements Filter {

        @Override
        public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain)
                throws IOException, ServletException {
            AdminUserDto adminUser = new AdminUserDto();
            adminUser.setUserId(BaseTest.randLong(5, 100));
            adminUser.setUsername(BaseTest.randStr(10));
            adminUser.setToken(BaseTest.randStr(20));
            AdminContext.setUser(adminUser);

            filterChain.doFilter(servletRequest, servletResponse);
        }
    }


}
