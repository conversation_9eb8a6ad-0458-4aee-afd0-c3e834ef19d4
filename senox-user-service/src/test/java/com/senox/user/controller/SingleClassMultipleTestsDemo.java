package com.senox.user.controller;

import com.senox.common.utils.RedisUtils;
import com.senox.user.BaseControllerTest;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 单个测试类多个测试用例演示
 */
public class SingleClassMultipleTestsDemo extends BaseControllerTest {

    private static final Logger logger = LoggerFactory.getLogger(SingleClassMultipleTestsDemo.class);

    @Test
    public void test01_BasicRedisOperations() {
        logger.info("=== 测试1：基本Redis操作 ===");
        
        String key = "single-class:test01";
        String value = "测试1数据-" + System.currentTimeMillis();
        
        // 基本操作
        RedisUtils.set(key, value);
        String result = RedisUtils.get(key);
        
        logger.info("写入: {} = {}", key, value);
        logger.info("读取: {} = {}", key, result);
        
        assert value.equals(result) : "基本读写测试失败";
        
        // 清理
        RedisUtils.del(key);
        
        logger.info("测试1完成");
    }

    @Test
    public void test02_ExpireOperations() {
        logger.info("=== 测试2：过期时间操作 ===");
        
        String key = "single-class:test02";
        String value = "测试2数据-" + System.currentTimeMillis();
        
        // 带过期时间的操作
        RedisUtils.set(key, value, 60); // 60秒过期
        
        // 验证存在
        boolean exists = RedisUtils.hasKey(key);
        assert exists : "过期key应该存在";
        
        String result = RedisUtils.get(key);
        assert value.equals(result) : "过期数据读取失败";
        
        logger.info("过期时间操作测试通过: {} = {}", key, result);
        
        // 清理
        RedisUtils.del(key);
        
        logger.info("测试2完成");
    }

    @Test
    public void test03_CounterOperations() {
        logger.info("=== 测试3：计数器操作 ===");
        
        String key = "single-class:test03:counter";
        
        // 计数器操作
        Long count1 = RedisUtils.incr(key);
        Long count2 = RedisUtils.incr(key);
        Long count3 = RedisUtils.incr(key);
        
        logger.info("计数器测试: {} -> {} -> {}", count1, count2, count3);
        
        assert count2 > count1 : "计数器递增失败";
        assert count3 > count2 : "计数器递增失败";
        
        // 清理
        RedisUtils.del(key);
        
        logger.info("测试3完成");
    }

    @Test
    public void test04_BusinessScenario() {
        logger.info("=== 测试4：业务场景模拟 ===");
        
        // 模拟用户缓存场景
        String userId = "user123";
        String userCacheKey = String.format("senox:user:%s", userId);
        String userInfo = "{\"id\":123,\"name\":\"测试用户\"}";
        
        // 设置用户缓存，2小时过期
        RedisUtils.set(userCacheKey, userInfo, 2 * 60 * 60);
        
        // 读取用户缓存
        String cachedUserInfo = RedisUtils.get(userCacheKey);
        assert userInfo.equals(cachedUserInfo) : "用户缓存测试失败";
        
        logger.info("用户缓存测试通过: {}", cachedUserInfo);
        
        // 模拟配置缓存场景
        String configKey = "senox:config:system";
        String configValue = "{\"maxUploadSize\":10485760}";
        
        RedisUtils.set(configKey, configValue, 24 * 60 * 60); // 1天过期
        String cachedConfig = RedisUtils.get(configKey);
        assert configValue.equals(cachedConfig) : "配置缓存测试失败";
        
        logger.info("配置缓存测试通过: {}", cachedConfig);
        
        // 清理
        RedisUtils.del(userCacheKey);
        RedisUtils.del(configKey);
        
        logger.info("测试4完成");
    }

    @Test
    public void test05_DataIsolation() {
        logger.info("=== 测试5：数据隔离验证 ===");
        
        // 验证前面测试的数据已经清理
        String[] testKeys = {
            "single-class:test01",
            "single-class:test02", 
            "single-class:test03:counter",
            "senox:user:user123",
            "senox:config:system"
        };
        
        for (String key : testKeys) {
            boolean exists = RedisUtils.hasKey(key);
            if (exists) {
                logger.warn("发现残留数据: {}", key);
            } else {
                logger.debug("数据已清理: {}", key);
            }
            assert !exists : "测试数据未清理: " + key;
        }
        
        // 设置本测试的数据
        String key = "single-class:test05";
        String value = "隔离测试数据";
        
        RedisUtils.set(key, value);
        String result = RedisUtils.get(key);
        assert value.equals(result) : "隔离测试失败";
        
        logger.info("数据隔离验证通过");
        
        // 清理
        RedisUtils.del(key);
        
        logger.info("测试5完成");
    }
}
