package com.senox.user.controller;

import com.senox.common.vo.PageResult;
import com.senox.user.BaseControllerTest;
import com.senox.user.constant.Gender;
import com.senox.user.vo.CustomerCovid19Vo;
import com.senox.user.vo.CustomerSearchVo;
import com.senox.user.vo.CustomerVo;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @Date 2020/12/31 14:10
 */
public class CustomerControllerTest extends BaseControllerTest {

    private static final Logger logger = LoggerFactory.getLogger(CustomerControllerTest.class);

    @Test
    public void testAddAndUpdate() {
        CustomerVo customerVo1 = mockCustomerVo();
        HttpEntity<CustomerVo> entity1 = new HttpEntity<>(customerVo1);
        ResponseEntity<Long> responseEntity1 = restTemplate.exchange("/customer/add", HttpMethod.POST,
                entity1, new ParameterizedTypeReference<Long>() {
                });
        Assertions.assertEquals(HttpStatus.OK, responseEntity1.getStatusCode());
        Long result1 = responseEntity1.getBody();
        logger.info("result1: {}", result1);
        Assertions.assertTrue(result1 > 0L);

        ResponseEntity<CustomerVo> responseEntity2 = restTemplate.exchange("/customer/get/" + result1,
                HttpMethod.GET, null, new ParameterizedTypeReference<CustomerVo>() {
                });
        CustomerVo result2 = responseEntity2.getBody();
        logger.info("result2: {}", result2);
        Assertions.assertEquals(customerVo1.getName(), result2.getName());
        Assertions.assertEquals(customerVo1.getNativePlace(), result2.getNativePlace());
        Assertions.assertEquals(customerVo1.getIdcard(), result2.getIdcard());
        Assertions.assertEquals(customerVo1.getGender(), result2.getGender());
        Assertions.assertEquals(customerVo1.getNation(), result2.getNation());
        Assertions.assertEquals(customerVo1.getEmail(), result2.getEmail());
        Assertions.assertEquals(customerVo1.getTelephone(), result2.getTelephone());
        Assertions.assertEquals(customerVo1.getProvinceId(), result2.getProvinceId());
        Assertions.assertEquals(customerVo1.getProvinceName(), result2.getProvinceName());
        Assertions.assertEquals(customerVo1.getCityId(), result2.getCityId());
        Assertions.assertEquals(customerVo1.getCityName(), result2.getCityName());
        Assertions.assertEquals(customerVo1.getProfessionId(), result2.getProfessionId());
        Assertions.assertEquals(customerVo1.getProfessionName(), result2.getProfessionName());
        Assertions.assertEquals(customerVo1.getBankAccount(), result2.getBankAccount());
        Assertions.assertEquals(customerVo1.getRemark(), result2.getRemark());
        Assertions.assertEquals(customerVo1.getAvatar(), result2.getAvatar());
        Assertions.assertEquals(customerVo1.getNatList().size(), result2.getNatList().size());
        Assertions.assertEquals(customerVo1.getVaccineList().size(), result2.getVaccineList().size());

        CustomerVo customerVo3 = new CustomerVo();
        customerVo3.setId(result1);
        customerVo3.setIdcard(randStr(18));
        customerVo3.setEmail(randStr(8) + "@" + randStr(4) + ".com");
        customerVo3.setProvinceId(randLong(1L, 100L));
        customerVo3.setProvinceName(randStr(20));
        customerVo3.setCityId(randLong(1L, 100L));
        customerVo3.setCityName(randStr(20));
        customerVo3.setProfessionId(randLong(1L, 100L));
        customerVo3.setProfessionName(randStr(20));
        customerVo3.setBankAccount(randStr(20));
        HttpEntity<CustomerVo> entity3 = new HttpEntity<>(customerVo3);
        ResponseEntity<Void> responseEntity3 = restTemplate.exchange("/customer/update", HttpMethod.POST, entity3,
                new ParameterizedTypeReference<Void>() {
                });
        Assertions.assertEquals(HttpStatus.OK, responseEntity3.getStatusCode());

        ResponseEntity<CustomerVo> responseEntity4 = restTemplate.exchange("/customer/get/" + result1,
                HttpMethod.GET, null, new ParameterizedTypeReference<CustomerVo>() {
                });
        CustomerVo result4 = responseEntity4.getBody();
        logger.info("result4: {}", result4);
        Assertions.assertEquals(customerVo1.getName(), result4.getName());
        Assertions.assertEquals(customerVo3.getIdcard(), result4.getIdcard());
        Assertions.assertEquals(customerVo1.getGender(), result4.getGender());
        Assertions.assertEquals(customerVo1.getNation(), result4.getNation());
        Assertions.assertEquals(customerVo3.getEmail(), result4.getEmail());
        Assertions.assertEquals(customerVo3.getProvinceId(), result4.getProvinceId());
        Assertions.assertEquals(customerVo3.getProvinceName(), result4.getProvinceName());
        Assertions.assertEquals(customerVo3.getCityId(), result4.getCityId());
        Assertions.assertEquals(customerVo3.getCityName(), result4.getCityName());
        Assertions.assertEquals(customerVo3.getProfessionId(), result4.getProfessionId());
        Assertions.assertEquals(customerVo3.getProfessionName(), result4.getProfessionName());
        Assertions.assertEquals(customerVo1.getTelephone(), result4.getTelephone());
        Assertions.assertEquals(customerVo1.getAvatar(), result4.getAvatar());
        Assertions.assertEquals(customerVo3.getBankAccount(), result4.getBankAccount());
    }

    @Test
    public void testListCustomerPage() {
        for (int i = 0; i < 30; i++) {
            CustomerVo customerVo = mockCustomerVo();
            HttpEntity<CustomerVo> entity = new HttpEntity<>(customerVo);
            restTemplate.exchange("/customer/add", HttpMethod.POST, entity, new ParameterizedTypeReference<Long>() {
            });
        }

        CustomerSearchVo searchVo = new CustomerSearchVo(2, 10);
        HttpEntity<CustomerSearchVo> entity = new HttpEntity<>(searchVo);
        ResponseEntity<PageResult<CustomerVo>> responseEntity = restTemplate.exchange("/customer/list",
                HttpMethod.POST, entity, new ParameterizedTypeReference<PageResult<CustomerVo>>() {
                });
        Assertions.assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
        PageResult<CustomerVo> result = responseEntity.getBody();
        logger.info("result: {}", result);
        Assertions.assertTrue(result.getTotalPages() >= 3);
        Assertions.assertTrue(result.getTotalSize() >= 30);

    }

    private CustomerVo mockCustomerVo() {
        CustomerVo result = new CustomerVo();
        result.setName(randStr(20));
        result.setIdcard(randStr(18));
        result.setIdcardType(0);
        result.setGender(Gender.fromValue(randInt(1, Gender.values().length)).getValue());
        result.setNation(randStr(10));
        result.setBornDate(LocalDate.now().minusYears(randInt(18, 60)));
        result.setNativePlace(randStr(10));
        result.setAddress(randStr(100));
        result.setTelephone("13" + randNumStr(9));
        result.setEmail(randStr(10) + "@" + randStr(4) + ".com");
        result.setProvinceId(randLong(1L, 100L));
        result.setProvinceName(randStr(10));
        result.setCityId(randLong(1L, 100L));
        result.setCityName(randStr(20));
        result.setWorkplaceRegionId(randLong(1, 100));
        result.setWorkplaceRegionName(randStr(20));
        result.setWorkplaceStreetId(randLong(1, 100));
        result.setWorkplaceStreetName(randStr(20));
        result.setWorkplaceAddress(randStr(20));
        result.setProfessionId(randLong(1L, 100L));
        result.setProfessionName(randStr(20));
        result.setJobTitle(randStr(20));
        result.setNatTested(Boolean.TRUE);
        result.setCovid19Vaccination(Boolean.TRUE);
        result.setBankAccount(randStr(15));
        result.setRemark(randStr(50));
        result.setAvatar(randStr(1000));

        result.setNatList(Lists.newArrayList(
                mockCovidVoList(LocalDate.now().minusDays(6)),
                mockCovidVoList(LocalDate.now().minusDays(13)),
                mockCovidVoList(LocalDate.now().minusDays(21))
        ));
        result.setVaccineList(Lists.newArrayList(mockCovidVoList(LocalDate.now())));
        return result;
    }

    private CustomerCovid19Vo mockCovidVoList(LocalDate date) {
        CustomerCovid19Vo result = new CustomerCovid19Vo();
        result.setOperateDate(date);
        result.setRemark(randStr(20));
        return result;
    }
}
