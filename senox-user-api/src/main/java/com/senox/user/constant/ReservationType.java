package com.senox.user.constant;

/**
 * <AUTHOR>
 * @date 2025/4/16 9:45
 */
public enum ReservationType {

    /**
     * 年货节
     */
    NEW_YEAR_FESTIVAL(0,"年货节"),
    /**
     * 开仓节
     */
    WAREHOUSE_OPEN_FESTIVAL(1,"开仓节");


    private final int value;
    private final String name;

    ReservationType(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static ReservationType fromValue(Integer value) {
        if (value != null) {
            for (ReservationType item : values()) {
                if (item.getValue() == value) {
                    return item;
                }
            }
        }
        return null;
    }
}
