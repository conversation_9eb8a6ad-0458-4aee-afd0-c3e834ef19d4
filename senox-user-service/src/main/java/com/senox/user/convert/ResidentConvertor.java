package com.senox.user.convert;

import com.senox.common.convert.BaseConvert;
import com.senox.user.domain.Resident;
import com.senox.user.vo.ResidentVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023/5/22 14:15
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface ResidentConvertor extends BaseConvert<Resident, ResidentVo> {

}
