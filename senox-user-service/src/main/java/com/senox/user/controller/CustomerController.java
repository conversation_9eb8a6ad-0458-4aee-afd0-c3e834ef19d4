package com.senox.user.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.StringUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.user.constant.Covid19Category;
import com.senox.user.domain.Customer;
import com.senox.user.domain.CustomerCovid19;
import com.senox.user.domain.CustomerExt;
import com.senox.user.service.CustomerService;
import com.senox.user.vo.CustomerCovid19Vo;
import com.senox.user.vo.CustomerSearchVo;
import com.senox.user.vo.CustomerVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2020/12/31 11:23
 */
@Api(tags = "客户管理")
@AllArgsConstructor
@RestController
@RequestMapping("/customer")
public class CustomerController extends BaseController {

    private final CustomerService customerService;

    @ApiOperation("添加客户")
    @PostMapping("/add")
    public Long addCustomer(@Validated({Add.class}) @RequestBody CustomerVo customerVo) {
        // 客户基础信息
        Customer customer = customerVo2Customer(customerVo);
        initEntityCreator(customer);
        initEntityModifier(customer);

        // 客户扩展信息
        CustomerExt ext = customerVo2CustomerExt(customerVo);

        // 新冠防疫信息
        List<CustomerCovid19> natList = covid19VoList2Covid19List(customerVo.getNatList(), Covid19Category.NAT);
        List<CustomerCovid19> vaccineList = covid19VoList2Covid19List(customerVo.getVaccineList(), Covid19Category.VACCINE);
        List<CustomerCovid19> covid19List = null;
        if (CollectionUtils.isEmpty(natList) && CollectionUtils.isEmpty(vaccineList)) {
            covid19List = Collections.emptyList();
        } else {
            covid19List = new ArrayList<>(natList.size() + vaccineList.size());
            covid19List.addAll(natList);
            covid19List.addAll(vaccineList);
        }

        return customerService.addCustomer(customer, ext, covid19List);
    }

    @ApiOperation("更新客户")
    @PostMapping("/update")
    public void updateCustomer(@Validated(Update.class) @RequestBody CustomerVo customerVo) {
        // 客户基础信息
        if (customerVo.getId() < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        Customer customer = customerVo2Customer(customerVo);
        initEntityModifier(customer);

        // 客户扩展信息
        CustomerExt ext = customerVo2CustomerExt(customerVo);

        // 新冠防疫信息
        List<CustomerCovid19> natList = covid19VoList2Covid19List(customerVo.getNatList(), Covid19Category.NAT);
        List<CustomerCovid19> vaccineList = covid19VoList2Covid19List(customerVo.getVaccineList(), Covid19Category.VACCINE);
        List<CustomerCovid19> covid19List = null;
        if (CollectionUtils.isEmpty(natList) && CollectionUtils.isEmpty(vaccineList)) {
            covid19List = Collections.emptyList();
        } else {
            covid19List = new ArrayList<>(natList.size() + vaccineList.size());
            covid19List.addAll(natList);
            covid19List.addAll(vaccineList);
        }

        customerService.updateCustomer(customer, ext, covid19List);
    }

    @ApiOperation("根据id获取客户信息")
    @GetMapping("/get/{id}")
    public CustomerVo getCustomer(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        // 客户基本信息
        Customer customer = customerService.findById(id);
        return prepareCustomerInfo(customer);
    }

    @ApiOperation("根据证件号获取客户信息")
    @GetMapping("/getByIdcard")
    public CustomerVo getCustomerByIdcard(@RequestParam String idcard) {
        if (StringUtils.isBlank(idcard)) {
            throw new InvalidParameterException("无效的证件号");
        }
        Customer customer = customerService.findByIdcard(idcard);
        return prepareCustomerInfo(customer);
    }

    @ApiOperation("根据客户编号获取客户信息")
    @GetMapping("/getBySerial")
    public CustomerVo getCustomerBySerialNo(@RequestParam String serialNo) {
        if (StringUtils.isBlank(serialNo)) {
            throw new InvalidParameterException("无效的客户编号");
        }
        Customer customer = customerService.findBySerialNo(serialNo);
        return prepareCustomerInfo(customer);
    }

    @ApiOperation("客户信息列表")
    @PostMapping("/list")
    public PageResult<CustomerVo> listCustomerPage(@RequestBody CustomerSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        PageResult<Customer> customerPage = customerService.listCustomerPage(searchVo);
        return PageResult.convertPage(customerPage, this::customer2Vo);
    }

    @ApiOperation("客户列表")
    @PostMapping("/listNoPage")
    public List<CustomerVo> listCustomer(@RequestBody CustomerSearchVo searchVo) {
        List<Customer> customerList = customerService.listCustomerWithoutPage(searchVo);
        return customerList == null ? Collections.emptyList() : customerList.stream().map(this::customer2Vo).collect(Collectors.toList());
    }

    /**
     * 补充客户信息
     * @param customer
     * @return
     */
    private CustomerVo prepareCustomerInfo(Customer customer) {
        if (customer == null) {
            return null;
        }

        // 客户扩展信息
        CustomerExt ext = customerService.findExtByCustomerId(customer.getId());
        // 客户新冠防疫信息
        List<CustomerCovid19> covid19List = customerService.listCustomerCovid19(customer.getId());

        CustomerVo result = customer2Vo(customer);
        // 扩展信息
        if (ext != null) {
            result.setBankAccount(ext.getBankAccount());
            result.setRemark(ext.getRemark());
            result.setAvatar(ext.getAvatar());
        }

        // 新冠防疫信息
        if (!CollectionUtils.isEmpty(covid19List)) {
            List<CustomerCovid19> natList = covid19List.stream()
                    .filter(x -> x.getCategory().equals(Covid19Category.NAT.getValue()))
                    .collect(Collectors.toList());

            List<CustomerCovid19> vaccineList = null;
            if (natList.size() < covid19List.size()) {
                vaccineList = covid19List.stream()
                        .filter(x -> x.getCategory().equals(Covid19Category.VACCINE.getValue()))
                        .collect(Collectors.toList());
            }

            if (!CollectionUtils.isEmpty(natList)) {
                result.setNatList(natList.stream().map(this::covid192Vo).collect(Collectors.toList()));
            }
            if (!CollectionUtils.isEmpty(vaccineList)) {
                result.setVaccineList(vaccineList.stream().map(this::covid192Vo).collect(Collectors.toList()));
            }
        }
        return result;
    }

    /**
     * 视图对象转实体对象
     * @param customerVo
     * @return
     */
    private Customer customerVo2Customer(CustomerVo customerVo) {
        Customer result = new Customer();
        BeanUtils.copyProperties(customerVo, result);
        return result;
    }

    /**
     * 视图对象转扩展对象
     * @param customerVo
     * @return
     */
    public CustomerExt customerVo2CustomerExt(CustomerVo customerVo) {
        if (StringUtils.isBlank(customerVo.getBankAccount())
                && StringUtils.isBlank(customerVo.getRemark())
                && StringUtils.isBlank(customerVo.getAvatar())) {
            return null;
        }
        CustomerExt ext = new CustomerExt();
        ext.setCustomerId(customerVo.getId());
        ext.setBankAccount(customerVo.getBankAccount());
        ext.setRemark(customerVo.getRemark());
        ext.setAvatar(customerVo.getAvatar());
        ext.setDisabled(customerVo.getDisabled());
        return ext;
    }

    /**
     * 实体对象转视图对象
     * @param customer
     * @return
     */
    private CustomerVo customer2Vo(Customer customer) {
        CustomerVo result = new CustomerVo();
        BeanUtils.copyProperties(customer, result);
        return result;
    }

    /**
     * 新冠防疫信息列表转实体列表
     * @param list
     * @param category
     * @return
     */
    private List<CustomerCovid19> covid19VoList2Covid19List(List<CustomerCovid19Vo> list, Covid19Category category) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(x -> covid19Vo2Entity(x, category)).collect(Collectors.toList());
    }

    /**
     * 新冠防疫视图转实体
     * @param covid19Vo
     * @param category
     * @return
     */
    private CustomerCovid19 covid19Vo2Entity(CustomerCovid19Vo covid19Vo, Covid19Category category) {
        CustomerCovid19 result = new CustomerCovid19();
        result.setCategory(category.getValue());
        result.setOperateDate(covid19Vo.getOperateDate());
        result.setRemark(covid19Vo.getRemark());
        return result;
    }

    /**
     * 新冠防疫实体转视图对象
     * @param covid19
     * @return
     */
    private CustomerCovid19Vo covid192Vo(CustomerCovid19 covid19) {
        CustomerCovid19Vo result = new CustomerCovid19Vo();
        result.setOperateDate(covid19.getOperateDate());
        result.setRemark(covid19.getRemark());
        return result;
    }
}
