package com.senox.user.service;

import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.OAuthException;
import com.senox.common.utils.*;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminUserDto;
import com.senox.user.authcredentials.dto.AccessTokenResultDto;
import com.senox.user.config.AppConfig;
import com.senox.user.constant.UserConst;
import com.senox.user.domain.AuthCredentials;
import com.senox.user.mapper.AuthCredentialsMapper;
import com.senox.user.utils.AuthCredentialsUtils;
import com.senox.user.utils.ContextUtils;
import com.senox.user.vo.AdminUserVo;
import com.senox.user.vo.AuthCredentialsSearchVo;
import com.senox.user.vo.AuthCredentialsVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.List;

import static com.senox.user.constant.UserConst.TOKEN_AUTH;

/**
 * <AUTHOR>
 * @date 2023-8-29
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AuthCredentialsService {
    private final AuthCredentialsMapper authCredentialsMapper;
    private final AppConfig appConfig;
    private final AdminUserService adminUserService;

    /**
     * 根据用户生成凭证
     *
     * @param userId 用户id
     */
    public void generateByUser(Long userId) {
        if (!WrapperClassUtils.biggerThanLong(userId, 0)) {
            return;
        }
        AdminUserVo adminUser = adminUserService.findById(userId);
        if (null == adminUser) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        if (BooleanUtils.isFalse(adminUser.getLoginable())) {
            throw new BusinessException(ResultConst.USER_NOT_AUTHENTICATED);
        }
        AuthCredentials newAuthCredentials = new AuthCredentials();
        newAuthCredentials.setUserId(userId);
        newAuthCredentials.setAppKey(getAppKeyUnique());
        newAuthCredentials.setAppSecret(AuthCredentialsUtils.generateAppSecret());
        ContextUtils.initEntityCreator(newAuthCredentials);
        ContextUtils.initEntityModifier(newAuthCredentials);
        int count = authCredentialsMapper.add(newAuthCredentials);
        if (count == 0) {
            throw new BusinessException(ResultConst.ERROR);
        }
    }

    /**
     * 获取唯一appKey
     */
    private String getAppKeyUnique() {
        return getAppKeyUnique(1);
    }

    /**
     * 获取唯一appKey
     */
    private String getAppKeyUnique(int index) {
        String appKey = AuthCredentialsUtils.generateAppKey();
        AuthCredentialsVo authCredentials = authCredentialsMapper.getByAppKey(appKey);
        if (null != authCredentials) {
            if (index >= 15){
                throw new BusinessException(ResultConst.ERROR);
            }
            index ++;
            return getAppKeyUnique(index);
        }
        return appKey;
    }

    /**
     * 列表
     *
     * @param searchVo 查询
     * @return 返回查询到的列表
     */
    public List<AuthCredentialsVo> list(AuthCredentialsSearchVo searchVo) {
        searchVo.setPage(false);
        return authCredentialsMapper.list(searchVo);
    }

    /**
     * 列表总数
     *
     * @param searchVo 查询
     * @return 返回总数
     */
    public int countList(AuthCredentialsSearchVo searchVo) {
        return authCredentialsMapper.countList(searchVo);
    }

    /**
     * 分页列表
     *
     * @param searchVo 查询
     * @return 返回分页后的列表
     */
    public PageResult<AuthCredentialsVo> listPage(AuthCredentialsSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return PageUtils.commonPageResult(searchVo, () -> countList(searchVo), () -> authCredentialsMapper.list(searchVo));
    }


    /**
     * 根据appKey查询身份验证凭证
     *
     * @param appKey appKey
     * @return 查询到的身份验证凭证
     */
    public AuthCredentialsVo getByAppKey(String appKey) {
        if (StringUtils.isBlank(appKey)) {
            return null;
        }
        return authCredentialsMapper.getByAppKey(appKey);
    }

    /**
     * 根据appKey查询用户
     *
     * @param appKey appKey
     * @return 查询到的用户
     */
    public AdminUserDto getUserByAppKey(String appKey) {
        if (StringUtils.isBlank(appKey)) {
            return null;
        }
        String cacheKey = String.format(UserConst.Cache.KEY_ADMIN_CREDENTIALS, appKey);
        AdminUserDto result = RedisUtils.get(cacheKey);
        if (result != null && result.isValid()) {
            log.info("credentials validated from redis {}", appKey);
            RedisUtils.set(cacheKey, result, UserConst.Cache.TTL_2H);
            return result;
        }
        AuthCredentialsVo authCredentials = getByAppKey(appKey);
        if (null == authCredentials || null == authCredentials.getUserDto()) {
            log.info("无效的授权凭证:{}", appKey);
            return null;
        }
        AdminUserDto user = authCredentials.getUserDto();
        result = new AdminUserDto();
        result.setUserId(user.getUserId());
        result.setUsername(user.getUsername());
        result.setRealName(user.getRealName());
        result.setAuthCredentials(appKey);
        RedisUtils.set(cacheKey, result, UserConst.Cache.TTL_2H);
        return result;
    }

    /**
     * 访问令牌
     *
     * @param appKey    appKey
     * @param appSecret appSecret
     * @return 返回带超时时间的访问令牌
     */
    public AccessTokenResultDto accessToken(String appKey, String appSecret) {
        AuthCredentialsVo authCredentials = checkAndReturnCredentials(appKey);
        checkCredentials(authCredentials, appSecret);
        String rawToken = String.format(TOKEN_AUTH, appSecret, System.currentTimeMillis(), appSecret);
        //生成accessToken
        String accessToken = AesUtils.encrypt(rawToken, appKey.getBytes(StandardCharsets.UTF_8), HexUtils.decode(appSecret));
        ///删除旧token
        removeOldAccessToken(appKey);
        //过期时间(秒)
        long expiresIn = UserConst.Cache.TTL_2H;
        //设置相关缓存
        RedisUtils.set(String.format(UserConst.Cache.KEY_OAUTH_USER, appKey), accessToken, expiresIn);
        RedisUtils.set(String.format(UserConst.Cache.KEY_OAUTH_USER_TOKEN, accessToken), appKey, expiresIn);
        AccessTokenResultDto result = new AccessTokenResultDto();
        result.setAccessToken(accessToken);
        result.setExpiresIn(expiresIn);
        return result;
    }

    /**
     * 访问令牌用户
     *
     * @param accessToken 访问令牌
     * @return 返回用户信息
     */
    public AdminUserDto userFromAccessToken(String accessToken) {
        if (StringUtils.isBlank(accessToken)) {
            return null;
        }
        String appKey = RedisUtils.get(String.format(UserConst.Cache.KEY_OAUTH_USER_TOKEN, accessToken));
        if (StringUtils.isBlank(appKey)) {
            throw new OAuthException(ResultConst.OAUTH_TOKEN_INVALID);
        }
        AuthCredentialsVo authCredentials = checkAndReturnCredentials(appKey);
        return authCredentials.getUserDto();
    }

    private void removeOldAccessToken(String appKey) {
        String accessToken = RedisUtils.get(String.format(UserConst.Cache.KEY_OAUTH_USER, appKey));
        if (StringUtils.isBlank(accessToken)) {
            return;
        }
        RedisUtils.del(String.format(UserConst.Cache.KEY_OAUTH_USER_TOKEN, accessToken));
    }

    private void checkCredentials(AuthCredentialsVo authCredentials, String appSecret) {
        if (!appSecret.equals(authCredentials.getAppSecret())) {
            throw new OAuthException(ResultConst.OAUTH_CREDENTIALS_VALIDATION_FAILED);
        }
    }

    private AuthCredentialsVo checkAndReturnCredentials(String appKey) {
        AuthCredentialsVo authCredentials = getByAppKey(appKey);
        if (null == authCredentials) {
            throw new OAuthException(ResultConst.OAUTH_CREDENTIALS_INVALID);
        }
        AdminUserDto user = authCredentials.getUserDto();
        if (BooleanUtils.isFalse(user.getLoginable())) {
            throw new OAuthException(ResultConst.USER_NOT_AUTHENTICATED);
        }
        return authCredentials;
    }
}
