package com.senox.user.authcredentials.context;

import com.senox.user.authcredentials.dto.AuthCredentialsDto;
import com.senox.user.vo.AuthCredentialsVo;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023-10-10
 */
@Setter
@Getter
public class CredentialsManageContext {
    private CredentialsManageContext() {
    }

    private static ThreadLocal<AuthCredentialsVo> authCredentials = new ThreadLocal<>();
    private static ThreadLocal<AuthCredentialsDto> authHeader = new ThreadLocal<>();

    public static void setAuthCredentials(AuthCredentialsVo authCredentials) {
        CredentialsManageContext.authCredentials.set(authCredentials);
    }

    public static void setAuthHeader(AuthCredentialsDto authHeader) {
        CredentialsManageContext.authHeader.set(authHeader);
    }

    public static AuthCredentialsVo getAuthCredentials() {
        return authCredentials.get();
    }

    public static AuthCredentialsDto getAuthHeader() {
        return authHeader.get();
    }

    public static void removeAuthCredentials() {
        authCredentials.remove();
    }

    public static void removeAuthHeader() {
        authHeader.remove();
    }
}
