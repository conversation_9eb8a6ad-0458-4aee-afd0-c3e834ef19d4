<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.user.mapper.IcCardMapper">

    <resultMap id="Result_IcCardVo" type="com.senox.user.vo.IcCardVo">
        <id property="id" jdbcType="BIGINT" column="id" />
        <result property="cardNo" jdbcType="VARCHAR" column="card_no" />
        <result property="customerSerial" jdbcType="VARCHAR" column="customer_serial" />
        <result property="customerName" jdbcType="VARCHAR" column="customer_name" />
        <result property="accountNo" jdbcType="VARCHAR" column="account_no" />
        <result property="foregift" jdbcType="DECIMAL" column="foregift" />
        <result property="balance" jdbcType="DECIMAL" column="balance" />
        <result property="grantTime" jdbcType="TIMESTAMP" column="grant_time" />
        <result property="retrieveTime" jdbcType="TIMESTAMP" column="retrieve_time" />
    </resultMap>

    <!-- IC卡统计 -->
    <select id="countIcCard" parameterType="com.senox.user.vo.IcCardSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(d.id) FROM u_ic_card d LEFT JOIN u_customer c ON d.customer_id = c.id
        <where>
            <if test="cardNo != null and cardNo != ''">
                AND d.card_no = #{cardNo}
            </if>
            <if test="customerSerial != null and customerSerial != ''">
                AND c.serial_no = #{customerSerial}
            </if>
            <if test="customerName != null and customerName != ''">
                AND c.name = #{customerName}
            </if>
            <if test="grantTimeStart != null">
                AND d.grant_time >= #{grantTimeStart}
            </if>
            <if test="grantTimeEnd != null">
                AND d.grant_time <![CDATA[<=]]> #{grantTimeEnd}
            </if>
        </where>
    </select>

    <!-- IC卡列表 -->
    <select id="listIcCard" parameterType="com.senox.user.vo.IcCardSearchVo" resultMap="Result_IcCardVo">
        SELECT c.id, c.card_no, u.serial_no AS customer_serial, u.name AS customer_name, c.account_no, c.foregift, c.balance
             , c.grant_time, c.retrieve_time, c.master, c.status
        FROM u_ic_card c
            LEFT JOIN u_customer u on c.customer_id = u.id
        <where>
            <if test="cardNo != null and cardNo != ''">
                AND d.card_no = #{cardNo}
            </if>
            <if test="customerSerial != null and customerSerial != ''">
                AND c.serial_no = #{customerSerial}
            </if>
            <if test="customerName != null and customerName != ''">
                AND c.name = #{customerName}
            </if>
            <if test="grantTimeStart != null">
                AND d.grant_time >= #{grantTimeStart}
            </if>
            <if test="grantTimeEnd != null">
                AND d.grant_time <![CDATA[<=]]> #{grantTimeEnd}
            </if>
        </where>
        <if test="orderStr != null and orderStr != ''">
            ORDER BY ${orderStr}
        </if>
        LIMIT ${offset}, ${pageSize}
    </select>

</mapper>