package com.senox.user.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.AuditVo;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.user.convert.MerchantApplyConvert;
import com.senox.user.convert.MerchantConvert;
import com.senox.user.domain.MerchantAuthApply;
import com.senox.user.service.MerchantAuthApplyService;
import com.senox.user.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-11-1
 */
@Api(tags = "商户权限申请")
@RequiredArgsConstructor
@RequestMapping("/merchant/auth/apply")
@RestController
public class MerchantAuthApplyController extends BaseController {

    private final MerchantAuthApplyService applyService;
    private final MerchantApplyConvert applyConvert;
    private final MerchantConvert merchantConvert;


    @ApiOperation("新增权限申请")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long apply(@Validated @RequestBody MerchantAuthApplyEditVo apply) {
        MerchantAuthApply entity = applyConvert.toDo(apply);

        initEntityCreator(entity);
        return applyService.addApply(entity);
    }

    @ApiOperation("修改权限申请")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateApply(@Validated @RequestBody MerchantAuthApplyEditVo apply) {
        if (!WrapperClassUtils.biggerThanLong(apply.getId(), 0L)) {
            throw new InvalidParameterException();
        }

        MerchantAuthApply entity = applyConvert.toDo(apply);
        initEntityModifier(entity);
        applyService.updateApply(entity);
    }

    @ApiOperation("修改权限申请")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteApply(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        applyService.deleteApply(id);
    }

    @ApiOperation("权限审批")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/audit")
    public MerchantVo audit(@Validated @RequestBody AuditVo audit) {
        audit.setOperatorId(getUserInContext().getUserId());
        audit.setOperatorName(getUserInContext().getUsername());
        return merchantConvert.toVo(applyService.auditApply(audit));
    }

    @ApiOperation("申请详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public MerchantAuthApplyVo findById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        MerchantAuthApply result = applyService.findById(id);
        return result == null ? null : applyConvert.toVo(result);
    }

    @ApiOperation("记录合计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/count")
    public int countApply(@RequestBody MerchantAuthApplySearchVo search) {
        return applyService.countApply(search);
    }

    @ApiOperation("列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public List<MerchantAuthApplyListVo> listApply(@RequestBody MerchantAuthApplySearchVo search) {
        search.setPage(false);
        return applyService.listApply(search);
    }

    @ApiOperation("分页列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page")
    public PageResult<MerchantAuthApplyListVo> listApplyPage(@RequestBody MerchantAuthApplySearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return PageUtils.commonPageResult(search, () -> applyService.countApply(search), () -> applyService.listApply(search));
    }

    @ApiOperation("待审核数量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/audit/count")
    public Integer auditCount() {
        return applyService.auditCount();
    }

}
