package com.senox.user.api.clients;

import com.senox.common.vo.PageResult;
import com.senox.user.api.UserServiceUrl;
import com.senox.user.vo.AdminRemoteAccessSearchVo;
import com.senox.user.vo.AdminRemoteAccessVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * <AUTHOR>
 * @date 2023/5/26 13:48
 */
@FeignClient(UserServiceUrl.SERVICE_NAME)
public interface AdminRemoteAccessClient {

    /**
     * 添加远程权限
     *
     * @param adminRemoteAccessVo
     * @return
     */
    @PostMapping(UserServiceUrl.ADMIN_REMOTE_ACCESS_ADD)
    Long addRemoteAccess(@RequestBody AdminRemoteAccessVo adminRemoteAccessVo);


    /**
     * 删除远程权限
     *
     * @param id
     */
    @PostMapping(UserServiceUrl.ADMIN_REMOTE_ACCESS_DELETE)
    void deleteRemoteAccess(@PathVariable Long id);

    /**
     * 获取远程权限
     *
     * @param id
     * @return
     */
    @GetMapping(UserServiceUrl.ADMIN_REMOTE_ACCESS_GET)
    AdminRemoteAccessVo findRemoteAccessById(@PathVariable Long id);

    /**
     * 远程权限列表
     *
     * @param search
     * @return
     */
    @PostMapping(UserServiceUrl.ADMIN_REMOTE_ACCESS_LIST)
    PageResult<AdminRemoteAccessVo> remoteAccessList(@RequestBody AdminRemoteAccessSearchVo search);

}
