<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.user.mapper.AdminUserMapper">

    <resultMap id="Result_AdminUser" type="com.senox.user.domain.AdminUser">
        <id property="id" jdbcType="BIGINT" column="id" />
        <result property="username" column="username" jdbcType="VARCHAR" />
        <result property="realName" column="real_name" jdbcType="VARCHAR" />
        <result property="password" column="password" jdbcType="VARCHAR" />
        <result property="salt" column="salt" jdbcType="VARCHAR" />
        <result property="gender" column="gender" jdbcType="TINYINT" />
        <result property="email" column="email" jdbcType="VARCHAR" />
        <result property="telephone" column="telephone" jdbcType="VARCHAR" />
        <result property="avatar" column="avatar" jdbcType="VARCHAR" />
        <result property="departmentId" column="department_id" jdbcType="BIGINT" />
        <result property="tollMan" column="is_toll_man" jdbcType="TINYINT" />
        <result property="maintainManType" column="maintain_man_type" jdbcType="TINYINT"/>
        <result property="loginPath" column="login_path" jdbcType="VARCHAR"/>
        <result property="loginable" column="is_loginable" jdbcType="TINYINT" />
        <result property="billSerial" column="bill_serial" jdbcType="INTEGER" />
        <result property="payDeviceSn" jdbcType="VARCHAR" column="pay_device_sn" />
        <result property="creatorId" column="creator_id" jdbcType="BIGINT" />
        <result property="creatorName" column="creator_name" jdbcType="VARCHAR" />
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP" />
        <result property="modifierId" column="modifier_id" jdbcType="BIGINT" />
        <result property="modifierName" column="modifier_name" jdbcType="VARCHAR" />
        <result property="modifiedTime" column="modified_time" jdbcType="TIMESTAMP" />
    </resultMap>

    <resultMap id="Result_AdminUserVo" type="com.senox.user.vo.AdminUserVo">
        <id property="id" jdbcType="BIGINT" column="id" />
        <result property="username" column="username" jdbcType="VARCHAR" />
        <result property="realName" column="real_name" jdbcType="VARCHAR" />
        <result property="gender" column="gender" jdbcType="TINYINT" />
        <result property="email" column="email" jdbcType="VARCHAR" />
        <result property="telephone" column="telephone" jdbcType="VARCHAR" />
        <result property="avatar" column="avatar" jdbcType="VARCHAR" />
        <result property="departmentId" column="department_id" jdbcType="BIGINT" />
        <result property="departmentName" column="department_name" jdbcType="VARCHAR" />
        <result property="tollMan" column="is_toll_man" jdbcType="TINYINT" />
        <result property="maintainManType" column="maintain_man_type" jdbcType="TINYINT"/>
        <result property="loginPath" column="login_path" jdbcType="VARCHAR"/>
        <result property="loginable" column="is_loginable" jdbcType="TINYINT" />
        <result property="billSerial" column="bill_serial" jdbcType="INTEGER" />
        <result property="payDeviceSn" column="pay_device_sn" jdbcType="VARCHAR" />
    </resultMap>

    <resultMap id="Result_TollMan" type="com.senox.user.vo.TollManSerialVo">
        <result property="billSerial" column="bill_serial" jdbcType="INTEGER" />
        <result property="payDeviceSn" column="pay_device_sn" jdbcType="VARCHAR" />
    </resultMap>

    <!-- 添加管理用户 -->
    <insert id="addAdminUser" parameterType="com.senox.user.domain.AdminUser" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO u_admin_user(
            username, real_name, password, salt, gender, email, telephone, avatar, department_id, is_toll_man, maintain_man_type
            , login_path , is_loginable, bill_serial, pay_device_sn, creator_id, creator_name, create_time, modifier_id, modifier_name, modified_time
        ) VALUES (
            #{username}, #{realName}, #{password}, #{salt}, #{gender}, #{email}, #{telephone}, #{avatar}, #{departmentId}, #{tollMan}, #{maintainManType}
            , #{loginPath}, #{loginable}, #{billSerial}, #{payDeviceSn}, #{creatorId}, #{creatorName}, NOW(), #{modifierId}, #{modifierName}, NOW()
        )
    </insert>

    <!-- 更新管理用户信息 -->
    <update id="updateAdminUser" parameterType="com.senox.user.domain.AdminUser">
        UPDATE u_admin_user
        <set>
            <if test="username != null and username != ''">
                username = #{username}
            </if>
            <if test="realName != null and realName != ''">
                , real_name = #{realName}
            </if>
            <if test="password != null and password != ''">
                , password = #{password}
            </if>
            <if test="salt != null and salt != ''">
                , salt = #{salt}
            </if>
            <if test="gender != null">
                , gender = #{gender}
            </if>
            <if test="email != null">
                , email = #{email}
            </if>
            <if test="telephone != null">
                , telephone = #{telephone}
            </if>
            <if test="avatar != null">
                , avatar = #{avatar}
            </if>
            <if test="departmentId != null">
                , department_id = #{departmentId}
            </if>
            <if test="tollMan != null">
                , is_toll_man = #{tollMan}
            </if>
            <if test="maintainManType != null">
                , maintain_man_type = #{maintainManType}
            </if>
            <if test="loginPath != null">
                , login_path = #{loginPath}
            </if>
            <if test="loginable != null">
                , is_loginable = #{loginable}
            </if>
            <if test="billSerial != null">
                , bill_serial = #{billSerial}
            </if>
            <if test="payDeviceSn != null">
                , pay_device_sn = #{payDeviceSn}
            </if>
            <if test="disabled != null">
                , is_disabled = #{disabled}
            </if>
            , modifier_id = #{modifierId}
            , modifier_name = #{modifierName}
            , modified_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 查找物业账单 -->
    <select id="findBillSerial" parameterType="java.lang.Long" resultMap="Result_TollMan">
        SELECT bill_serial, pay_device_sn FROM u_admin_user WHERE id = #{id}
    </select>

    <!-- 根据id查找用户 -->
    <select id="findById" parameterType="java.lang.Long" resultMap="Result_AdminUserVo">
        SELECT u.id, u.username, u.real_name, u.gender, u.email, u.telephone, u.avatar, u.department_id, IFNULL(d.full_name, d.name) AS department_name
            , u.is_toll_man, u.maintain_man_type, u.login_path, u.is_loginable, u.bill_serial, u.pay_device_sn
        FROM u_admin_user u
            LEFT JOIN u_department d ON u.department_id = d.id
        WHERE u.id = #{id}
    </select>

    <!-- 根据openid查找管理用户 -->
    <select id="findByOpenid" resultMap="Result_AdminUser">
        SELECT u.id, u.username, u.real_name, u.gender, u.email, u.telephone, u.avatar, u.department_id, u.is_toll_man, u.maintain_man_type, u.login_path, u.is_loginable, u.bill_serial, u.pay_device_sn
        FROM u_admin_user u
            INNER JOIN wx_user wu on wu.admin_user_id = u.id
        WHERE wu.openid = #{openid}
            AND u.is_disabled = 0
        <if test="appId != null and appId != ''">
            AND wu.app_id = #{appId}
        </if>
    </select>

    <!-- 根据用户名查找用户 -->
    <select id="findByUsername" parameterType="java.lang.String" resultMap="Result_AdminUser">
        SELECT id, username, real_name, password, salt, gender, email, telephone, avatar, department_id, is_toll_man, maintain_man_type, login_path, is_loginable, bill_serial, pay_device_sn
        FROM u_admin_user
        WHERE username = #{username}
            AND is_disabled = 0
    </select>

    <select id="findByRealName" parameterType="java.lang.String" resultMap="Result_AdminUser">
        SELECT id, username, real_name, gender, email, telephone, avatar, department_id, is_toll_man, maintain_man_type, login_path, is_loginable, bill_serial, pay_device_sn
        FROM u_admin_user
        WHERE real_name = #{realName}
            AND is_disabled = 0
        LIMIT 1
    </select>

    <select id="findByTelephone" parameterType="java.lang.String" resultMap="Result_AdminUser">
        SELECT id, username, real_name, gender, email, telephone, avatar, department_id, is_toll_man, maintain_man_type, login_path, is_loginable, bill_serial, pay_device_sn
        FROM u_admin_user
        WHERE telephone = #{telephone}
          AND is_disabled = 0
            LIMIT 1
    </select>

    <!-- 管理员统计 -->
    <select id="countAdminUser" parameterType="com.senox.user.vo.AdminUserSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(u.id) FROM u_admin_user u
            LEFT JOIN u_department d ON u.department_id = d.id
        <where>
            <if test="username != null and username != ''">
                AND u.username LIKE CONCAT('%', #{username}, '%')
            </if>
            <if test="realName != null and realName != ''">
                AND u.real_name LIKE CONCAT('%', #{realName}, '%')
            </if>
            <if test="telephone != null and telephone != ''">
                AND u.telephone LIKE CONCAT('%', #{telephone}, '%')
            </if>
            <if test="maintainManType != null and maintainManType.size() > 0">
                AND u.maintain_man_type IN <foreach collection="maintainManType" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="isMaintainManager">
                AND (
                        (
                            u.maintain_man_type IN ( 1, 2 )
                            <if test="departmentId != null">
                                AND ( u.department_id = #{departmentId} OR d.parent_id = #{departmentId} )
                            </if>
                        )
                        OR u.maintain_man_type = 2
                    )
            </if>
            AND u.is_disabled = 0
        </where>
    </select>

    <!-- 管理员列表 -->
    <select id="listAdminUser" parameterType="com.senox.user.vo.AdminUserSearchVo" resultMap="Result_AdminUserVo">
        SELECT u.id, u.username, u.real_name, u.gender, u.email, u.telephone, u.department_id, IFNULL(d.full_name, d.name) AS department_name
            , u.is_toll_man, u.maintain_man_type, u.login_path, u.is_loginable, u.bill_serial, u.pay_device_sn
        FROM u_admin_user u
            LEFT JOIN u_department d ON u.department_id = d.id
        <where>
            <if test="username != null and username != ''">
                AND u.username LIKE CONCAT('%', #{username}, '%')
            </if>
            <if test="realName != null and realName != ''">
                AND u.real_name LIKE CONCAT('%', #{realName}, '%')
            </if>
            <if test="telephone != null and telephone != ''">
                AND u.telephone LIKE CONCAT('%', #{telephone}, '%')
            </if>
            <if test="maintainManType != null and maintainManType.size() > 0">
                AND u.maintain_man_type IN <foreach collection="maintainManType" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="isMaintainManager">
                AND (
                        (
                            u.maintain_man_type IN ( 1, 2 )
                            <if test="departmentId != null">
                                AND ( u.department_id = #{departmentId} OR d.parent_id = #{departmentId} )
                            </if>
                        )
                        OR u.maintain_man_type = 2
                    )
            </if>
            AND u.is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY u.id DESC
            </otherwise>
        </choose>
        LIMIT ${offset}, ${pageSize}
    </select>

    <!-- 批量添加用户角色 -->
    <insert id="batchAddAdminUserRole" parameterType="com.senox.user.domain.AdminUserRole">
        INSERT INTO u_admin_user_role(
            user_id, role_id, creator_id, creator_name, create_time, modifier_id, modifier_name, modified_time
        ) VALUES
        <foreach collection="userRoles" item="item" separator=",">
        (
            #{item.userId}, #{item.roleId}, #{item.creatorId}, #{item.creatorName}, NOW(), #{item.modifierId}, #{item.modifierName}, NOW()
        )
        </foreach>
    </insert>

    <!--批量删除用户角色 -->
    <delete id="batchDelAdminUserRole">
        DELETE FROM u_admin_user_role
        WHERE user_id = #{userId}
            AND role_id IN <foreach collection="roles" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </delete>

    <!-- 判断角色占用情况 -->
    <select id="countAdminUserRole" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM u_admin_user_role WHERE role_id = #{roleId} AND is_disabled = 0
    </select>

    <!-- 用户角色列表 -->
    <select id="listAdminUserRole" parameterType="java.lang.Long" resultType="java.lang.Long">
        SELECT role_id FROM u_admin_user_role WHERE user_id = #{userId} AND is_disabled = 0
    </select>

    <!-- 用户角色编码列表 -->
    <select id="listAdminUserRoleCode" parameterType="java.lang.Long" resultType="java.lang.String">
        SELECT r.code FROM u_admin_user_role ur inner join u_role r on ur.role_id = r.id
            WHERE ur.user_id = #{userId} AND ur.is_disabled = 0
    </select>

    <!-- 角色用户列表 -->
    <select id="listRoleAdminUser" parameterType="java.lang.Long" resultType="com.senox.user.vo.AdminUserVo">
        select au.id,au.username,au.real_name  from u_admin_user_role aur inner join u_admin_user au on au.id = aur.user_id where aur.role_id = #{roleId} and au.is_disabled = 0
    </select>

    <!-- 用户部门批量添加 -->
    <insert id="batchAddUserDepartment">
        INSERT INTO u_admin_user_department(user_id, department_id) VALUES
        <foreach collection="departmentIds" item="item" separator=",">
        (#{userId}, #{item})
        </foreach>
    </insert>

    <!-- 批量添加 -->
    <update id="batchDelUserDepartment">
        DELETE FROM u_admin_user_department
        WHERE user_id = #{userId}
            AND department_id IN <foreach collection="departmentIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </update>

    <!-- 用户部门id列表 -->
    <select id="listUserDepartmentId" parameterType="java.lang.Long" resultType="java.lang.Long">
        SELECT department_id FROM u_admin_user_department WHERE user_id = #{userId}
    </select>
</mapper>

