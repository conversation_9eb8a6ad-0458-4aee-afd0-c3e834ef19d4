package com.senox.user.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.user.domain.Role;
import com.senox.user.service.RoleService;
import com.senox.user.vo.RoleVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/1/28 16:44
 */
@RestController
@RequestMapping("/role")
public class RoleController extends BaseController {

    @Autowired
    private RoleService roleService;

    @ApiOperation("添加角色")
    @PostMapping("/add")
    public Long addRole(@Validated({Add.class}) @RequestBody RoleVo roleVo) {
        Role role = roleVo2Entity(roleVo);
        initEntityCreator(role);
        initEntityModifier(role);
        return roleService.addRole(role, roleVo.getCosList());
    }

    @ApiOperation("更新角色")
    @PostMapping("/update")
    public void updateRole(@Validated({Update.class}) @RequestBody RoleVo roleVo) {
        if (roleVo.getId() < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        Role role = roleVo2Entity(roleVo);
        initEntityModifier(role);
        roleService.updateRole(role, roleVo.getCosList());
    }

    @ApiOperation("删除角色")
    @PostMapping("/delete/{id}")
    public void deleteRole(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        roleService.deleteRole(id);
    }

    @ApiOperation("获取角色")
    @GetMapping("/get/{id}")
    public RoleVo getRole(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        // 角色
        Role role = roleService.findRoleById(id);
        RoleVo result = role == null ? null : role2Vo(role);

        // 权限
        if (role != null) {
            result.setCosList(roleService.listRoleCos(id));
        }
        return result;
    }

    @ApiOperation("角色列表")
    @PostMapping("/list")
    public List<RoleVo> listRole() {
        List<Role> roleList = roleService.listRoles();
        return roleList == null ? Collections.emptyList()
                : roleList.stream().map(this::role2Vo).collect(Collectors.toList());
    }

    /**
     * 角色视图转实体
     * @param role
     * @return
     */
    private Role roleVo2Entity(RoleVo role) {
        Role result = new Role();
        result.setId(role.getId());
        result.setName(role.getName());
        result.setCode(role.getCode());
        return result;
    }

    /**
     * 角色转视图对象
     * @param role
     * @return
     */
    private RoleVo role2Vo(Role role) {
        RoleVo result = new RoleVo();
        result.setId(role.getId());
        result.setName(role.getName());
        result.setCode(role.getCode());
        return result;
    }
}
