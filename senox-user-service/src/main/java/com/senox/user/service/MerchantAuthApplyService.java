package com.senox.user.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.DesensitizeUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.AuditStatus;
import com.senox.common.vo.AuditVo;
import com.senox.user.component.BicycleChargesComponent;
import com.senox.user.constant.UserConst;
import com.senox.user.domain.Merchant;
import com.senox.user.domain.MerchantAuthApply;
import com.senox.user.mapper.MerchantAuthApplyMapper;
import com.senox.user.vo.MerchantAuthApplyListVo;
import com.senox.user.vo.MerchantAuthApplySearchVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-10-31
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MerchantAuthApplyService extends ServiceImpl<MerchantAuthApplyMapper, MerchantAuthApply> {

    private final RabbitTemplate rabbitTemplate;
    private final MerchantService merchantService;
    private final BicycleChargesComponent bicycleChargesComponent;

    /**
     * 添加商户权限申请
     * @param apply 申请参数
     */
    public Long addApply(MerchantAuthApply apply) {
        prepareMerchantAuthApply(apply);

        apply.setDisabled(Boolean.FALSE);
        apply.setApplyTime(LocalDateTime.now());
        apply.setCreateTime(LocalDateTime.now());
        apply.setModifierId(apply.getCreatorId());
        apply.setModifierName(apply.getCreatorName());
        apply.setModifiedTime(LocalDateTime.now());
        save(apply);
        return apply.getId();
    }

    /**
     * 更新商户权限申请
     * @param apply
     */
    public void updateApply(MerchantAuthApply apply) {
        if (!WrapperClassUtils.biggerThanLong(apply.getId(), 0L)) {
            return;
        }

        // 审批过的单据不可调整
        MerchantAuthApply dbApply = findById(apply.getId());
        if (AuditStatus.fromStatus(dbApply.getAuditStatus()) != AuditStatus.PENDING) {
            throw new BusinessException("单据已审核，无法修改");
        }

        prepareMerchantAuthApply(apply);
        apply.setApplyTime(null);
        apply.setDisabled(null);
        apply.setCreatorId(null);
        apply.setCreatorName(null);
        apply.setCreateTime(null);
        apply.setModifiedTime(LocalDateTime.now());
        updateById(apply);
    }

    /**
     * 删除申请
     * @param id
     */
    public void deleteApply(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        MerchantAuthApply dbApply = findById(id);
        if (AuditStatus.fromStatus(dbApply.getAuditStatus()) != AuditStatus.PENDING) {
            throw new BusinessException("单据已审核，无法修改");
        }

        LambdaQueryWrapper<MerchantAuthApply> wrapper = new QueryWrapper<MerchantAuthApply>().lambda()
                .eq(MerchantAuthApply::getId, id)
                .eq(MerchantAuthApply::getAuditStatus, AuditStatus.PENDING.getValue());
        remove(wrapper);
    }

    /**
     * 审核商户权限申请
     *
     * @param audit
     */
    @Transactional(rollbackFor = Exception.class)
    public Merchant auditApply(AuditVo audit) {
        MerchantAuthApply apply = findById(audit.getId());
        if (AuditStatus.fromStatus(apply.getAuditStatus()) != AuditStatus.PENDING) {
            throw new BusinessException("申请已审批");
        }

        Merchant merchant = null;
        // 审批通过，添加商户权限
        if (audit.getStatus() == AuditStatus.PASS) {

            if (!StringUtils.isBlank(apply.getRcSerial())) {
                merchant = merchantService.findByRcSerial(apply.getRcSerial());
            } else if (WrapperClassUtils.biggerThanLong(apply.getMerchantId(), 0L)) {
                merchant = merchantService.findById(apply.getMerchantId());
            } else {
                merchant = merchantService.findByName(apply.getMerchantName());
            }

            if (merchant != null) {
                // 商户已存在，授予三轮车配送权限
                Merchant authMerchant = new Merchant();
                authMerchant.setId(merchant.getId());
                authMerchant.setBicycleAuth(BooleanUtils.isTrue(apply.getBicycleAuth()));
                if (!StringUtils.isBlank(apply.getAddress())) {
                    authMerchant.setAddress(apply.getAddress());
                }
                authMerchant.setModifierId(audit.getOperatorId());
                authMerchant.setModifierName(audit.getOperatorName());
                authMerchant.setModifiedTime(LocalDateTime.now());
                merchantService.update(authMerchant);

            } else {
                // 新增商户，并授予三轮车权限
                merchant = new Merchant();
                merchant.setName(apply.getMerchantName());
                merchant.setContact(apply.getContact());
                merchant.setIdcard(apply.getIdcard());
                merchant.setAddress(apply.getAddress());
                merchant.setRcSerial(apply.getRcSerial());
                merchant.setReferralCode(apply.getReferralCode());
                //设置当前默认收费标准
                merchant.setBicycleChargesId(bicycleChargesComponent.getCurrentEffectiveCharges().getId());
                merchant.setBicycleAuth(BooleanUtils.isTrue(apply.getBicycleAuth()));
                merchant.setDryAuth(BooleanUtils.isTrue(apply.getDryAuth()));
                merchant.setCreatorId(audit.getOperatorId());
                merchant.setCreatorName(audit.getOperatorName());
                merchantService.add(merchant);
            }
        }

        // 审批
        MerchantAuthApply auditApply = new MerchantAuthApply();
        auditApply.setId(audit.getId());
        auditApply.setMerchantId(merchant == null ? null : merchant.getId());
        auditApply.setAuditStatus(audit.getStatus().getValue());
        auditApply.setAuditRemark(audit.getRemark());
        auditApply.setAuditId(audit.getOperatorId());
        auditApply.setAuditTime(LocalDateTime.now());
        auditApply.setModifierId(audit.getOperatorId());
        auditApply.setModifierName(audit.getOperatorName());
        auditApply.setModifiedTime(LocalDateTime.now());
        updateById(auditApply);

        rabbitTemplate.convertAndSend(UserConst.MQ.MQ_MERCHANT_APPLY_AUDIT, audit);
        return merchant;
    }


    /**
     * 根据id获取申请
     *
     * @param id id
     * @return 返回获取到的申请
     */
    public MerchantAuthApply findById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? getById(id) : null;
    }


    /**
     * 列表总数
     *
     * @param search 查询参数
     * @return 返回列表总数
     */
    public int countApply(MerchantAuthApplySearchVo search) {
        return getBaseMapper().countApply(search);
    }

    /**
     * 列表
     *
     * @param search 查询参数
     * @return 返回列表
     */
    public List<MerchantAuthApplyListVo> listApply(MerchantAuthApplySearchVo search) {
        List<MerchantAuthApplyListVo>  result = getBaseMapper().listApply(search);
        if (BooleanUtils.isTrue(search.getDesensitized()) && !CollectionUtils.isEmpty(result)) {
            for (MerchantAuthApplyListVo item : result) {
                item.setIdcard(DesensitizeUtils.desensitizeIdcard(item.getIdcard()));
                item.setContact(DesensitizeUtils.desensitizeIdcard(item.getContact()));
            }
        }
        return result;
    }


    /**
     * 待审核数量
     * @return
     */
    public Integer auditCount() {
        return getBaseMapper().auditCount();
    }

    /**
     * 商户权限申请
     * @param apply
     */
    private void prepareMerchantAuthApply(MerchantAuthApply apply) {
        if (apply.getMerchantId() == null) {
            apply.setMerchantId(0L);
        }
        if (StringUtils.isBlank(apply.getCreateOpenid())) {
            apply.setCreateOpenid(StringUtils.EMPTY);
        }

        apply.setAuditStatus(AuditStatus.PENDING.getValue());
        apply.setAuditId(0L);
        apply.setDisabled(Boolean.FALSE);
    }
}
