package com.senox.user.handler;

import com.senox.user.constant.MerchantBillSettlePeriod;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @date 2023-11-14
 */
public class MerchantBillSettlePeriodTypeHandler extends BaseTypeHandler<MerchantBillSettlePeriod> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, MerchantBillSettlePeriod parameter, JdbcType jdbcType) throws SQLException {
        ps.setInt(i, parameter.getNumber());
    }

    @Override
    public MerchantBillSettlePeriod getNullableResult(ResultSet rs, String columnName) throws SQLException {
        int value = rs.getInt(columnName);
        return rs.wasNull() ? null : MerchantBillSettlePeriod.fromNumber(value);
    }

    @Override
    public MerchantBillSettlePeriod getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        int value = rs.getInt(columnIndex);
        return rs.wasNull() ? null : MerchantBillSettlePeriod.fromNumber(value);
    }

    @Override
    public MerchantBillSettlePeriod getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        int value = cs.getInt(columnIndex);
        return cs.wasNull() ? null : MerchantBillSettlePeriod.fromNumber(value);
    }
}
