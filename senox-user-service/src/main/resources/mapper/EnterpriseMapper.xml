<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.user.mapper.EnterpriseMapper">

    <!-- 删除企业 -->
    <update id="deleteEnterprise" parameterType="com.senox.user.domain.Enterprise">
        UPDATE u_enterprise
        <set>
            name = concat(name, "_", id)
            , is_disabled = 1
            <if test="modifierId != null">
                , modifier_id = #{modifierId}
            </if>
            <if test="modifierName != null">
                , modifier_name = #{modifierName}
            </if>
            , modified_time = NOW()
        </set>
        WHERE id = #{id} AND is_disabled = 0
    </update>

    <!-- 企业合计 -->
    <select id="countEnterprise" parameterType="com.senox.user.vo.EnterpriseSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM u_enterprise e
        <where>
            <if test="category != null and category != ''">
                AND e.category = #{category}
            </if>
            <if test="firefightingEmphasis != null">
                AND e.is_firefighting_emphasis = #{firefightingEmphasis}
            </if>
            <if test="name != null and name != ''">
                AND (e.name LIKE CONCAT('%', #{name}, '%') OR e.full_name LIKE CONCAT('%', #{name}, '%'))
            </if>
            <if test="chargeMan != null and chargeMan != ''">
                AND e.charge_man LIKE CONCAT('%', #{chargeMan}, '%')
            </if>
            <if test="contact != null and contact != ''">
            <choose>
                <when test="contact.length() > 10">
                    AND (e.contact1 = #{contact} OR e.contact2 = #{contact})
                </when>
                <otherwise>
                    AND (e.contact1 LIKE CONCAT('%', #{contact}, '%') OR e.contact2 LIKE CONCAT('%', #{contact}, '%'))
                </otherwise>
            </choose>
            </if>
            <if test="(realtySerial != null and realtySerial != '') or regionId != null or streetId != null or (address != null and address != '')">
                AND EXISTS (
                    SELECT er.id
                    FROM u_enterprise_realty er
                    <if test="regionId != null or streetId != null or (address != null and address != '')">
                        INNER JOIN r_realty r ON r.serial_no = er.realty_serial
                    </if>
                    WHERE er.enterprise_id = e.id
                    <if test="realtySerial != null and realtySerial != ''">
                        <if test="realtySerial != null and realtySerial != ''">
                            <choose>
                                <when test="realtySerial.length() >= 9">
                                    AND er.realty_serial = #{realtySerial}
                                </when>
                                <otherwise>
                                    AND er.realty_serial LIKE CONCAT('%', #{realtySerial}, '%')
                                </otherwise>
                            </choose>
                        </if>
                    </if>
                    <if test="regionId != null">
                        AND r.region_id = #{regionId}
                    </if>
                    <if test="streetId != null">
                        AND r.street_id = #{streetId}
                    </if>
                    <if test="address != null">
                        AND r.name LIKE CONCAT('%', #{address} , '%')
                    </if>
                )
            </if>
            AND e.is_disabled = 0
        </where>
    </select>

    <!-- 企业列表 -->
    <select id="listEnterprise" parameterType="com.senox.user.vo.EnterpriseSearchVo" resultType="com.senox.user.vo.EnterpriseViewVo">
        SELECT e.id, e.name, e.full_name, e.charge_man, e.contact1, e.contact2, e.category, c.description AS category_desc
            , e.other_category, e.is_firefighting_emphasis AS firefighting_emphasis, e.address, e.remark
        FROM u_enterprise e
            LEFT JOIN dict_business_category c ON c.code = e.category
        <where>
            <if test="category != null and category != ''">
                AND e.category = #{category}
            </if>
            <if test="firefightingEmphasis != null">
                AND e.is_firefighting_emphasis = #{firefightingEmphasis}
            </if>
            <if test="name != null and name != ''">
                AND (e.name LIKE CONCAT('%', #{name}, '%') OR e.full_name LIKE CONCAT('%', #{name}, '%'))
            </if>
            <if test="chargeMan != null and chargeMan != ''">
                AND e.charge_man LIKE CONCAT('%', #{chargeMan}, '%')
            </if>
            <if test="contact != null and contact != ''">
            <choose>
                <when test="contact.length() > 10">
                    AND (e.contact1 = #{contact} OR e.contact2 = #{contact})
                </when>
                <otherwise>
                    AND (e.contact1 LIKE CONCAT('%', #{contact}, '%') OR e.contact2 LIKE CONCAT('%', #{contact}, '%'))
                </otherwise>
            </choose>
            </if>
            <if test="(realtySerial != null and realtySerial != '') or regionId != null or streetId != null or (address != null and address != '')">
                AND EXISTS (
                SELECT er.id
                FROM u_enterprise_realty er
                <if test="regionId != null or streetId != null or (address != null and address != '')">
                    INNER JOIN r_realty r ON r.serial_no = er.realty_serial
                </if>
                WHERE er.enterprise_id = e.id
                <if test="realtySerial != null and realtySerial != ''">
                    <choose>
                        <when test="realtySerial.length() >= 9">
                            AND er.realty_serial = #{realtySerial}
                        </when>
                        <otherwise>
                            AND er.realty_serial LIKE CONCAT('%', #{realtySerial}, '%')
                        </otherwise>
                    </choose>
                </if>
                <if test="regionId != null">
                    AND r.region_id = #{regionId}
                </if>
                <if test="streetId != null">
                    AND r.street_id = #{streetId}
                </if>
                <if test="address != null">
                    AND r.name LIKE CONCAT('%', #{address} , '%')
                </if>
                )
            </if>
            AND e.is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY e.id DESC
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>
</mapper>