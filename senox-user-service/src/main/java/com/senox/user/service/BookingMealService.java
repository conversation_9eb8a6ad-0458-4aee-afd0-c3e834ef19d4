package com.senox.user.service;

import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.PageUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.domain.BookingMeal;
import com.senox.user.domain.BookingMealCompanyDayReport;
import com.senox.user.domain.Company;
import com.senox.user.mapper.BookingMealMapper;
import com.senox.user.vo.BookingMealDayReportSearchVo;
import com.senox.user.vo.BookingMealSearchVo;
import com.senox.user.vo.CompanyVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/4/6 10:09
 */
@Service
public class BookingMealService {

    private static final Logger logger = LoggerFactory.getLogger(BookingMealService.class);

    @Autowired
    private CompanyService companyService;
    @Autowired
    private BookingMealMapper bookingMealMapper;

    /**
     * 订餐分页
     * @param searchVo
     * @return
     */
    public PageResult<BookingMeal> listBookingPage(BookingMealSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }

        // 总记录数
        int totalSize = bookingMealMapper.countBooking(searchVo);
        searchVo.prepare();

        if (totalSize <= searchVo.getOffset()) {
            return PageResult.emptyPage();
        }

        // list
        List<BookingMeal> resultList = bookingMealMapper.listBooking(searchVo);
        return PageUtils.resultPage(searchVo, totalSize, resultList);
    }

    /**
     * 批量添加就餐预定
     * @param bookingList
     * @return
     */
    public int batchAddBookings(List<BookingMeal> bookingList) {
        if (CollectionUtils.isEmpty(bookingList)) {
            return 0;
        }
        return bookingMealMapper.batchAddBookings(bookingList);
    }

    /**
     * 公司日报
     * @param searchVo
     * @return
     */
    public List<BookingMealCompanyDayReport> listCompanyDayReportRealTime(BookingMealDayReportSearchVo searchVo) {
        // 代理公司列表
        List<Company> delegateCompanies = companyService.listDelegateCompany();
        if (!CollectionUtils.isEmpty(delegateCompanies)) {
            searchVo.setDelegateCompanies(delegateCompanies.stream().map(this::company2Vo).collect(Collectors.toList()));
        }
        logger.info("Booking meal company day report param: {}", JsonUtils.object2Json(searchVo));

        // 自报餐的公司日报
        List<BookingMealCompanyDayReport> employeeReport = bookingMealMapper.listCompanyDayReport(searchVo);
        logger.info("employee day report: {}", JsonUtils.object2Json(employeeReport));

        // 代报餐的公司日报
        List<BookingMealCompanyDayReport> companyReport = null;
        if (!CollectionUtils.isEmpty(delegateCompanies)) {
            companyReport = bookingMealMapper.listDelegateBooking(searchVo);
        } else {
            companyReport = Collections.emptyList();
        }
        logger.info("company day report: {}", JsonUtils.object2Json(companyReport));

        // 结果
        List<BookingMealCompanyDayReport> resultList = new ArrayList<>(employeeReport.size() + delegateCompanies.size());
        resultList.addAll(employeeReport);
        resultList.addAll(companyReport);
        return resultList;
    }

    private CompanyVo company2Vo(Company company) {
        CompanyVo result = new CompanyVo();
        BeanUtils.copyProperties(company, result);
        return result;
    }
}
