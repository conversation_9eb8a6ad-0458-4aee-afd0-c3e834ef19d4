package com.senox.context;

import java.util.Objects;

/**
 * 管理后台上下文
 * <AUTHOR>
 * @Date 2020/12/22 15:49
 */
public class AdminContext {

    /**
     * 请求头
     */
    public static final String HEADER_AUTHORIZATION = "X-SENOX-AUTHORIZATION";
    public static final String HEADER_WECHAT_MP = "X-SENOX-MP";
    public static final String HEADER_WECHAT_AUTH = "X-SENOX-WECHAT";
    public static final String HEADER_AUTH_CREDENTIALS = "X-SENOX-AUTH_CREDENTIALS";
    public static final String HEADER_ACCESS_TOKEN = "X-SENOX-ACCESS-TOKEN";
    public static final String HEADER_FEIGN_TOKEN = "X-SENOX-FEIGN-TOKEN";

    private static ThreadLocal<AdminUserDto> adminUser = new ThreadLocal<>();

    private static ThreadLocal<PluginEnv> pluginEnv = new ThreadLocal<>();

    public static AdminUserDto getUser() {
        return adminUser.get();
    }

    public static void setUser(AdminUserDto user) {
        adminUser.set(user);
    }

    public static void setPluginEnv(PluginEnv env) {
        pluginEnv.set(env);
    }

    public static PluginEnv getPluginEnv() {
        return pluginEnv.get();
    }

    /**
     * 清楚线程变量
     */
    public static void clear() {
        adminUser.remove();
        pluginEnv.remove();
    }

    /**
     * 上下文用户信息是否有效
     * @return
     */
    public static boolean isUserValid() {
        return !Objects.isNull(getUser()) && getUser().isValid();
    }

    /**
     * 是否需要mock
     * @return
     */
    public static boolean isNeedMock() {
        return !Objects.isNull(getPluginEnv()) && getPluginEnv().isNeedMock();
    }
}
