package com.senox.user.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.domain.ReservationRecord;
import com.senox.user.domain.ReservationRecordItem;
import com.senox.user.mapper.ReservationRecordMapper;
import com.senox.user.vo.ReservationRecordSearchVo;
import com.senox.user.vo.ReservationRecordVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/27 17:14
 */
@Service
@RequiredArgsConstructor
public class ReservationRecordService extends ServiceImpl<ReservationRecordMapper, ReservationRecord> {

    private final ReservationRecordItemService reservationRecordItemService;

    /**
     * 添加预约记录
     * @param record
     * @param recordItems
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addReservationRecord(ReservationRecord record, List<ReservationRecordItem> recordItems) {
        if (record.getType() == null) {
            record.setType(0);
        }
        //校验重复预约
        checkReservationRecord(record.getType(), record.getCreateOpenid(), record.getVisitTimeStart(), record.getVisitTimeEnd());
        record.setModifiedTime(LocalDateTime.now());
        record.setCreateTime(LocalDateTime.now());
        if (save(record)) {
            reservationRecordItemService.addReservationRecordItem(record.getId(), recordItems);
            return record.getId();
        }
        return 0L;
    }

    /**
     * 更新预约记录
     * @param record
     * @param recordItems
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateReservationRecord(ReservationRecord record, List<ReservationRecordItem> recordItems) {
        if (!WrapperClassUtils.biggerThanLong(record.getId(), 0L)) {
            throw new InvalidParameterException();
        }
        record.setModifiedTime(LocalDateTime.now());
        if (updateById(record)) {
            reservationRecordItemService.addReservationRecordItem(record.getId(), recordItems);
        }
    }


    /**
     * 根据id预约记录详细
     * @param id
     * @return
     */
    public ReservationRecordVo findInfoById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return getBaseMapper().findInfoById(id);
    }

    /**
     * 预约记录列表
     * @param searchVo
     * @return
     */
    public PageResult<ReservationRecordVo> page(ReservationRecordSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return PageUtils.commonPageResult(searchVo, () -> getBaseMapper().countReservationRecord(searchVo), () -> getBaseMapper().listReservationRecord(searchVo));
    }

    /**
     * 预约记录随行人总数
     * @param searchVo
     * @return
     */
    public ReservationRecordVo sumReservationRecord(ReservationRecordSearchVo searchVo) {
        return getBaseMapper().sumReservationRecord(searchVo);
    }

    /**
     * 根据openid和时间查询预约记录
     * @param type
     * @param openid
     * @param visitTimeStart
     * @param visitTimeEnd
     * @return
     */
    public ReservationRecord findByOpenidAndTime(Integer type, String openid, LocalDateTime visitTimeStart, LocalDateTime visitTimeEnd) {
        return getOne(new QueryWrapper<ReservationRecord>().lambda()
                .eq(ReservationRecord::getType, type)
                .eq(ReservationRecord::getCreateOpenid, openid)
                .eq(ReservationRecord::getVisitTimeStart, visitTimeStart)
                .eq(ReservationRecord::getVisitTimeEnd, visitTimeEnd));
    }

    /**
     * 校验预约记录是否重复
     * @param type
     * @param openid
     * @param visitTimeStart
     * @param visitTimeEnd
     */
    private void checkReservationRecord(Integer type, String openid, LocalDateTime visitTimeStart, LocalDateTime visitTimeEnd) {
        ReservationRecord reservationRecord = findByOpenidAndTime(type, openid, visitTimeStart, visitTimeEnd);
        if (reservationRecord != null) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR, "已经预约过了");
        }
    }
}
