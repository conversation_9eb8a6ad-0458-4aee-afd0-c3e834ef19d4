<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.user.mapper.ResidentMapper">

    <!-- 获取最大的退货单号 -->
    <select id="findMaxResidentNo" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT MAX(resident_no) FROM u_resident WHERE resident_no LIKE CONCAT(#{prefix}, '%');
    </select>


    <select id="count" parameterType="com.senox.user.vo.ResidentSearchVo" resultType="int">
        SELECT count(r.id)
        FROM u_resident r
        <where>
            <if test="null != residentNo and residentNo != ''">
                AND r.resident_no = #{residentNo}
            </if>
            <if test="name != null and name != ''">
                AND r.name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="idNum != null and idNum != ''">
                AND r.id_num LIKE CONCAT('%', #{idNum}, '%')
            </if>
            <if test="null != residentType">
                AND r.resident_type = #{residentType}
            </if>
            <if test="deviceId != null or (realtySerial != null and realtySerial != '')">
                AND EXISTS (
                    SELECT ra.id
                    FROM u_resident_access ra
                    WHERE ra.resident_no = r.resident_no AND ra.access = 1 AND ra.disabled = 0
                    <if test="deviceId != null">
                        AND ra.device_id = #{deviceId}
                    </if>
                    <if test="realtySerial != null and realtySerial != ''">
                        AND ra.realty_serial LIKE CONCAT('%', #{realtySerial}, '%')
                    </if>
                )
            </if>
            AND r.disabled = 0
        </where>
    </select>

    <select id="list" parameterType="com.senox.user.vo.ResidentSearchVo" resultType="com.senox.user.domain.Resident">
        SELECT r.id, r.resident_no, r.resident_type, r.name, r.id_num, r.born_date, r.gender, r.nature, r.telephone
            , r.address, r.face_url, r.remark, r.create_time
        FROM u_resident r
        <where>
            <if test="null != residentNo and residentNo != ''">
                and r.resident_no = #{residentNo}
            </if>
            <if test="name != null and name != ''">
                AND r.name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="idNum != null and idNum != ''">
                AND r.id_num LIKE CONCAT('%', #{idNum}, '%')
            </if>
            <if test="null != residentType">
                AND r.resident_type = #{residentType}
            </if>
            <if test="deviceId != null or (realtySerial != null and realtySerial != '')">
                AND EXISTS (
                    SELECT ra.id
                    FROM u_resident_access ra
                    WHERE ra.resident_no = r.resident_no AND ra.access = 1 AND ra.disabled = 0
                    <if test="deviceId != null">
                        AND ra.device_id = #{deviceId}
                    </if>
                    <if test="realtySerial != null and realtySerial != ''">
                        AND ra.realty_serial LIKE CONCAT('%', #{realtySerial}, '%')
                    </if>
                )
            </if>
            AND r.disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY r.id DESC
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>
</mapper>