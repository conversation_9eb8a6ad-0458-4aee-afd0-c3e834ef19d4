package com.senox.user.service;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.dm.constant.EmployeeType;
import com.senox.dm.vo.AccessControlVo;
import com.senox.dm.vo.AccessDeviceRightVo;
import com.senox.dm.vo.EmployeeAccessStateVo;
import com.senox.user.component.AccessControlComponent;
import com.senox.user.domain.ResidentAccess;
import com.senox.user.event.ResidentAccessSyncEvent;
import com.senox.user.mapper.ResidentAccessMapper;
import com.senox.user.utils.ContextUtils;
import com.senox.user.vo.ResidentAccessResultVo;
import com.senox.user.vo.ResidentAccessVo;
import com.senox.user.vo.ResidentDeviceAccessVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/22 15:41
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ResidentAccessService extends ServiceImpl<ResidentAccessMapper, ResidentAccess> {

    private final AccessControlComponent accessControlComponent;
    private final ApplicationEventPublisher eventPublisher;

    /**
     * 给住户添加权限
     *
     * @param residentNo
     * @param realtySerial
     * @param accessList
     */
    @Transactional(rollbackFor = Exception.class)
    public void addResidentAccess(String residentNo, String realtySerial, List<ResidentAccess> accessList) {
        accessList = accessList.stream().distinct().collect(Collectors.toList());
        List<ResidentAccess> list = residentAccessList(residentNo, realtySerial, 0L);
        DataSepDto<ResidentAccess> sepData = compareAndSeparateResidentAccess(list, accessList);
        ResidentAccessResultVo result = getBaseMapper().residentAccessResultByNo(residentNo, Boolean.FALSE);
        if (!CollectionUtils.isEmpty(sepData.getAddList())) {
            sepData.getAddList().forEach(item -> {
                item.setState(Boolean.FALSE);
                item.setCreateTime(LocalDateTime.now());
                item.setModifiedTime(LocalDateTime.now());
            });
            saveBatch(sepData.getAddList());
            if (!StringUtils.isBlank(result.getFaceUrl())) {
                //下发权限调用
                accessRight(residentNo, sepData.getAddList(), Boolean.FALSE);
            }
        }
        //权限已存在，则添加记录
        if (!CollectionUtils.isEmpty(sepData.getRemoveList()) && !StringUtils.isBlank(result.getFaceUrl())) {
            //删除住户权限
            deleteResidentAccess(sepData.getRemoveList().stream().map(ResidentAccess::getId).collect(Collectors.toList()));
        }
    }

    /**
     * 根据设备Id查询所拥有的权限列表
     * @param deviceId
     * @return
     */
    public List<ResidentAccessVo> listResidentAccessByDeviceId(Long deviceId) {
        return getBaseMapper().listResidentAccessByDeviceId(deviceId);
    }

    /**
     * 门禁权限同步
     * @param deviceId
     * @param targetDeviceId
     */
    public void residentAccessSync(Long deviceId, Long targetDeviceId) {
        List<ResidentAccessVo> srcList = listResidentAccessByDeviceId(deviceId);
        if (CollectionUtils.isEmpty(srcList)) {
            log.info("【设备未查询到权限记录】同步结束");
            return;
        }
        List<ResidentAccessVo> targetList = listResidentAccessByDeviceId(targetDeviceId);
        List<ResidentAccessVo> accessVoList = compare(srcList, targetList);
        accessVoList.forEach(access-> access.setDeviceId(targetDeviceId));
        log.info("同步的用户编号 {}", JsonUtils.object2Json(accessVoList.stream().map(ResidentAccessVo::getResidentNo).collect(Collectors.toList())));
        List<ResidentAccess> accessList = buildAccessList(accessVoList, targetDeviceId);
        boolean result = saveBatch(accessList);
        if (result && !CollectionUtils.isEmpty(accessList)) {
            log.info("【publish 门禁权限下发事件】");
            eventPublisher.publishEvent(new ResidentAccessSyncEvent(this, accessVoList));
        }
    }

    /**
     * 比较
     * @param srcList
     * @param targetList
     * @return
     */
    private List<ResidentAccessVo> compare(List<ResidentAccessVo> srcList, List<ResidentAccessVo> targetList) {
        if (CollectionUtils.isEmpty(targetList)) {
            return srcList;
        }
        return srcList.stream()
                .filter(src -> targetList.stream()
                        .noneMatch(target -> target.getResidentNo().equals(src.getResidentNo())))
                .collect(Collectors.toList());
    }

    /**
     * 构建权限记录
     * @param accessVoList
     * @param deviceId
     * @return
     */
    private List<ResidentAccess> buildAccessList(List<ResidentAccessVo> accessVoList, Long deviceId) {
        AccessControlVo controlVo = accessControlComponent.findAccessControlById(deviceId);
        if (controlVo == null) {
            return Collections.emptyList();
        }
        List<ResidentAccess> accessList = new ArrayList<>(accessVoList.size());
        for (ResidentAccessVo accessVo : accessVoList) {
            //重新设置ip
            accessVo.setDeviceIp(controlVo.getDeviceIp());
            ResidentAccess access = new ResidentAccess();
            access.setResidentNo(accessVo.getResidentNo());
            access.setDeviceId(deviceId);
            access.setRealtySerial(accessVo.getRealtySerial());
            access.setContractNo(accessVo.getContractNo());
            access.setAccess(Boolean.TRUE);
            access.setState(Boolean.FALSE);
            ContextUtils.initEntityCreator(access);
            ContextUtils.initEntityModifier(access);
            access.setCreateTime(LocalDateTime.now());
            access.setModifiedTime(LocalDateTime.now());
            accessList.add(access);
        }
        return accessList;
    }

    /**
     * 删除住户权限
     *
     * @param ids
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteResidentAccess(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new InvalidParameterException();
        }
        ids.forEach(x -> {
            ResidentAccess residentAccess = getById(x);
            if (residentAccess != null) {
                List<ResidentAccess> accessList = residentAccessList(residentAccess.getResidentNo(), null, residentAccess.getDeviceId());
                if (accessList.size() == 1) {
                    //下发权限调用
                    accessRight(residentAccess.getResidentNo(), Lists.newArrayList(residentAccess), Boolean.TRUE);
                }
                removeById(residentAccess.getId());
            }
        });

    }

    /**
     * 根据合同号移除住户权限
     * @param contractNo
     */
    public void deleteAccessByContractNo(String contractNo) {
        List<ResidentAccess> accesses = listAccessByContractNo(contractNo, false);
        if (CollectionUtils.isEmpty(accesses)) {
            log.info("【住户权限】暂无找到{}的住户权限", contractNo);
            return;
        }
        accesses.forEach(access -> {
            //设置弃用
            access.setDisabled(true);
            access.setModifiedTime(LocalDateTime.now());
            ContextUtils.initEntityModifier(access);
            //下发权限调用，调用设备取消用户门禁权限
            accessRight(access.getResidentNo(), Lists.newArrayList(access), Boolean.TRUE);
        });
        //更新权限信息
        updateBatchById(accesses);
    }

    /**
     * 续签合同
     * @param oldContractNo
     * @param newContractNo
     */
    @Transactional(rollbackFor = Exception.class)
    public void renewalAccess(String oldContractNo, String newContractNo) {
        List<ResidentAccess> accesses = listAccessByContractNo(oldContractNo, true);
        if (CollectionUtils.isEmpty(accesses)) {
            log.info("【住户权限】暂无找到续签{}的住户权限", oldContractNo);
            return;
        }
        //移除历史权限记录
        removeByIds(accesses.stream().map(ResidentAccess::getId).collect(Collectors.toList()));
        accesses.forEach(access -> {
            //设置启用及新合同
            access.setId(null);
            access.setAccess(true);
            access.setState(false);
            access.setDisabled(false);
            access.setContractNo(newContractNo);
            access.setModifiedTime(LocalDateTime.now());
            access.setCreateTime(LocalDateTime.now());
            ContextUtils.initEntityModifier(access);
            ContextUtils.initEntityCreator(access);
        });
        //更新权限信息
        saveBatch(accesses);
        //下发权限调用，调用设备添加用户门禁权限
        accesses.forEach(access -> accessRight(access.getResidentNo(), Lists.newArrayList(access), Boolean.FALSE));
    }


    /**
     * 根據合同号查找住户权限
     * @param contractNo
     * @param disable
     * @return
     */
    public List<ResidentAccess> listAccessByContractNo(String contractNo, Boolean disable) {
        return StringUtils.isBlank(contractNo)
                ? Collections.emptyList()
                : list(new QueryWrapper<ResidentAccess>().lambda()
                        .eq(ResidentAccess::getContractNo, contractNo)
                        .eq(ResidentAccess::getDisabled, disable));
    }

    /**
     * 根据住户编号查询住户的权限
     *
     * @param residentNo
     * @return
     */
    public ResidentAccessResultVo residentAccessResultByNo(String residentNo) {
        if (StringUtils.isBlank(residentNo)) {
            throw new InvalidParameterException();
        }
        ResidentAccessResultVo resultVo = getBaseMapper().residentAccessResultByNo(residentNo, Boolean.TRUE);
        if (resultVo == null) {
            return null;
        }
        List<ResidentAccessVo> accessVos = processResidentAccessVoList(resultVo.getResidentAccessVoList());


        accessVos = accessVos.stream().sorted(Comparator.comparing(ResidentAccessVo::getId)).collect(Collectors.toList());
        resultVo.setResidentAccessVoList(accessVos);
        return resultVo;
    }

    private List<ResidentAccessVo> processResidentAccessVoList(List<ResidentAccessVo> residentAccessVoList) {
        if (CollectionUtils.isEmpty(residentAccessVoList)) {
            return Collections.emptyList();
        }
        residentAccessVoList = residentAccessVoList.stream().filter(x -> !BooleanUtils.isTrue(x.getDisabled())).collect(Collectors.toList());
        List<ResidentAccessVo> accessVos = new ArrayList<>();
        Map<Long, Map<Long, Map<String, Map<String, List<ResidentAccessVo>>>>> groupedMap = residentAccessVoList.stream()
                .collect(Collectors.groupingBy(ResidentAccessVo::getStreetId,
                        Collectors.groupingBy(ResidentAccessVo::getRegionId,
                                Collectors.groupingBy(ResidentAccessVo::getRealtySerial,
                                        Collectors.groupingBy(ResidentAccessVo::getContractNo)))));
        groupedMap.forEach((streetId, streetMap) ->
                streetMap.forEach((regionId, regionMap) ->
                        regionMap.forEach((realtySerial, realtyMap) ->
                                realtyMap.forEach((contractNo, contractList) -> {
                                    ResidentAccessVo vo = buildResidentAccessVo(contractList);
                                    accessVos.add(vo);
                                }))));
        return accessVos;
    }

    private ResidentAccessVo buildResidentAccessVo(List<ResidentAccessVo> contractList) {
        ResidentAccessVo vo = contractList.get(0);
        List<ResidentDeviceAccessVo> deviceAccessVos = contractList.stream()
                .map(accessVo -> new ResidentDeviceAccessVo(accessVo.getId(), accessVo.getDeviceId(), accessVo.getDeviceName(), accessVo.getState()))
                .collect(Collectors.toList());
        vo.setDeviceAccessVos(deviceAccessVos);
        return vo;
    }

    /**
     * 根据住户编号查询权限
     *
     * @param residentNo
     * @return
     */
    public List<ResidentAccess> residentAccessByNo(String residentNo) {
        return StringUtils.isBlank(residentNo)
                ? Collections.emptyList()
                : list(new QueryWrapper<ResidentAccess>().lambda().eq(ResidentAccess::getResidentNo, residentNo));
    }

    /**
     * 更新权限状态
     *
     * @param stateVo
     */
    public void updateResidentAccessState(EmployeeAccessStateVo stateVo) {
        if (StringUtils.isBlank(stateVo.getEmployeeNo()) || !WrapperClassUtils.biggerThanLong(stateVo.getDeviceId(), 0L)) {
            throw new InvalidParameterException();
        }
        List<ResidentAccess> residentAccessList = residentAccessList(stateVo.getEmployeeNo(), null, stateVo.getDeviceId());
        if (CollectionUtils.isEmpty(residentAccessList)) {
            return;
        }
        residentAccessList.forEach(residentAccess -> {
            residentAccess.setState(Boolean.TRUE);
            residentAccess.setModifiedTime(LocalDateTime.now());
        });
        updateBatchById(residentAccessList);
    }

    /**
     * 根据住户编号和物业编号和设备号查询相关权限
     *
     * @param residentNo
     * @param realtySerial
     * @param deviceId
     * @return
     */
    public List<ResidentAccess> residentAccessList(String residentNo, String realtySerial, Long deviceId) {
        LambdaQueryWrapper<ResidentAccess> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isBlank(residentNo) && StringUtils.isBlank(realtySerial) && !WrapperClassUtils.biggerThanLong(deviceId, 0L)) {
            return Collections.emptyList();
        }
        if (!StringUtils.isBlank(residentNo)) {
            wrapper.eq(ResidentAccess::getResidentNo, residentNo);
        }
        if (realtySerial != null) {
            wrapper.eq(ResidentAccess::getRealtySerial, realtySerial);
        }
        if (WrapperClassUtils.biggerThanLong(deviceId, 0L)) {
            wrapper.eq(ResidentAccess::getDeviceId, deviceId);
        }
        wrapper.eq(ResidentAccess::getDisabled, Boolean.FALSE);
        return list(wrapper);
    }

    /**
     * 比较区分
     *
     * @param srcList
     * @param targetList
     * @return
     */
    public DataSepDto<ResidentAccess> compareAndSeparateResidentAccess(List<ResidentAccess> srcList, List<ResidentAccess> targetList) {
        List<ResidentAccess> addList = new ArrayList<>(targetList.size());
        List<ResidentAccess> removeList = new ArrayList<>(targetList.size());
        if (CollectionUtils.isEmpty(srcList)) {
            addList = targetList;

        } else if (CollectionUtils.isEmpty(targetList)) {
            removeList = srcList;

        } else {
            addList = targetList.stream().filter(x -> !srcList.contains(x)).collect(Collectors.toList());
            removeList = srcList.stream().filter(x -> !targetList.contains(x)).collect(Collectors.toList());
        }
        DataSepDto<ResidentAccess> result = new DataSepDto<>();
        result.setAddList(addList);
        result.setRemoveList(removeList);
        return result;
    }


    /**
     * 下发添加门禁权限
     *
     * @param residentNo
     * @param accessList
     * @param isDelete
     */
    public void accessRight(String residentNo, List<ResidentAccess> accessList, Boolean isDelete) {
        ResidentAccessResultVo result = getBaseMapper().residentAccessResultByNo(residentNo, Boolean.FALSE);
        if (result == null || CollectionUtils.isEmpty(accessList)) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        //如果为住户则必须要有合同
        if (result.getResidentType() == 0 && (accessList.stream().anyMatch(access -> StringUtils.isBlank(access.getContractNo())))) {
            throw new BusinessException("合同号不能为空");
        }
        List<Long> deviceIds = accessList.stream().map(ResidentAccess::getDeviceId).collect(Collectors.toList());
        List<AccessControlVo> accessControlVos = new ArrayList<>();
        deviceIds.forEach(x -> {
            AccessControlVo control = accessControlComponent.findAccessControlById(x);
            accessControlVos.add(control);
        });


        AccessDeviceRightVo rightVo = new AccessDeviceRightVo();
        rightVo.setDeviceIps(accessControlVos.stream().map(AccessControlVo::getDeviceIp).toArray(String[]::new));
        rightVo.setEmployeeNo(result.getResidentNo());
        rightVo.setEmployeeName(result.getName());
        rightVo.setFaceUrl(result.getFaceUrl());
        rightVo.setEmployeeType(EmployeeType.RESIDENT.getValue());
        if (BooleanUtils.isTrue(isDelete)) {
            accessControlComponent.deleteAccessRight(rightVo);
        } else {
            accessControlComponent.addAccessRight(rightVo);
        }
    }


}
