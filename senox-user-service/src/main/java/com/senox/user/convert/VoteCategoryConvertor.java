package com.senox.user.convert;

import com.senox.user.domain.VoteCategory;
import com.senox.user.vo.VoteCategoryVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/16 11:09
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface VoteCategoryConvertor {

    VoteCategory toDo(VoteCategoryVo vo);

    List<VoteCategory> toDo(List<VoteCategoryVo> voList);

    VoteCategoryVo toVo(VoteCategory domain);

    List<VoteCategoryVo> toVo(List<VoteCategory> domainList);
}
