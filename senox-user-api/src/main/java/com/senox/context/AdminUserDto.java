package com.senox.context;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 上下文管理员细腻些
 * <AUTHOR>
 * @Date 2020/12/22 15:50
 */
@Setter
@Getter
@ToString
public class AdminUserDto implements Serializable {

    private static final long serialVersionUID = -1289719100791920081L;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 用户姓名
     */
    private String username;
    /**
     * 真实姓名
     */
    private String realName;
    /**
     * 部门id
     */
    private Long departmentId;
    /**
     * 部门名
     */
    private String departmentName;
    /**
     * 是否收费员
     */
    private Boolean tollMan = Boolean.FALSE;
    /**
     * 签名
     */
    private String token;
    /**
     * mpId
     */
    private String mpAppId;
    /**
     * 微信签名
     */
    private String wechatToken;
    /**
     * 授权凭证
     */
    private String authCredentials;
    /**
     * 访问令牌
     */
    private String accessToken;
    /**
     * feign签名
     */
    private String feignToken;
    /**
     * 可登录
     */
    private Boolean loginable;

    /**
     * 0：员工，1：维修员，2：维修主管
     */
    private Integer maintainType;

    /**
     * 登录跳转路径
     */
    private String loginPath;

    /**
     * 角色编码列表
     */
    private List<String> roleCodes;


    /**
     * 权限
     */
    private List<String> cosList;


    public AdminUserDto() {
    }

    public AdminUserDto(Long userId, String username) {
        this.userId = userId;
        this.username = username;
    }

    /**
     * 是否有效用户
     * @return
     */
    @JsonIgnore
    public boolean isValid() {
        return userId != null && userId > 0L && username != null && username.length() > 0
                && isValidToken();
    }

    @JsonIgnore
    private boolean isValidToken() {
        return (getToken() != null && getToken().length() > 0)
                || (getWechatToken() != null && getWechatToken().length() > 0)
                || (getAuthCredentials() != null && getAuthCredentials().length() > 0)
                || (getAccessToken() != null && getAccessToken().length() > 0)
                || (getFeignToken() != null && getFeignToken().length() > 0);
    }
}
