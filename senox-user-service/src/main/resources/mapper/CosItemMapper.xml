<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.user.mapper.CosItemMapper">

    <resultMap id="Result_CosItem" type="com.senox.user.domain.CosItem">
        <id property="id" jdbcType="BIGINT" column="id" />
        <result property="name" jdbcType="VARCHAR" column="name" />
        <result property="displayName" jdbcType="VARCHAR" column="display_name" />
        <result property="url" jdbcType="VARCHAR" column="url" />
        <result property="parentId" jdbcType="BIGINT" column="parent_id" />
        <result property="disabled" jdbcType="TINYINT" column="is_disabled" />
        <result property="creatorId" jdbcType="BIGINT" column="creator_id" />
        <result property="creatorName" jdbcType="VARCHAR" column="creator_name" />
        <result property="createTime" jdbcType="TIMESTAMP" column="create_time" />
        <result property="modifierId" jdbcType="BIGINT" column="modifier_id" />
        <result property="modifierName" jdbcType="VARCHAR" column="modifier_name" />
        <result property="modifiedTime" jdbcType="TIMESTAMP" column="modified_time" />
    </resultMap>

    <!-- 添加权限项 -->
    <insert id="addCosItem" parameterType="com.senox.user.domain.CosItem" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO u_cos_item(
            name, display_name, url, parent_id, order_no, creator_id, creator_name, create_time, modifier_id, modifier_name, modified_time
        ) VALUES (
            #{name}, #{displayName}, #{url}, #{parentId}, #{orderNo}, #{creatorId}, #{creatorName}, NOW(), #{modifierId}, #{modifierName}, NOW()
        )
    </insert>

    <!-- 更新权限项 -->
    <update id="updateCosItem" parameterType="com.senox.user.domain.CosItem">
        UPDATE u_cos_item
        <set>
            <if test="name != null and name != ''">
                , name = #{name}
            </if>
            <if test="displayName != null and displayName != ''">
                , display_name = #{displayName}
            </if>
            <if test="url != null">
                , url = #{url}
            </if>
            <if test="orderNo != null">
                , order_no = #{orderNo}
            </if>
            , modifier_id = #{modifierId}
            , modifier_name = #{modifierName}
            , modified_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 删除权限项 -->
    <delete id="deleteCosItem" parameterType="java.lang.Long">
        DELETE FROM u_cos_item WHERE id = #{id}
    </delete>

    <!-- 根据id查找权限项 -->
    <select id="findById" parameterType="java.lang.Long" resultMap="Result_CosItem">
        SELECT id, name, display_name, url, parent_id, order_no
        FROM u_cos_item
        WHERE id = #{id}
    </select>

    <!-- 根据权限名获取id -->
    <select id="findIdByName" parameterType="java.lang.String" resultType="java.lang.Long">
        SELECT id FROM u_cos_item WHERE name = #{name} AND is_disabled = 0
    </select>

    <!-- 权限列表 -->
    <select id="listAll" resultMap="Result_CosItem">
        SELECT id, name, display_name, url, parent_id, order_no
        FROM u_cos_item
        WHERE is_disabled = 0
    </select>

    <!-- 根据角色查找权限列表 -->
    <select id="listCosByRole" parameterType="java.util.List" resultMap="Result_CosItem">
        SELECT c.id, c.name FROM u_role_cos rc INNER JOIN u_cos_item c ON rc.cos_id = c.id
        WHERE c.is_disabled = 0
            AND rc.role_id IN <foreach collection="roles" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </select>
</mapper>