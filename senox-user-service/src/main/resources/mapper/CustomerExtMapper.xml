<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.user.mapper.CustomerExtMapper">

    <resultMap id="Result_CustomerExt" type="com.senox.user.domain.CustomerExt">
        <id property="id" jdbcType="BIGINT" column="id" />
        <result property="customerId" jdbcType="BIGINT" column="customer_id" />
        <result property="bankAccount" jdbcType="VARCHAR" column="bank_account" />
        <result property="remark" jdbcType="VARCHAR" column="remark" />
        <result property="avatar" jdbcType="VARCHAR" column="avatar" />
        <result property="disabled" jdbcType="TINYINT" column="is_disabled" />
        <result property="creatorId" jdbcType="BIGINT" column="creator_id" />
        <result property="creatorName" jdbcType="VARCHAR" column="creator_name" />
        <result property="createTime" jdbcType="TIMESTAMP" column="create_time" />
        <result property="modifierId" jdbcType="BIGINT" column="modifier_id" />
        <result property="modifierName" jdbcType="VARCHAR" column="modifier_name" />
        <result property="modifiedTime" jdbcType="TIMESTAMP" column="modified_time" />
    </resultMap>

    <!-- 添加客户扩展信息 -->
    <insert id="addCustomerExt" parameterType="com.senox.user.domain.CustomerExt" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO u_customer_ext(
            customer_id, bank_account, remark, avatar, creator_id, creator_name, create_time, modifier_id, modifier_name, modified_time
        ) VALUES (
            #{customerId}, #{bankAccount}, #{remark}, #{avatar}, #{creatorId}, #{creatorName}, NOW(), #{modifierId}, #{modifierName}, NOW()
        )
    </insert>

    <!-- 更新客户扩展信息 -->
    <update id="updateCustomerExt" parameterType="com.senox.user.domain.CustomerExt">
        UPDATE u_customer_ext
        <set>
            <if test="remark != null">
                , remark = #{remark}
            </if>
            <if test="bankAccount != null">
                , bank_account = #{bankAccount}
            </if>
            <if test="avatar != null and avatar != ''">
                , avatar = #{avatar}
            </if>
            <if test="disabled != null">
                , is_disabled = #{disabled}
            </if>
            , modifier_id = #{modifierId}
            , modifier_name = #{modifierName}
            , modified_time = NOW()
        </set>
        WHERE customer_id = #{customerId}
    </update>

    <!-- 根据id查找客户扩展信息 -->
    <select id="findById" parameterType="java.lang.Long" resultMap="Result_CustomerExt">
        SELECT id, customer_id, bank_account, remark, avatar, is_disabled
        FROM u_customer_ext
        WHERE id = #{id}
    </select>

    <!-- 根据客户id获取客户扩展信息 -->
    <select id="findByCustomerId" parameterType="java.lang.Long" resultMap="Result_CustomerExt">
        SELECT id, customer_id, bank_account, remark, avatar, is_disabled
        FROM u_customer_ext
        WHERE customer_id = #{id}
    </select>


</mapper>