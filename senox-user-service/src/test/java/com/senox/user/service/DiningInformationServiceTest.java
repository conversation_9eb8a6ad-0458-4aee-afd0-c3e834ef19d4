package com.senox.user.service;

import com.senox.common.exception.BusinessException;
import com.senox.common.utils.JsonUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.BaseTest;
import com.senox.user.domain.DiningInformation;
import com.senox.user.vo.DiningInformationSearchVo;
import com.senox.user.vo.DiningInformationVo;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

class DiningInformationServiceTest extends BaseTest {

    private static final Logger logger = LoggerFactory.getLogger(DiningInformationServiceTest.class);

    @Autowired
    private DiningInformationService diningInformationService;

    @Test
    void test() {
        List<DiningInformation> list1 = new ArrayList<>();
        List<DiningInformation> list2 = new ArrayList<>();
        for (int i = 1; i <= 5; i++) {
            DiningInformation information = new DiningInformation();
            information.setEmployeeName("叶伟豪");
            information.setMealDate(LocalDate.of(2022, 12, 3));
            information.setMealTime(LocalTime.of(12, 38 + i, 12));
            information.setCreatorId(1L);
            information.setModifierId(1L);
            information.setCreatorName("张");
            information.setModifierName("张");

            list1.add(information);
        }
        for (int i = 1; i <= 5; i++) {
            DiningInformation information = new DiningInformation();
            information.setEmployeeName("杨健");
            information.setMealDate(LocalDate.of(2022, 12, 4));
            information.setMealTime(LocalTime.of(13, 38 + i, 12));
            information.setCreatorId(1L);
            information.setModifierId(1L);
            information.setCreatorName("张");
            information.setModifierName("张");
            list2.add(information);
        }
        List<DiningInformation> list = new ArrayList<>(list1);
        list.addAll(list2);
        Assertions.assertEquals(10, list.size());
        diningInformationService.saveDiningInformationBatch(list);

        DiningInformationSearchVo searchVo = new DiningInformationSearchVo();
        searchVo.setPageNo(1);
        searchVo.setPageSize(10);

        PageResult<DiningInformationVo> diningPage = diningInformationService.informationList(searchVo);
        logger.info("diningPage : {}", JsonUtils.object2Json(diningPage));
        Assertions.assertEquals(2, diningPage.getTotalSize());


        DiningInformation information = new DiningInformation();
        information.setEmployeeName("蔡圣德");
        information.setMealDate(LocalDate.of(2022, 12, 4));
        information.setMealTime(LocalTime.of(13, 38, 12));
        information.setCreatorId(1L);
        information.setModifierId(1L);
        information.setCreatorName("张");
        information.setModifierName("张");
        information.setDining(0);
        List<DiningInformation> list3 = Collections.singletonList(information);
        diningInformationService.saveDiningInformationBatch(list3);
        Assertions.assertThrows(BusinessException.class, () -> diningInformationService.saveDiningInformation(information));

        diningPage = diningInformationService.informationList(searchVo);
        logger.info("diningPage : {}", JsonUtils.object2Json(diningPage));
        Assertions.assertEquals(3, diningPage.getTotalSize());


        DiningInformationVo byId = diningInformationService.findById(diningPage.getDataList().get(0).getId());
        Assertions.assertEquals("杨健", byId.getEmployeeName());
        Assertions.assertEquals(0, LocalDate.of(2022, 12, 4).compareTo(byId.getMealDate()));

        DiningInformation diningInformation = new DiningInformation();
        diningInformation.setId(diningPage.getDataList().get(0).getId());
        diningInformation.setEmployeeName("蔡学军");
        diningInformationService.updateDiningInformation(diningInformation);

        diningPage = diningInformationService.informationList(searchVo);
        Assertions.assertEquals("蔡学军", diningPage.getDataList().get(0).getEmployeeName());
        Assertions.assertEquals(0, LocalDate.of(2022, 12, 4).compareTo(diningPage.getDataList().get(0).getMealDate()));


        diningInformationService.deleteDiningInformation(diningPage.getDataList().get(0).getId());
        diningPage = diningInformationService.informationList(searchVo);
        Assertions.assertEquals("蔡圣德", diningPage.getDataList().get(0).getEmployeeName());
        Assertions.assertEquals(0, LocalDate.of(2022, 12, 4).compareTo(diningPage.getDataList().get(0).getMealDate()));
    }
}
