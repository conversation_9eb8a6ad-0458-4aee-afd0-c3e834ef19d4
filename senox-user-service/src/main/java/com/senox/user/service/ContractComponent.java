package com.senox.user.service;

import com.senox.common.utils.FeignUtils;
import com.senox.common.utils.StringUtils;
import com.senox.realty.api.clients.ContractClient;
import com.senox.realty.vo.ContractVo;
import feign.FeignException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/2/18 14:29
 */
@Component
public class ContractComponent {

    @Autowired
    private ContractClient contractClient;

    /**
     * 根据合同号获取合同
     * @param contractNo
     * @return
     */
    public ContractVo findByContractNo(String contractNo) {
        if (StringUtils.isBlank(contractNo)) {
            return null;
        }

        try {
            return contractClient.getByContractNo(contractNo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }
}
