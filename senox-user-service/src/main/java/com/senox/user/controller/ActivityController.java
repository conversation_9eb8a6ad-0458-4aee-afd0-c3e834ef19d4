package com.senox.user.controller;

import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.user.convert.ActivityConvertor;
import com.senox.user.domain.Activity;
import com.senox.user.service.ActivityService;
import com.senox.user.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2024/10/16 9:09
 */
@Api(tags = "活动")
@RestController
@RequiredArgsConstructor
@RequestMapping("/activity")
public class ActivityController {

    private final ActivityService activityService;
    private final ActivityConvertor activityConvertor;

    @ApiOperation("添加活动")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addActivity(@RequestBody ActivityVo activityVo) {
        Activity activity = activityConvertor.toDo(activityVo);
        return activityService.addActivity(activity);
    }

    @ApiOperation("更新活动")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateActivity(@RequestBody ActivityVo activityVo) {
        Activity activity = activityConvertor.toDo(activityVo);
        activityService.updateActivity(activity);
    }

    @ApiOperation("生成活动链接")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/generate/{id}")
    public String generateActivityUrl(@PathVariable Long id) {
        return activityService.generateActivityUrl(id);
    }

    @ApiOperation("根据id获取活动")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public ActivityVo findActivityById(@PathVariable Long id) {
        return activityConvertor.toVo(activityService.findById(id));
    }

    @ApiOperation("根据uuid获取活动")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/getByUuid/{uuid}")
    public ActivityVo findActivityByUuid(@PathVariable String uuid) {
        return activityConvertor.toVo(activityService.findByUuid(uuid));
    }

    @ApiOperation("根据id删除活动")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/delete/{id}")
    public void deleteActivityById(@PathVariable Long id) {
        activityService.deleteById(id);
    }

    @ApiOperation("活动分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page")
    public PageResult<ActivityVo> pageActivityResult(@RequestBody ActivitySearchVo searchVo) {
        return activityService.page(searchVo);
    }
}
