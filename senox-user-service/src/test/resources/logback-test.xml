<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %5level [%thread] %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 抑制Docker客户端的连接关闭异常 -->
    <logger name="com.github.dockerjava.zerodep.shaded.org.apache.hc.client5.http.wire" level="WARN" />
    <logger name="com.github.dockerjava.zerodep.ApacheDockerHttpClientImpl" level="WARN" />
    <logger name="com.github.dockerjava" level="INFO" />

    <!-- 抑制Redis连接关闭异常 -->
    <logger name="io.lettuce.core.protocol.ConnectionWatchdog" level="ERROR" />

    <!-- 保持Testcontainers的重要信息 -->
    <logger name="org.testcontainers" level="INFO" />
    
    <root level="INFO">
        <appender-ref ref="CONSOLE" />
    </root>
</configuration>
