package com.senox.user.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023-10-30
 */
@Getter
public enum MerchantBillSettlePeriod {

    /**
     * 单结
     */
    AD_HOC(0, "单结"),

    /**
     * 日结
     */
    DAILY(1, "日结"),

    /**
     * 月结
     */
    MONTHLY(2, "月结"),
    ;
    private final int number;
    private final String name;

    MerchantBillSettlePeriod(int number, String name) {
        this.number = number;
        this.name = name;
    }

    public static MerchantBillSettlePeriod fromNumber(Integer number) {
        if (null == number) {
            return null;
        }
        for (MerchantBillSettlePeriod item : values()) {
            if (number.equals(item.number)) {
                return item;
            }
        }
        return null;
    }
}
