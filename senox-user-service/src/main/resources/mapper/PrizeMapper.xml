<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.user.mapper.PrizeMapper">


    <select id="countPrize" parameterType="com.senox.user.vo.PrizeSearchVo" resultType="int">
        select count(1) from u_prize
        <where>
            <if test="name != null and name != ''">
                and name like concat('%', #{name}, '%')
            </if>
            <if test="activityId != null">
                and activity_id = #{activityId}
            </if>
            <if test="remainingNumCheck != null">
                <choose>
                    <when test="remainingNumCheck">
                        and remaining_num > 0
                    </when>
                </choose>
            </if>
            and is_disabled = 0
        </where>
    </select>

    <select id="listPrize" parameterType="com.senox.user.vo.PrizeSearchVo" resultType="com.senox.user.domain.Prize">
        select id
            , activity_id
            , name
            , description
            , media_url
            , total_num
            , remaining_num
            , probability
        from u_prize
        <where>
            <if test="name != null and name != ''">
                and name like concat('%', #{name}, '%')
            </if>
            <if test="activityId != null">
                and activity_id = #{activityId}
            </if>
            <if test="remainingNumCheck != null">
                <choose>
                    <when test="remainingNumCheck">
                        and remaining_num > 0
                    </when>
                </choose>
            </if>
            and is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY id DESC
            </otherwise>
        </choose>
        <if test="page">
            limit ${offset}, ${pageSize}
        </if>
    </select>

    <update id="reduceRemainingNum" parameterType="java.lang.Long">
        update u_prize set remaining_num = remaining_num - 1
            where id = #{id} and remaining_num > 0 and is_disabled = 0
    </update>

</mapper>
