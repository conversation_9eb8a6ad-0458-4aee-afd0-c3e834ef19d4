<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.user.mapper.EmployeeMapper">

    <resultMap id="Result_Employee" type="com.senox.user.domain.Employee">
        <id property="id" jdbcType="BIGINT" column="id" />
        <result property="username" jdbcType="VARCHAR" column="username" />
        <result property="companyName" jdbcType="VARCHAR" column="company_name" />
        <result property="departmentId" jdbcType="BIGINT" column="department_id"/>
        <result property="mpOpenid" jdbcType="VARCHAR" column="mp_openid" />
        <result property="defaultBooked" jdbcType="TINYINT" column="default_booked" />
        <result property="canteenMaster" jdbcType="TINYINT" column="is_canteen_master" />
        <result property="remark" jdbcType="VARCHAR" column="remark" />
        <result property="disabled" jdbcType="TINYINT" column="is_disabled" />
        <result property="creatorId" jdbcType="BIGINT" column="creator_id" />
        <result property="creatorName" jdbcType="VARCHAR" column="creator_name" />
        <result property="createTime" jdbcType="TIMESTAMP" column="create_time" />
        <result property="modifierId" jdbcType="BIGINT" column="modifier_id" />
        <result property="modifierName" jdbcType="VARCHAR" column="modifier_name" />
        <result property="modifiedTime" jdbcType="TIMESTAMP" column="modified_time" />
    </resultMap>

    <resultMap id="Result_EmployeeVo" type="com.senox.user.vo.EmployeeVo">
        <id property="id" jdbcType="BIGINT" column="id" />
        <result property="username" jdbcType="VARCHAR" column="username" />
        <result property="companyName" jdbcType="VARCHAR" column="company_name" />
        <result property="departmentId" jdbcType="BIGINT" column="department_id"/>
        <result property="departmentName" jdbcType="VARCHAR" column="full_name"/>
        <result property="mpOpenid" jdbcType="VARCHAR" column="mp_openid" />
        <result property="defaultBooked" jdbcType="TINYINT" column="default_booked" />
        <result property="canteenMaster" jdbcType="TINYINT" column="is_canteen_master" />
        <result property="remark" jdbcType="VARCHAR" column="remark" />
        <result property="disabled" jdbcType="TINYINT" column="is_disabled" />
        <result property="modifiedTime" jdbcType="TIMESTAMP" column="modified_time" />
    </resultMap>

    <!-- 新增员工 -->
    <insert id="addEmployee" parameterType="com.senox.user.domain.Employee" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO u_employee(
            username, company_name, department_id, mp_openid, default_booked, is_canteen_master, remark, creator_id, creator_name, create_time
            , modifier_id, modifier_name, modified_time
        ) VALUES (
            #{username}, #{companyName}, #{departmentId}, #{mpOpenid}, #{defaultBooked}, #{canteenMaster}, #{remark}, #{creatorId}, #{creatorName}, NOW()
            , #{modifierId}, #{modifierName}, NOW()
        )
    </insert>

    <!-- 更新员工 -->
    <update id="updateEmployee" parameterType="com.senox.user.domain.Employee">
        UPDATE u_employee
        <set>
            <if test="username != null and username != ''">
                , username = #{username}
            </if>
            <if test="companyName != null and companyName != ''">
                , company_name = #{companyName}
            </if>
            <if test="departmentId != null">
                , department_id = #{departmentId}
            </if>
            <if test="mpOpenid != null">
                , mp_openid = #{mpOpenid}
            </if>
            <if test="defaultBooked != null">
                , default_booked = #{defaultBooked}
            </if>
            <if test="canteenMaster != null">
                , is_canteen_master = #{canteenMaster}
            </if>
            <if test="remark != null">
                , remark = #{remark}
            </if>
            <if test="disabled != null">
                , is_disabled = #{disabled}
            </if>
            , modifier_id = #{modifierId}
            , modifier_name = #{modifierName}
            , modified_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据id查找员工信息 -->
    <select id="findById" parameterType="java.lang.Long" resultMap="Result_EmployeeVo">
        SELECT e.id, e.username, e.department_id, e.company_name, e.mp_openid, e.default_booked, e.is_canteen_master, e.remark, e.is_disabled, e.modified_time, d.full_name
        FROM u_employee e
            LEFT JOIN u_department d ON d.id = e.department_id
        WHERE e.id = #{id}
    </select>

    <select id="findByName" parameterType="java.lang.String" resultMap="Result_Employee">
        SELECT id, username, company_name, department_id, mp_openid, default_booked, is_canteen_master, modified_time
        FROM u_employee
        where username = #{employeeName}
    </select>


    <!-- 查找代理企业的员工 -->
    <select id="findDelegateCompanyEmployee" parameterType="java.lang.Long" resultMap="Result_Employee">
        SELECT e.id, e.username, e.company_name, e.mp_openid, e.default_booked
        FROM u_employee e
            INNER JOIN u_employee_meal_delegate d on e.id = d.employee_id and type = 2
        WHERE d.delegate_id  = #{delegateCompany} AND e.is_disabled = 0
        LIMIT 1
    </select>

    <!-- 统计员工 -->
    <select id="countEmployee" parameterType="com.senox.user.vo.EmployeeSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM u_employee e
            LEFT JOIN u_department d ON d.id = e.department_id
        <where>
            <if test="username != null and username != ''">
                AND username = #{username}
            </if>
            <if test="companyName != null and companyName != ''">
                AND company_name = #{companyName}
            </if>
            <if test="mpOpenid != null and mpOpenid != ''">
                AND mp_openid = #{mpOpenid}
            </if>
            <if test="usernames != null and usernames.size() > 0">
                AND e.username IN <foreach collection="usernames" item="item" separator="," open="(" close=")">#{item}</foreach>
            </if>
            AND e.is_disabled = 0
        </where>
    </select>

    <!-- 员工列表 -->
    <select id="listEmployee" parameterType="com.senox.user.vo.EmployeeSearchVo" resultMap="Result_EmployeeVo">
        SELECT e.id, e.username, e.company_name, e.department_id, e.mp_openid, e.default_booked, e.is_canteen_master, e.modified_time, d.full_name
        FROM u_employee e
            LEFT JOIN u_department d ON d.id = e.department_id
        <where>
            <if test="username != null and username != ''">
                AND username = #{username}
            </if>
            <if test="companyName != null and companyName != ''">
                AND company_name = #{companyName}
            </if>
            <if test="mpOpenid != null and mpOpenid != ''">
                AND mp_openid = #{mpOpenid}
            </if>
            <if test="usernames != null and usernames.size() > 0">
                AND e.username IN <foreach collection="usernames" item="item" separator="," open="(" close=")">#{item}</foreach>
            </if>
            AND e.is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise> ORDER BY id DESC</otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

    <!-- 加载尚未报餐的员工列表 -->
    <select id="listNoBookedEmployee" resultMap="Result_Employee">
        SELECT e.id, e.username, e.company_name, e.mp_openid, e.default_booked
        FROM u_employee e
            LEFT JOIN wx_booking_meal b ON e.company_name  = b.company AND e.username  = b.employee AND b.meal_date = #{mealDate}
        WHERE e.is_disabled  = 0
            AND b.id IS NULL
            <if test="excludeCompanies != null and excludeCompanies.size() > 0">
                AND e.company_name NOT IN <foreach collection="excludeCompanies" item="item" separator="," open="(" close=")">#{item}</foreach>
            </if>
        ORDER BY id
        LIMIT ${offset}, ${rows}
    </select>

</mapper>