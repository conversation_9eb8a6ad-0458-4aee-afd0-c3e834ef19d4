<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.user.mapper.CustomerMapper">

    <resultMap id="Result_Customer" type="com.senox.user.domain.Customer">
        <id property="id" jdbcType="BIGINT" column="id" />
        <result property="serialNo" jdbcType="VARCHAR" column="serial_no" />
        <result property="name" jdbcType="VARCHAR" column="name" />
        <result property="idcard" jdbcType="VARCHAR" column="idcard" />
        <result property="idcardType" jdbcType="TINYINT" column="idcard_type" />
        <result property="gender" jdbcType="TINYINT" column="gender" />
        <result property="nation" jdbcType="VARCHAR" column="nation" />
        <result property="bornDate" jdbcType="TIMESTAMP" column="born_date" />
        <result property="nativePlace" jdbcType="VARCHAR" column="native_place" />
        <result property="address" jdbcType="VARCHAR" column="address" />
        <result property="telephone" jdbcType="VARCHAR" column="telephone" />
        <result property="email" jdbcType="VARCHAR" column="email" />
        <result property="provinceId" jdbcType="BIGINT" column="province_id" />
        <result property="provinceName" jdbcType="VARCHAR" column="province_name" />
        <result property="cityId" jdbcType="BIGINT" column="city_id" />
        <result property="cityName" jdbcType="VARCHAR" column="city_name" />
        <result property="workplaceRegionId" jdbcType="BIGINT" column="workplace_region_id" />
        <result property="workplaceRegionName" jdbcType="VARCHAR" column="workplace_region_name" />
        <result property="workplaceStreetId" jdbcType="BIGINT" column="workplace_street_id" />
        <result property="workplaceStreetName" jdbcType="VARCHAR" column="workplace_street_name" />
        <result property="workplaceAddress" jdbcType="VARCHAR" column="workplace_address" />
        <result property="professionId" jdbcType="BIGINT" column="profession_id" />
        <result property="professionName" jdbcType="VARCHAR" column="profession_name" />
        <result property="jobTitle" jdbcType="VARCHAR" column="job_title" />
        <result property="natTested" jdbcType="TINYINT" column="is_nat_tested" />
        <result property="covid19Vaccination" jdbcType="TINYINT" column="is_covid19_vaccination" />
        <result property="disabled" jdbcType="TINYINT" column="is_disabled" />
        <result property="creatorId" jdbcType="BIGINT" column="creator_id" />
        <result property="creatorName" jdbcType="VARCHAR" column="creator_name" />
        <result property="createTime" jdbcType="TIMESTAMP" column="create_time" />
        <result property="modifierId" jdbcType="BIGINT" column="modifier_id" />
        <result property="modifierName" jdbcType="VARCHAR" column="modifier_name" />
        <result property="modifiedTime" jdbcType="TIMESTAMP" column="modified_time" />
    </resultMap>

    <!-- 添加客户 -->
    <insert id="addCustomer" parameterType="com.senox.user.domain.Customer" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO u_customer(
            serial_no, name, idcard, idcard_type, gender, nation, born_date, native_place, address, telephone, email
            , province_id, province_name, city_id, city_name, workplace_region_id, workplace_region_name
            , workplace_street_id, workplace_street_name, workplace_address, profession_id, profession_name
            , job_title, is_nat_tested , is_covid19_vaccination, is_znw_synced, creator_id, creator_name, create_time
            , modifier_id, modifier_name, modified_time
        ) VALUES (
            #{serialNo}, #{name}, #{idcard}, #{idcardType}, #{gender}, #{nation}, #{bornDate}, #{nativePlace}, #{address}, #{telephone}, #{email}
            , #{provinceId}, #{provinceName}, #{cityId}, #{cityName}, #{workplaceRegionId}, #{workplaceRegionName}
            , #{workplaceStreetId}, #{workplaceStreetName}, #{workplaceAddress}, #{professionId}, #{professionName}
            , #{jobTitle} , #{natTested}, #{covid19Vaccination}, #{znwSynced}, #{creatorId}, #{creatorName}, NOW()
            , #{modifierId}, #{modifierName}, NOW()
        )
    </insert>

    <!-- 更新客户 -->
    <update id="updateCustomer" parameterType="com.senox.user.domain.Customer">
        UPDATE u_customer
        <set>
            <if test="name != null and name != ''">
                , name = #{name}
            </if>
            <if test="idcard != null">
                , idcard = #{idcard}
            </if>
            <if test="idcardType != null">
                , idcard_type = #{idcardType}
            </if>
            <if test="gender != null">
                , gender = #{gender}
            </if>
            <if test="nation != null">
                , nation = #{nation}
            </if>
            <if test="bornDate != null">
                , born_date = #{bornDate}
            </if>
            <if test="nativePlace != null">
                , native_place = #{nativePlace}
            </if>
            <if test="address != null">
                , address = #{address}
            </if>
            <if test="telephone != null">
                , telephone = #{telephone}
            </if>
            <if test="email != null">
                , email = #{email}
            </if>
            <if test="provinceId != null">
                , province_id = #{provinceId}
            </if>
            <if test="provinceName != null">
                , province_name = #{provinceName}
            </if>
            <if test="cityId != null">
                , city_id = #{cityId}
            </if>
            <if test="cityName != null">
                , city_name = #{cityName}
            </if>
            <if test="workplaceRegionId != null">
                , workplace_region_id = #{workplaceRegionId}
            </if>
            <if test="workplaceRegionName != null">
                , workplace_region_name = #{workplaceRegionName}
            </if>
            <if test="workplaceStreetId != null">
                , workplace_street_id = #{workplaceStreetId}
            </if>
            <if test="workplaceStreetName != null">
                , workplace_street_name = #{workplaceStreetName}
            </if>
            <if test="workplaceAddress != null">
                , workplace_address = #{workplaceAddress}
            </if>
            <if test="professionId != null">
                , profession_id = #{professionId}
            </if>
            <if test="professionName != null">
                , profession_name = #{professionName}
            </if>
            <if test="jobTitle != null">
                , job_title = #{jobTitle}
            </if>
            <if test="natTested != null">
                , is_nat_tested = #{natTested}
            </if>
            <if test="covid19Vaccination != null">
                , is_covid19_vaccination = #{covid19Vaccination}
            </if>
            <if test="disabled != null">
                , is_disabled = #{disabled}
            </if>
            , modifier_id = #{modifierId}
            , modifier_name = #{modifierName}
            , modified_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据id查找客户 -->
    <select id="findById" parameterType="java.lang.Long" resultMap="Result_Customer">
        SELECT id, serial_no, name, idcard, idcard_type, gender, nation, born_date, native_place, address, telephone, email
            , province_id, province_name, city_id, city_name, workplace_region_id , workplace_region_name
            , workplace_street_id, workplace_street_name, workplace_address, profession_id, profession_name
            , job_title, is_nat_tested, is_covid19_vaccination, is_disabled
        FROM u_customer
        WHERE id = #{id}
    </select>

    <!-- 根据身份证查找客户 -->
    <select id="findByIdcard" parameterType="java.lang.String" resultMap="Result_Customer">
        SELECT id, serial_no, name, idcard, idcard_type, gender, nation, born_date, native_place, address, telephone, email
            , province_id, province_name, city_id, city_name, workplace_region_id , workplace_region_name
            , workplace_street_id, workplace_street_name, workplace_address, profession_id, profession_name
            , job_title, is_nat_tested, is_covid19_vaccination, is_disabled
        FROM u_customer
        WHERE idcard = #{idcard} AND is_disabled = 0
        LIMIT 1
    </select>

    <select id="findBySerialNo" parameterType="java.lang.String" resultMap="Result_Customer">
        SELECT id, serial_no, name, idcard, idcard_type, gender, nation, born_date, native_place, address, telephone, email
             , province_id, province_name, city_id, city_name, workplace_region_id , workplace_region_name
             , workplace_street_id, workplace_street_name, workplace_address, profession_id, profession_name
             , job_title, is_nat_tested, is_covid19_vaccination, is_disabled
        FROM u_customer
        WHERE serial_no = #{serialNo}
        LIMIT 1
    </select>

    <!-- 取最大客户编号 -->
    <select id="findMaxSerialNo" resultType="java.lang.String">
        SELECT MAX(serial_no) FROM u_customer WHERE serial_no NOT IN ('8888')
    </select>

    <!-- 客户统计 -->
    <select id="countCustomer" parameterType="com.senox.user.vo.CustomerSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(id)
        FROM u_customer
        <where>
            <if test="serialNo != null and serialNo != ''">
                AND serial_no = #{serialNo}
            </if>
            <if test="name != null and name != ''">
                AND name = #{name}
            </if>
            <if test="idcard != null and idcard != ''">
                AND idcard = #{idcard}
            </if>
            <if test="telephone != null and telephone != ''">
                AND telephone = #{telephone}
            </if>
            <if test="workplaceRegionId != null">
                AND workplace_region_id = #{workplaceRegionId}
            </if>
            <if test="workplaceStreetId != null">
                AND workplace_street_id = #{workplaceStreetId}
            </if>
            <if test="natTested != null">
                AND is_nat_tested = #{natTested}
            </if>
            <if test="covid19Vaccination != null">
                AND is_covid19_vaccination = #{covid19Vaccination}
            </if>
            <if test="keyword != null">
                AND (serial_no LIKE CONCAT('%', #{keyword}, '%') OR name LIKE CONCAT('%', #{keyword}, '%') OR idcard LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            AND is_disabled = 0
        </where>
    </select>

    <!-- 客户分页列表 -->
    <select id="listCustomer" parameterType="com.senox.user.vo.CustomerSearchVo" resultMap="Result_Customer">
        SELECT id, serial_no, name, idcard, idcard_type, gender, nation, born_date, native_place, address, telephone, email
            , province_name, city_name, workplace_region_name, workplace_street_name, workplace_address, profession_name
            , job_title, is_nat_tested, is_covid19_vaccination, is_disabled, modified_time
        FROM u_customer
        <where>
            <if test="serialNo != null and serialNo != ''">
                AND serial_no = #{serialNo}
            </if>
            <if test="name != null and name != ''">
                AND name = #{name}
            </if>
            <if test="idcard != null and idcard != ''">
                AND idcard = #{idcard}
            </if>
            <if test="telephone != null and telephone != ''">
                AND telephone = #{telephone}
            </if>
            <if test="workplaceRegionId != null">
                AND workplace_region_id = #{workplaceRegionId}
            </if>
            <if test="workplaceStreetId != null">
                AND workplace_street_id = #{workplaceStreetId}
            </if>
            <if test="natTested != null">
                AND is_nat_tested = #{natTested}
            </if>
            <if test="covid19Vaccination != null">
                AND is_covid19_vaccination = #{covid19Vaccination}
            </if>
            <if test="keyword != null">
                AND (serial_no LIKE CONCAT('%', #{keyword}, '%') OR name LIKE CONCAT('%', #{keyword}, '%') OR idcard LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            AND is_disabled = 0
        </where>
        <if test="orderStr != null and orderStr != ''">
            ORDER BY ${orderStr}
        </if>
        LIMIT ${offset}, ${pageSize}
    </select>
</mapper>