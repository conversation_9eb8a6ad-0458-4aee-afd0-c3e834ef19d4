package com.senox.context;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskDecorator;

/**
 * 线程继承父线程上下文
 * <AUTHOR>
 * @date 2024/3/21 14:09
 */
@Slf4j
public class AdminContextDecorator implements TaskDecorator {

    @Override
    public Runnable decorate(Runnable runnable) {
        AdminUserDto contextUser = AdminContext.getUser();
        PluginEnv pluginEnv = AdminContext.getPluginEnv();
        return () -> {
            try {
                AdminContext.setUser(contextUser);
                AdminContext.setPluginEnv(pluginEnv);
                log.info("Set thread context user: {} ", contextUser == null ? null : contextUser.toString());
                log.info("Set thread pluginEnv: {} ", pluginEnv);
                runnable.run();
            } finally {
                log.info("Context decorated thread finished");
            }
        };
    }
}
