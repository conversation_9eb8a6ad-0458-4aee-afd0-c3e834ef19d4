package com.senox.user.api.clients;

import com.senox.common.vo.PageResult;
import com.senox.user.api.UserServiceUrl;
import com.senox.user.vo.WxUserRealtyVo;
import com.senox.user.vo.WxUserRemarkVo;
import com.senox.user.vo.WxUserSearchVo;
import com.senox.user.vo.WxUserVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 微信
 * <AUTHOR>
 * @date 2021/6/7 15:32
 */
@FeignClient(value = UserServiceUrl.SERVICE_NAME)
public interface WxUserClient {

    /**
     * 设置灰度用户
     * @param userId
     */
    @PostMapping(UserServiceUrl.WXUSER_GRAY_SET)
    void setGrayUser(@PathVariable Long userId);

    /**
     * 取消灰度用户
     * @param userId
     */
    @PostMapping(UserServiceUrl.WXUSER_GRAY_CANCEL)
    void cancelGrayUser(@PathVariable Long userId);

    /**
     * 微信用户列表
     * @param searchVo
     * @return
     */
    @PostMapping(UserServiceUrl.WXUSER_LIST)
    PageResult<WxUserVo> listWxUserPage(@RequestBody WxUserSearchVo searchVo);

    /**
     * 绑定微信物业
     * @param userRealty
     */
    @PostMapping(UserServiceUrl.WXUSER_REALTY_BIND)
    void bindWxUserRealty(@RequestBody WxUserRealtyVo userRealty);

    /**
     * 解绑微信物业
     * @param userRealty
     */
    @PostMapping(UserServiceUrl.WXUSER_REALTY_UNBIND)
    void unbindWxUserRealty(@RequestBody WxUserRealtyVo userRealty);

    /**
     * 微信用户绑定的物业列表
     * @param userId
     * @return
     */
    @PostMapping(UserServiceUrl.WXUSER_REALTY_LIST)
    List<WxUserRealtyVo> listWxUserRealty(@PathVariable Long userId);

    /**
     * 微信用户备注更新
     * @param remarkVo
     */
    @PostMapping(UserServiceUrl.WXUSER_UPDATE_REMARK)
    void updateWxUserRemark(@RequestBody WxUserRemarkVo remarkVo);

    /**
     * 解绑骑手
     * @param riderId
     */
    @PostMapping(UserServiceUrl.WXUSER_UNBIND_RIDER)
    void unbindRider(@PathVariable Long riderId);
}
