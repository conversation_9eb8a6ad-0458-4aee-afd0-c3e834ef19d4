<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.user.mapper.FeedBackMapper">

    <resultMap id="Result_FeedBack" type="com.senox.user.vo.FeedBackVo">
        <result property="id" column="id"/>
        <result property="openid" column="openid"/>
        <result property="title" column="title"/>
        <result property="anonymous" column="anonymous"/>
        <result property="name" column="name"/>
        <result property="contact" column="contact"/>
        <result property="content" column="content"/>
        <result property="createTime" column="create_time"/>
        <collection property="mediaUrls" ofType="java.lang.String">
            <constructor>
                <arg column="media_url" />
            </constructor>
        </collection>
        <collection property="feedBackReplyVoList" ofType="com.senox.user.vo.FeedBackReplyVo">
            <result property="id" column="reply_id"/>
            <result property="feedBackId" column="feed_back_id"/>
            <result property="openid" column="reply_openid"/>
            <result property="name" column="reply_name"/>
            <result property="content" column="reply_content"/>
            <result property="parentId" column="parent_id"/>
            <result property="createTime" column="item_create_time"/>
        </collection>
    </resultMap>

    <select id="countFeedBack" resultType="int" parameterType="com.senox.user.vo.FeedBackSearchVo">
        select count(id) from u_feed_back
        <where>
            <if test="name != null and name != ''">
                AND name = #{name}
            </if>
            <if test="openid != null and openid != ''">
                AND openid = #{openid}
            </if>
            <if test="startTime != null">
                AND create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND create_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="replyState != null">
                AND reply_state = #{replyState}
            </if>
            AND disabled = 0
        </where>
    </select>

    <select id="listFeedBack" parameterType="com.senox.user.vo.FeedBackSearchVo" resultType="com.senox.user.vo.FeedBackVo">
        select id, title, anonymous, name, contact, content, reply_state, create_time from u_feed_back
        <where>
            <if test="name != null and name != ''">
                AND name = #{name}
            </if>
            <if test="openid != null and openid != ''">
                AND openid = #{openid}
            </if>
            <if test="startTime != null">
                AND create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND create_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="replyState != null">
                AND reply_state = #{replyState}
            </if>
            AND disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                order by ${orderStr}
            </when>
            <otherwise>
                order by id desc
            </otherwise>
        </choose>
        <if test="page">
            limit ${offset}, ${pageSize}
        </if>
    </select>

    <select id="getFeedBackResultById" parameterType="long" resultMap="Result_FeedBack">
        select f.id as id
            ,f.openid
            ,f.title
            ,f.anonymous
            ,f.name
            ,f.contact
            ,f.content
            ,f.create_time
            ,m.media_url
        <if test="isDetail">
            ,r.id as reply_id
            ,r.openid as reply_openid
            ,r.feed_back_id as feed_back_id
            ,r.content as reply_content
            ,r.name as reply_name
            ,r.parent_id as parent_id
            ,r.create_time as item_create_time
        </if>
        from u_feed_back f
            LEFT JOIN u_feed_back_media m ON f.id = m.feed_back_id
        <if test="isDetail">
            LEFT JOIN u_feed_back_reply r ON f.id = r.feed_back_id
        </if>
        where f.id = #{id}
    </select>
</mapper>