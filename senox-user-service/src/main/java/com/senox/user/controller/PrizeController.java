package com.senox.user.controller;

import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.user.convert.PrizeConvert;
import com.senox.user.convert.PrizeRecordsConvert;
import com.senox.user.domain.Prize;
import com.senox.user.domain.PrizeDrawNumber;
import com.senox.user.service.PrizeDrawNumberService;
import com.senox.user.service.PrizeRecordsService;
import com.senox.user.service.PrizeService;
import com.senox.user.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2025/4/11 10:23
 */
@Api(tags = "奖品")
@RestController
@RequestMapping("/prize")
@RequiredArgsConstructor
public class PrizeController {

    private final PrizeService prizeService;
    private final PrizeConvert prizeConvert;
    private final PrizeRecordsService recordsService;
    private final PrizeRecordsConvert recordsConvert;
    private final PrizeDrawNumberService drawNumberService;

    @ApiOperation("添加抽奖奖品")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addPrize(@RequestBody PrizeVo prizeVo) {
        Prize prize = prizeConvert.toDo(prizeVo);
        return prizeService.addPrize(prize);
    }

    @ApiOperation("更新抽奖奖品")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updatePrize(@RequestBody PrizeVo prizeVo) {
        Prize prize = prizeConvert.toDo(prizeVo);
        prizeService.updatePrize(prize);
    }

    @ApiOperation("根据id获取抽奖奖品")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public PrizeVo findById(@PathVariable Long id) {
        return prizeConvert.toVo(prizeService.findById(id));
    }

    @ApiOperation("根据id删除抽奖奖品")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteById(@PathVariable Long id) {
        prizeService.deleteById(id);
    }

    @ApiOperation("抽奖奖品分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page")
    public PageResult<PrizeVo> pagePrize(@RequestBody PrizeSearchVo searchVo) {
        return PageResult.convertPage(prizeService.pagePrize(searchVo), prizeConvert::toVo);
    }

    @ApiOperation("抽奖")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/draw/{activityId}")
    public PrizeRecordsVo drawPrize(@PathVariable Long activityId, @RequestParam String openid) {
        return recordsConvert.toVo(recordsService.drawPrize(activityId, openid));
    }

    @ApiOperation("增加抽奖次数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/increase/drawNumber")
    public void increaseDrawNumber(@RequestBody PrizeDrawNumberVo drawNumberVo) {
        PrizeDrawNumber drawNumber = recordsConvert.toDo(drawNumberVo);
        recordsService.increaseDrawNumber(drawNumber);
    }

    @ApiOperation("查询抽奖次数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/drawNumber/count")
    public int countPrizeDrawNumber(@RequestBody PrizeDrawNumberVo drawNumberVo) {
        return drawNumberService.countPrizeDrawNumber(drawNumberVo);
    }

    @ApiOperation("用户可用次数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/availableNumber/{activityId}")
    public Integer availableNumber(@PathVariable Long activityId, @RequestParam String openid) {
        return recordsService.availableNumber(activityId, openid);
    }

    @ApiOperation("根据id获取抽奖记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/records/get/{id}")
    public PrizeRecordsVo findRecordsById(@PathVariable Long id) {
        return recordsService.findPrizeRecordsVoById(id);
    }

    @ApiOperation("根据uuid获取抽奖记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/records/byUuid")
    public PrizeRecordsVo findByUuid(@RequestParam String uuid) {
        return recordsService.findPrizeRecordsVoByUuid(uuid);
    }

    @ApiOperation("抽奖记录分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/records/page")
    public PageResult<PrizeRecordsVo> pageRecords(@RequestBody PrizeRecordsSearchVo searchVo) {
        return recordsService.pageRecords(searchVo);
    }

    @ApiOperation("根据uuid兑奖")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/verify/prize")
    public void verifyPrize(@RequestParam String uuid) {
        recordsService.verifyPrize(uuid);
    }
}
