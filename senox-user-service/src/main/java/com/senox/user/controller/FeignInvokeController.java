package com.senox.user.controller;

import com.senox.common.constant.SystemParam;
import com.senox.common.domain.SystemSetting;
import com.senox.common.service.SystemSettingService;
import com.senox.common.utils.AesUtils;
import com.senox.common.utils.DateUtils;
import com.senox.common.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/9/12 17:18
 */
@Api(tags = "管理用户")
@RestController
@RequiredArgsConstructor
@RequestMapping("/feignInvoke")
public class FeignInvokeController {

    private final SystemSettingService settingService;

    @ApiOperation("获取feignToken")
    @GetMapping("/get")
    public String getFeignToken() {
        return generateFeignToken();
    }

    /**
     * 生成feign调用的token
     * @return
     */
    private String generateFeignToken() {
        SystemSetting key = settingService.findByParam(SystemParam.FEIGN_KEY);
        SystemSetting iv = settingService.findByParam(SystemParam.FEIGN_IV);
        String rawFeignToken = StringUtils.randStr(5)
                .concat(DateUtils.formatDateTime(LocalDateTime.now(), "yyyyMMddHHmmss"))
                .concat(StringUtils.randStr(5));
        return AesUtils.encrypt(rawFeignToken, key.getValue(), iv.getValue(), 2);
    }
}
