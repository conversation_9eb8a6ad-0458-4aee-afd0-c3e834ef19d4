package com.senox.user.convert;

import com.senox.user.domain.PrizeDrawNumber;
import com.senox.user.domain.PrizeRecords;
import com.senox.user.vo.PrizeDrawNumberVo;
import com.senox.user.vo.PrizeRecordsVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/15 14:12
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface PrizeRecordsConvert {

    PrizeRecords toDo(PrizeRecordsVo vo);

    List<PrizeRecords> toDo(List<PrizeRecordsVo> voList);

    PrizeRecordsVo toVo(PrizeRecords domain);

    List<PrizeRecordsVo> toVo(List<PrizeRecords> domainList);

    PrizeDrawNumber toDo(PrizeDrawNumberVo drawNumberVo);
}
