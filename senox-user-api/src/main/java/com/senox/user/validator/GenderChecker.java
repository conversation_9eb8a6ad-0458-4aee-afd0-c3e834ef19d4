package com.senox.user.validator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 性别类型校验
 * <AUTHOR>
 * @Date 2020/12/29 17:15
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = GenderValidator.class)
public @interface GenderChecker {

    String message() default "Invalid gender.";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
