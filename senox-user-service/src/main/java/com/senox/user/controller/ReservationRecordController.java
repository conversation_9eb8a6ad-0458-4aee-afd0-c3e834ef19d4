package com.senox.user.controller;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.user.convert.ReservationRecordConvert;
import com.senox.user.domain.ReservationRecord;
import com.senox.user.domain.ReservationRecordItem;
import com.senox.user.service.ReservationRecordService;
import com.senox.user.utils.ContextUtils;
import com.senox.user.vo.ReservationRecordSearchVo;
import com.senox.user.vo.ReservationRecordVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/27 18:08
 */
@Api(tags = "预约记录")
@RestController
@RequiredArgsConstructor
@RequestMapping("/reservation/record")
public class ReservationRecordController {

    private final ReservationRecordService reservationRecordService;
    private final ReservationRecordConvert reservationRecordConvert;

    @ApiOperation("添加预约记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addReservationRecord(@Validated(Add.class) @RequestBody ReservationRecordVo recordVo) {
        ReservationRecord reservationRecord = reservationRecordConvert.toDo(recordVo);
        ContextUtils.initEntityCreator(reservationRecord);
        ContextUtils.initEntityModifier(reservationRecord);
        List<ReservationRecordItem> recordItems = new ArrayList<>();
        if (!CollectionUtils.isEmpty(recordVo.getCarNoList())) {
            recordVo.getCarNoList().forEach(item -> {
                ReservationRecordItem recordItem = new ReservationRecordItem();
                recordItem.setCarNo(item);
                recordItems.add(recordItem);
            });
        }
        return reservationRecordService.addReservationRecord(reservationRecord, recordItems);
    }

    @ApiOperation("更新预约记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateParkingRecord(@Validated(Update.class) @RequestBody ReservationRecordVo recordVo) {
        ReservationRecord reservationRecord = reservationRecordConvert.toDo(recordVo);
        ContextUtils.initEntityModifier(reservationRecord);
        List<ReservationRecordItem> recordItems = new ArrayList<>();
        if (!CollectionUtils.isEmpty(recordVo.getCarNoList())) {
            recordVo.getCarNoList().forEach(item -> {
                ReservationRecordItem recordItem = new ReservationRecordItem();
                recordItem.setCarNo(item);
                recordItems.add(recordItem);
            });
        }
        reservationRecordService.updateReservationRecord(reservationRecord, recordItems);
    }

    @ApiOperation("根据id获取预约记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/findById/{id}")
    public ReservationRecordVo findById(@PathVariable Long id) {
        return reservationRecordService.findInfoById(id);
    }

    @ApiOperation("预约记录列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public PageResult<ReservationRecordVo> page(@RequestBody ReservationRecordSearchVo searchVo) {
        return reservationRecordService.page(searchVo);
    }

    @ApiOperation("预约记录同行人数合计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/sum")
    public ReservationRecordVo sumReservationRecord(@RequestBody ReservationRecordSearchVo searchVo) {
        return reservationRecordService.sumReservationRecord(searchVo);
    }
}
