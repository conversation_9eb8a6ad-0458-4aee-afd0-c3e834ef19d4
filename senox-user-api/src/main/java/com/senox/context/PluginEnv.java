package com.senox.context;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/5/24 8:15
 */
@Getter
@Setter
@Builder
@ToString
public class PluginEnv implements Serializable {

    private static final long serialVersionUID = -3096090734860817716L;

    private boolean mq;

    private boolean xxl;

    /**
     * 是否需要mock
     * @return
     */
    @JsonIgnore
    public boolean isNeedMock() {
        return mq || xxl;
    }
}
