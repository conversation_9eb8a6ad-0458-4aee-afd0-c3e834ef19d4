<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.user.mapper.CompanyMapper">
    
    <resultMap id="Result_Company" type="com.senox.user.domain.Company">
        <id property="id" jdbcType="BIGINT" column="id" />
        <result property="companyName" jdbcType="VARCHAR" column="company_name" />
        <result property="orderNo" jdbcType="INTEGER" column="order_no" />
        <result property="disabled" jdbcType="TINYINT" column="is_disabled" />
        <result property="creatorId" jdbcType="BIGINT" column="creator_id" />
        <result property="creatorName" jdbcType="VARCHAR" column="creator_name" />
        <result property="createTime" jdbcType="TIMESTAMP" column="create_time" />
        <result property="modifierId" jdbcType="BIGINT" column="modifier_id" />
        <result property="modifierName" jdbcType="VARCHAR" column="modifier_name" />
        <result property="modifiedTime" jdbcType="TIMESTAMP" column="modified_time" />
    </resultMap>

    <!-- 添加企业 -->
    <insert id="addCompany" parameterType="com.senox.user.domain.Company" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO u_company(
            company_name, order_no, creator_id, creator_name, create_time, modifier_id, modifier_name, modified_time
        ) VALUES (
            #{companyName}, #{orderNo}, #{creatorId}, #{creatorName}, NOW(), #{modifierId}, #{modifierName}, NOW()
        )
    </insert>

    <!-- 更新企业 -->
    <update id="updateCompany" parameterType="com.senox.user.domain.Company">
        UPDATE u_company
        <set>
            <if test="companyName != null and companyName != ''">
                , company_name = #{companyName}
            </if>
            <if test="orderNo != null">
                , order_no = #{orderNo}
            </if>
            <if test="disabled != null">
                , is_disabled = #{disabled}
            </if>
            , modifier_id = #{modifierId}
            , modifier_name = #{modifierName}
            , modified_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据id查找企业 -->
    <select id="findById" parameterType="java.lang.Long" resultMap="Result_Company">
        SELECT id, company_name, order_no, is_disabled FROM u_company WHERE id = #{id}
    </select>

    <!-- 根据名称查找企业 -->
    <select id="findByName" parameterType="java.lang.String" resultMap="Result_Company">
        SELECT id, company_name, order_no, is_disabled FROM u_company WHERE company_name = #{name}
    </select>

    <!-- 企业列表 -->
    <select id="listAll" resultMap="Result_Company">
        SELECT id, company_name, modified_time FROM u_company WHERE is_disabled = 0 ORDER BY order_no
    </select>

    <!-- 代报餐的企业 -->
    <select id="listDelegateCompany" resultMap="Result_Company">
        SELECT c.id, c.company_name FROM u_company c
        WHERE c.id IN (SELECT d.delegate_id FROM u_employee_meal_delegate d WHERE d.type = 2)
    </select>

    <!-- 未报餐的代理企业 -->
    <select id="listNoBookedDelegateCompany" parameterType="java.time.LocalDate" resultMap="Result_Company">
        SELECT c.id, c.company_name
        FROM u_company c
            LEFT JOIN wx_booking_meal m ON m.delegate_company = c.id AND m.meal_date = #{mealDate}
        WHERE c.id IN (SELECT d.delegate_id FROM u_employee_meal_delegate d WHERE d.type = 2)
            AND m.id IS NULL
    </select>

</mapper>