package com.senox.user.api.clients;

import com.senox.common.vo.PageResult;
import com.senox.user.api.UserServiceUrl;
import com.senox.user.vo.FeedBackReplyVo;
import com.senox.user.vo.FeedBackSearchVo;
import com.senox.user.vo.FeedBackVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2023/6/1 9:17
 */
@FeignClient(UserServiceUrl.SERVICE_NAME)
public interface FeedBackClient {

    /**
     * 添加建议反馈
     * @param feedBackVo
     * @return
     */
    @PostMapping(UserServiceUrl.FEED_BACK_ADD)
    Long addFeedBack(@RequestBody FeedBackVo feedBackVo);

    /**
     * 获取意见反馈
     * @param id
     * @param isDetail
     * @return
     */
    @GetMapping(UserServiceUrl.FEED_BACK_GET)
    FeedBackVo findFeedBackById(@PathVariable Long id, @PathVariable Boolean isDetail);

    /**
     * 意见反馈列表
     * @param search
     * @return
     */
    @PostMapping(UserServiceUrl.FEED_BACK_LIST)
    PageResult<FeedBackVo> listFeedBack(@RequestBody FeedBackSearchVo search);

    /**
     * 添加建议回复
     * @param feedBackReplyVo
     * @return
     */
    @PostMapping(UserServiceUrl.FEED_BACK_REPLY_ADD)
    Long addFeedBackReply(@RequestBody FeedBackReplyVo feedBackReplyVo);

    /**
     * 获取建议回复
     * @param id
     * @return
     */
    @GetMapping(UserServiceUrl.FEED_BACK_REPLY_GET)
    FeedBackReplyVo findFeedBackReplyById(@PathVariable Long id);
}
