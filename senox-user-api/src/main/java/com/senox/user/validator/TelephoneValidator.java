package com.senox.user.validator;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.regex.Pattern;

/**
 * 手机号校验
 * <AUTHOR>
 * @Date 2020/12/29 17:27
 */
public class TelephoneValidator implements ConstraintValidator<TelephoneChecker, String> {

    private static final Pattern TELEPHONE_PATTERN = Pattern.compile("^1[3456789]\\d{9}$");

    @Override
    public boolean isValid(String s, ConstraintValidatorContext constraintValidatorContext) {
        if (s == null || s.length() < 1) {
            return true;
        }

        return TELEPHONE_PATTERN.matcher(s).matches();
    }
}
