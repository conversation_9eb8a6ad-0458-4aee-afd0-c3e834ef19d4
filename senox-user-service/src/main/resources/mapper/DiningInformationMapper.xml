<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.user.mapper.DiningInformationMapper">

    <resultMap id="Result_DiningInformationVo" type="com.senox.user.vo.DiningInformationVo">
        <id property="id" jdbcType="BIGINT" column="id" />
        <result property="employeeName" jdbcType="VARCHAR" column="employee_name" />
        <result property="departmentId" jdbcType="BIGINT" column="department_id"/>
        <result property="departmentName" jdbcType="VARCHAR" column="full_name"/>
        <result property="mealDate" jdbcType="TIMESTAMP" column="meal_date" />
        <result property="mealTime" jdbcType="VARCHAR" column="meal_time" />
        <result property="dining" jdbcType="TINYINT" column="dining" />
        <result property="companyName" jdbcType="VARCHAR" column="company_name"/>
        <result property="modifiedTime" jdbcType="TIMESTAMP" column="modified_time" />
    </resultMap>

    <resultMap id="Result_DiningInformation" type="com.senox.user.domain.DiningInformation">
        <id property="id" jdbcType="BIGINT" column="id" />
        <result property="employeeName" jdbcType="VARCHAR" column="employee_name" />
        <result property="mealDate" jdbcType="TIMESTAMP" column="meal_date" />
        <result property="mealTime" jdbcType="VARCHAR" column="meal_time" />
        <result property="dining" jdbcType="TINYINT" column="dining" />
        <result property="disabled" jdbcType="TINYINT" column="disabled" />
        <result property="creatorId" jdbcType="BIGINT" column="creator_id" />
        <result property="creatorName" jdbcType="VARCHAR" column="creator_name" />
        <result property="createTime" jdbcType="TIMESTAMP" column="create_time" />
        <result property="modifierId" jdbcType="BIGINT" column="modifier_id" />
        <result property="modifierName" jdbcType="VARCHAR" column="modifier_name" />
        <result property="modifiedTime" jdbcType="TIMESTAMP" column="modified_time" />
    </resultMap>



    <select id="count" parameterType="com.senox.user.vo.DiningInformationSearchVo" resultType="java.lang.Integer">
        SELECT count(d.id)
        FROM u_dining_information d
            INNER JOIN  u_employee e ON d.employee_name = e.username
            LEFT JOIN u_department ed ON e.department_id = ed.id
        <where>
            <if test="employeeName != null and employeeName != ''">
                AND d.employee_name = #{employeeName}
            </if>
            <if test="departmentId != null ">
                AND e.department_id = #{departmentId}
            </if>
            <if test="dateStart != null">
                AND d.meal_date >= #{dateStart}
            </if>
            <if test="dateEnd != null">
                AND d.meal_date <![CDATA[<=]]> #{dateEnd}
            </if>
            <if test="dining != null">
                <choose>
                    <when test="dining">
                        AND d.dining = 1
                    </when>
                    <otherwise>
                        AND d.dining = 0
                    </otherwise>
                </choose>
            </if>
            <if test="company != null">
                AND e.company_name = #{company}
            </if>
        </where>
    </select>


    <select id="list" parameterType="com.senox.user.vo.DiningInformationSearchVo" resultMap="Result_DiningInformationVo">
        SELECT d.id, d.employee_name, d.meal_date, d.meal_time, d.dining, d.modified_time, e.company_name, ed.full_name
        FROM u_dining_information d
            INNER JOIN  u_employee e ON d.employee_name = e.username
            LEFT JOIN u_department ed ON e.department_id = ed.id
        <where>
            <if test="employeeName != null and employeeName != ''">
                AND d.employee_name = #{employeeName}
            </if>
            <if test="departmentId != null ">
                AND e.department_id = #{departmentId}
            </if>
            <if test="dateStart != null">
                AND d.meal_date >= #{dateStart}
            </if>
            <if test="dateEnd != null">
                AND d.meal_date <![CDATA[<=]]> #{dateEnd}
            </if>
            <if test="dining != null">
                <choose>
                    <when test="dining">
                        AND d.dining = 1
                    </when>
                    <otherwise>
                        AND d.dining = 0
                    </otherwise>
                </choose>
            </if>
            <if test="company != null">
                AND e.company_name = #{company}
            </if>
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}, d.id
            </when>
            <otherwise>
                ORDER BY d.meal_date DESC, d.id
            </otherwise>
        </choose>
        LIMIT ${offset}, ${pageSize}
    </select>


    <select id="diningInformationList" parameterType="java.util.List" resultMap="Result_DiningInformation">
        SELECT id, employee_name, meal_date, meal_time, dining
        FROM u_dining_information
        WHERE (employee_name, meal_date) IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            (#{item.employeeName}, #{item.mealDate})
        </foreach>
    </select>

    <select id="findById" parameterType="java.lang.Long" resultMap="Result_DiningInformationVo">
        SELECT d.id , d.employee_name, d.meal_date, d.meal_time, d.dining, d.modified_time, e.company_name , e.department_id
        FROM u_dining_information d
            INNER JOIN u_employee e ON d.employee_name = e.username
        where d.id = #{id}
    </select>

</mapper>