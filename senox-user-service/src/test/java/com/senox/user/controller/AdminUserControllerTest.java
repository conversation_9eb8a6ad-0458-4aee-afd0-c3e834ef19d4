package com.senox.user.controller;

import com.senox.user.BaseControllerTest;
import com.senox.user.constant.Gender;
import com.senox.user.vo.AdminUserVo;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;



/**
 * <AUTHOR>
 * @Date 2020/12/30 11:50
 */
public class AdminUserControllerTest extends BaseControllerTest {

    private static final Logger logger = LoggerFactory.getLogger(AdminUserControllerTest.class);

    @Test
    public void testAddAndFind() {
        AdminUserVo adminUserVo1 = mockAdminUserVo();
        HttpEntity<AdminUserVo> entity1 = new HttpEntity<>(adminUserVo1);
        ResponseEntity<Long> responseEntity1 = restTemplate.exchange("/adminUser/add", HttpMethod.POST,
                entity1, new ParameterizedTypeReference<Long>() {
                });
        Long result1 = responseEntity1.getBody();
        Assertions.assertTrue(result1 > 0L);

        ResponseEntity<AdminUserVo> responseEntity2 = restTemplate.exchange("/adminUser/get/" + result1,
                HttpMethod.GET, null, new ParameterizedTypeReference<AdminUserVo>() {
                });
        AdminUserVo result2 = responseEntity2.getBody();
        logger.info("result2: {}", result2);
        Assertions.assertEquals(adminUserVo1.getUsername(), result2.getUsername());
        Assertions.assertEquals(adminUserVo1.getEmail(), result2.getEmail());
        Assertions.assertEquals(adminUserVo1.getTelephone(), result2.getTelephone());
        Assertions.assertEquals(adminUserVo1.getGender(), result2.getGender());
        Assertions.assertEquals(adminUserVo1.getAvatar(), result2.getAvatar());

        AdminUserVo adminUserVo3 = new AdminUserVo();
        adminUserVo3.setId(result1);
        adminUserVo3.setEmail(randStr(8) + "@" + randStr(4) + ".com");
        adminUserVo3.setTelephone("13" + randNumStr(9));
        HttpEntity<AdminUserVo> entity3 =  new HttpEntity<>(adminUserVo3);
        ResponseEntity<Void> responseEntity3 = restTemplate.exchange("/adminUser/update", HttpMethod.POST,
                entity3, new ParameterizedTypeReference<Void>() {
                });
        logger.info("result3: {}", responseEntity3);

        ResponseEntity<AdminUserVo> responseEntity4 = restTemplate.exchange("/adminUser/get/" + result1,
                HttpMethod.GET, null, new ParameterizedTypeReference<AdminUserVo>() {
                });
        AdminUserVo result4 = responseEntity4.getBody();
        logger.info("result4: {}", result4);
        Assertions.assertEquals(adminUserVo1.getUsername(), result4.getUsername());
        Assertions.assertEquals(adminUserVo3.getEmail(), result4.getEmail());
        Assertions.assertEquals(adminUserVo3.getTelephone(), result4.getTelephone());
        Assertions.assertEquals(adminUserVo1.getGender(), result4.getGender());
        Assertions.assertEquals(adminUserVo1.getAvatar(), result4.getAvatar());
    }

    private AdminUserVo mockAdminUserVo() {
        AdminUserVo result = new AdminUserVo();
        result.setUsername(randStr(10));
        result.setRealName(randStr(10));
        result.setPassword(randStr(10));
        result.setGender(Gender.fromValue(randInt(1, Gender.values().length)).getValue());
        result.setEmail(randStr(8) + "@" + randStr(3) + ".com");
        result.setTelephone("13" + randNumStr(9));
        result.setAvatar(randStr(100));
        return result;
    }

}
