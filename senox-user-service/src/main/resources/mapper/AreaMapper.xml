<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.user.mapper.AreaMapper">

    <resultMap id="Result_Area" type="com.senox.user.domain.Area">
        <id property="id" jdbcType="BIGINT" column="id" />
        <result property="serialNo" jdbcType="VARCHAR" column="serial_no" />
        <result property="name" jdbcType="VARCHAR" column="name" />
        <result property="briefName" jdbcType="VARCHAR" column="brief_name" />
        <result property="category" jdbcType="TINYINT" column="category" />
        <result property="parentId" jdbcType="BIGINT" column="parent_id" />
        <result property="creatorId" jdbcType="BIGINT" column="creator_id" />
        <result property="creatorName" jdbcType="VARCHAR" column="creator_name" />
        <result property="createTime" jdbcType="TIMESTAMP" column="create_time" />
        <result property="modifierId" jdbcType="BIGINT" column="modifier_id" />
        <result property="modifierName" jdbcType="VARCHAR" column="modifier_name" />
        <result property="modifiedTime" jdbcType="TIMESTAMP" column="modified_time" />
    </resultMap>

    <!-- 添加地区信息 -->
    <insert id="addArea" parameterType="com.senox.user.domain.Area" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO dict_area(
            serial_no, name, brief_name, category, parent_id, creator_id, creator_name, create_time, modifier_id, modifier_name, modified_time
        ) VALUES (
            #{serialNo}, #{name}, #{briefName}, #{category}, #{parentId}, #{creatorId}, #{creatorName}, NOW(), #{modifierId}, #{modifierName}, NOW()
        )
    </insert>

    <!-- 更新地区信息 -->
    <update id="updateArea" parameterType="com.senox.user.domain.Area">
        UPDATE dict_area
        <set>
            <if test="serialNo != null and serialNo != ''">
                , serial_no = #{serialNo}
            </if>
            <if test="name != null and name != ''">
                , name = #{name}
            </if>
            <if test="briefName != null">
                , brief_name = #{briefName}
            </if>
            <if test="category != null">
                , category = #{category}
            </if>
            <if test="parentId != null">
                , parent_id = #{parentId}
            </if>
            <if test="disabled != null">
                , is_disabled = #{disabled}
            </if>
            , modifier_id = #{modifierId}
            , modifier_name = #{modifierName}
            , modified_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 查找地区 -->
    <select id="findById" parameterType="java.lang.Long" resultMap="Result_Area">
        SELECT id, serial_no, name, brief_name, category, parent_id
        FROM dict_area
        WHERE id = #{id}
    </select>

    <!-- 根据编号查找id -->
    <select id="findIdBySerialNo" parameterType="java.lang.String" resultType="java.lang.Long">
        SELECT id FROM dict_area WHERE serial_no = #{serialNo}
    </select>

    <!-- 查找地区子列表 -->
    <select id="listByParentIdAndCategory" resultMap="Result_Area">
        SELECT id, serial_no, name, brief_name, category, parent_id
        FROM dict_area
        WHERE parent_id = #{parentId}
            AND is_disabled = 0
            AND category in <foreach collection="categories" item="item" separator="," open="(" close=")">#{item}</foreach>
    </select>

    <select id="listArea" resultMap="Result_Area">
        SELECT id, serial_no, name, brief_name, category, parent_id
        FROM dict_area
        WHERE is_disabled = 0
    </select>

</mapper>