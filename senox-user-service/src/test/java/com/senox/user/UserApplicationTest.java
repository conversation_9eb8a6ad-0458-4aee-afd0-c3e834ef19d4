package com.senox.user;


import com.senox.user.config.AppConfig;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @Date 2020/12/22 15:54
 */
@SpringBootTest(classes = UserApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class UserApplicationTest {

    private static final Logger logger = LoggerFactory.getLogger(UserApplicationTest.class);

    @Autowired
    private AppConfig appConfig;

    @Test
    public void contextLoads() {
        logger.info("appConfig key: {}", appConfig.getTokenKey());
        logger.info("appConfig iv: {}", appConfig.getTokenIv());
    }

}