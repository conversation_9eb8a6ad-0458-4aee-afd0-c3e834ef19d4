package com.senox.user.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.context.AdminUserDto;
import com.senox.user.constant.UserConst;
import com.senox.user.domain.FeedBackReply;
import com.senox.user.mapper.FeedBackReplyMapper;
import com.senox.user.vo.FeedBackMessageVo;
import com.senox.user.vo.FeedBackReplyStateVo;
import com.senox.user.vo.FeedBackReplyVo;
import com.senox.user.vo.FeedBackVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/6/1 9:42
 */
@Slf4j
@AllArgsConstructor
@Service
public class FeedBackReplyService extends ServiceImpl<FeedBackReplyMapper, FeedBackReply> {

    private final FeedBackService feedBackService;
    private final RabbitTemplate rabbitTemplate;

    /**
     * 添加建议回复
     *
     * @param feedBackReply
     * @param adminUserDto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addFeedBackReply(FeedBackReply feedBackReply, AdminUserDto adminUserDto) {
        if (feedBackReply.getCreateTime() == null) {
            feedBackReply.setCreateTime(LocalDateTime.now());
        }
        if (feedBackReply.getModifiedTime() == null) {
            feedBackReply.setModifiedTime(LocalDateTime.now());
        }
        save(feedBackReply);
        FeedBackReplyStateVo stateVo = new FeedBackReplyStateVo();
        stateVo.setId(feedBackReply.getFeedBackId());
        if (StringUtils.isBlank(feedBackReply.getOpenid())) {
            stateVo.setReplyState(Boolean.TRUE);
            feedBackService.updateFeedBackState(stateVo, adminUserDto);

            FeedBackVo feedBackVo = feedBackService.getFeedBackResultById(feedBackReply.getFeedBackId(), false);
            FeedBackMessageVo messageVo = new FeedBackMessageVo(feedBackReply.getId(), feedBackVo.getOpenid());
            rabbitTemplate.convertAndSend(UserConst.MQ.MQ_USER_AC_FEED_BACK, messageVo);
            log.info("发送意见反馈消息队列 {}", JsonUtils.object2Json(messageVo));
        } else {
            stateVo.setReplyState(Boolean.FALSE);
            feedBackService.updateFeedBackState(stateVo, adminUserDto);
        }
        return feedBackReply.getId();
    }

    /**
     * 获取建议回复
     *
     * @param id
     * @return
     */
    public FeedBackReplyVo findFeedBackReplyById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }

        return getBaseMapper().findFeedBackReplyById(id);
    }
}
