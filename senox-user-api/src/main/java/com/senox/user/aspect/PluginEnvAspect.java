package com.senox.user.aspect;
import com.senox.context.AdminContext;
import com.senox.context.AdminUserDto;
import com.senox.context.PluginEnv;
import com.senox.user.annotation.PluginEnvBuilder;
import com.senox.user.component.FeignInvokeComponent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @date 2024/9/3 9:50
 */
@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
public class PluginEnvAspect {

    private final FeignInvokeComponent feignInvokeComponent;

    @Around(value = "@annotation(com.senox.user.annotation.PluginEnvBuilder)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        try {
            log.info("pluginEnv context user set start ======> ");
            PluginEnvBuilder pluginEnv = getAnnotation(joinPoint);
            AdminContext.setPluginEnv(buildPluginEnv(pluginEnv.value()));
            setContextUser();
            log.info("pluginEnv context user set end ======> ");
            return joinPoint.proceed();
        } catch (Exception e) {
            log.error("Exception during pluginEnv context user set execution: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 获取注解
     * @param joinPoint
     * @return
     */
    private PluginEnvBuilder getAnnotation(JoinPoint joinPoint){
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        return method.getAnnotation(PluginEnvBuilder.class);
    }


    private PluginEnv buildPluginEnv(String args) {
        log.info("pluginEnv args: {}", args);
        PluginEnv pluginEnv = PluginEnv.builder().build();
        if (args.equalsIgnoreCase("mq")) {
            pluginEnv.setMq(true);
        } else if (args.equalsIgnoreCase("xxl")) {
            pluginEnv.setXxl(true);
        }
        return pluginEnv;
    }

    /**
     * 设置上下文用户信息
     */
    public void setContextUser() {
        log.info("【AdminContext user】 ：{} ", AdminContext.getUser());
        if (!AdminContext.isUserValid()) {
            if (AdminContext.isNeedMock()) {
                AdminUserDto adminUser = new AdminUserDto();
                adminUser.setUserId(1L);
                adminUser.setUsername("admin");
                //生成feignToken
                String feignToken = feignInvokeComponent.getFeignToken();
                log.info("【Feign invoke token】 feignToken： {}", feignToken);
                adminUser.setFeignToken(feignInvokeComponent.getFeignToken());
                AdminContext.setUser(adminUser);
            } else {
                throw new RuntimeException("用户未授权");
            }
        }
    }
}
