<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.senox.user</groupId>
    <artifactId>senox-user</artifactId>
    <version>1.2.7-SNAPSHOT</version>
    <packaging>pom</packaging>

    <modules>
        <module>senox-user-api</module>
        <module>senox-user-service</module>
    </modules>

    <parent>
        <artifactId>senox-base</artifactId>
        <groupId>com.senox</groupId>
        <version>1.2.6-SNAPSHOT</version>
    </parent>

    <properties>
        <senox-base.version>1.2.6-SNAPSHOT</senox-base.version>
        <senox-realty.version>1.3.0-SNAPSHOT</senox-realty.version>
        <senox-device.version>1.2.2-SNAPSHOT</senox-device.version>
        <senox-tms.version>1.1.3-SNAPSHOT</senox-tms.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.senox</groupId>
                <artifactId>api</artifactId>
                <version>${senox-base.version}</version>
            </dependency>

            <dependency>
                <groupId>com.senox</groupId>
                <artifactId>common</artifactId>
                <version>${senox-base.version}</version>
            </dependency>

            <dependency>
                <groupId>com.senox.realty</groupId>
                <artifactId>senox-realty-api</artifactId>
                <version>${senox-realty.version}</version>
            </dependency>

            <dependency>
                <groupId>com.senox.dm</groupId>
                <artifactId>senox-device-api</artifactId>
                <version>${senox-device.version}</version>
            </dependency>

            <dependency>
                <groupId>com.senox.tms</groupId>
                <artifactId>senox-tms-api</artifactId>
                <version>${senox-tms.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>
