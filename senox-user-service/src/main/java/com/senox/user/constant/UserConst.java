package com.senox.user.constant;

import com.senox.context.AdminUserDto;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2021/1/8 14:22
 */
public class UserConst {

    private UserConst() {
    }

    /**
     * token 分割字符串
     */
    public static final String TOKEN_AUTH_SPLIT_STR = "##";
    /**
     * token 格式
     */
    public static final String TOKEN_AUTH = "%s" + TOKEN_AUTH_SPLIT_STR + "%s" + TOKEN_AUTH_SPLIT_STR + "%s";
    /**
     * 默认排序号
     */
    public static final int ORDER_DF_50 = 50;
    /**
     * 批量处理数量
     */
    public static final int BATCH_DEAL_SIZE = 2000;

    public static final AdminUserDto SYNCER = new AdminUserDto();
    static {
        SYNCER.setUserId(1L);
        SYNCER.setUsername("mockAdmin");
    }


    /**
     * 缓存
     */
    public static class Cache {
        private Cache() {
        }

        /**
         * 用户缓存key
         */
        public static final String KEY_USER = "senox:adminUser:%s";
        /**
         * 用户feign缓存key
         */
        public static final String KEY_FEIGN_USER = "senox:feign:adminUser:%s";
        /**
         * 微信管理员
         */
        public static final String KEY_ADMIN_WECHAT = "senox:adminUser:wechat:%s:%s";
        /**
         * 微信用户
         */
        public static final String KEY_WXUSER = "senox:wechat:user:%s:%s";
        /**
         * 客户编号
         */
        public static final String KEY_CUSTOMER_SERIALNO = "senox:customer:serialNo";
        /**
         * 无效的token
         */
        public static final String KEY_INVALID_TOKEN = "senox:invalidToken:%s";
        /**
         * 无效的feignToken
         */
        public static final String KEY_INVALID_FEIGN_TOKEN = "senox:invalidFeignToken:%s";
        /**
         * 用户角色
         */
        public static final String KEY_USER_ROLES = "senox:userRole:%s";
        /**
         * 省/市/区列表缓存
         */
        public static final String KEY_AREA = "senox:area:%s";
        /**
         * 行业
         */
        public static final String KEY_PROFESSION = "senox:profession";
        /**
         * 企业
         */
        public static final String KEY_COMPANY = "senox:company";
        /**
         * 部门
         */
        public static final String KEY_DEPARTMENT = "senox:department:%s";
        /**
         * 部门
         */
        public static final String KEY_DEPARTMENT_ALL = "senox:department:all";
        /**
         * 微信绑定用户信息
         */
        public static final String KEY_BIND_EMPLOYEE = "senox:wechat:employee:%s";
        /**
         * 经营范围
         */
        public static final String KEY_BUSINESS_CATEGORY = "senox:businessCategory";

        /**
         * 凭证
         */
        public static final String KEY_ADMIN_CREDENTIALS = "senox:adminUser:credentials:%s";

        /**
         * 认证用户
         */
        public static final String KEY_OAUTH_USER = "senox:oauth:user:%s";

        /**
         * 认证用户token
         */
        public static final String KEY_OAUTH_USER_TOKEN = "senox:oauth:token:%s";

        /**
         * 商户使用申请锁
         */
        public static final String KEY_MERCHANT_APPLY_LOCK = "senox:merchant:apply:lock:%s";

        /**
         * 投票可用数
         */
        public static final String VOTE_NUMBERS = "senox:vote:numbers:%s:%s:%s";
        /**
         * 投票限制数
         */
        public static final String VOTE_LIMIT_NUMBERS = "senox:vote:limit:numbers:%s";

        /**
         * 投票可用数
         */
        public static final String PRIZE_DRAW_NUMBERS = "senox:prizeDraw:numbers:%s:%s";

        /**
         * 缓存10min
         */
        public static final long TTL_10M = TimeUnit.MINUTES.toSeconds(10L);

        /**
         * 缓存1h
         */
        public static final long TTL_1H = TimeUnit.HOURS.toSeconds(1L);

        /**
         * 用户缓存时长
         */
        public static final long TTL_2H = TimeUnit.HOURS.toSeconds(2);
        /**
         * 省/市/区列表缓存时长
         */
        public static final long TTL_7D = TimeUnit.DAYS.toSeconds(7);
    }

    public static class MQ {
        private MQ() {
        }

        /**
         * 门禁设备权限授权
         */
        public static final String EX_DEVICE_AC_RESIDENT_RIGHT_AUTH = "device.ac.right.resident.auth.exchange";
        public static final String MQ_DEVICE_AC_RESIDENT_RIGHT_AUTH = "device.ac.right.resident.auth.queue";

        /**
         * 意见反馈消息
         */
        public static final String EX_USER_AC_FEED_BACK = "user.ac.feed.back.exchange";
        public static final String MQ_USER_AC_FEED_BACK = "user.ac.feed.back.queue";

        public static final String MQ_MERCHANT_APPLY_AUDIT = "merchant.authApply.audit.queue";
    }

}
