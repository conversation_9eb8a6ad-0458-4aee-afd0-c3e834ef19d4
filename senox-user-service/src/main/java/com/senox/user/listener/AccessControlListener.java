package com.senox.user.listener;

import com.fasterxml.jackson.core.type.TypeReference;
import com.rabbitmq.client.Channel;
import com.senox.common.utils.JsonUtils;
import com.senox.dm.vo.EmployeeAccessStateVo;
import com.senox.user.constant.UserConst;
import com.senox.user.service.ResidentAccessService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2023/5/30 13:46
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AccessControlListener {

    private final ResidentAccessService residentAccessService;

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = UserConst.MQ.MQ_DEVICE_AC_RESIDENT_RIGHT_AUTH, durable = "true", exclusive = "false", autoDelete = "false"),
            exchange = @Exchange(value = UserConst.MQ.EX_DEVICE_AC_RESIDENT_RIGHT_AUTH, type = ExchangeTypes.TOPIC),
            key = UserConst.MQ.MQ_DEVICE_AC_RESIDENT_RIGHT_AUTH
    ), containerFactory = "containerFactory")
    public void accessRightAuthListener(Message message, Channel channel) throws IOException {
        String mqMessage = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("收到门禁授权成功消息 {}", mqMessage);

        EmployeeAccessStateVo accessAuthState = JsonUtils.json2GenericObject(mqMessage, new TypeReference<EmployeeAccessStateVo>() {});
        residentAccessService.updateResidentAccessState(accessAuthState);
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
    }
}
