<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.user.mapper.CustomerCovid19Mapper">

    <resultMap id="Result_CustomerCovid19" type="com.senox.user.domain.CustomerCovid19">
        <id property="id" jdbcType="BIGINT" column="id" />
        <result property="customerId" jdbcType="BIGINT" column="customer_id" />
        <result property="category" jdbcType="TINYINT" column="category" />
        <result property="operateDate" jdbcType="TIMESTAMP" column="operate_date" />
        <result property="remark" jdbcType="VARCHAR" column="remark" />
        <result property="disabled" jdbcType="TINYINT" column="is_disabled" />
        <result property="creatorId" jdbcType="BIGINT" column="creator_id" />
        <result property="creatorName" jdbcType="VARCHAR" column="creator_name" />
        <result property="createTime" jdbcType="TIMESTAMP" column="create_time" />
        <result property="modifierId" jdbcType="BIGINT" column="modifier_id" />
        <result property="modifierName" jdbcType="VARCHAR" column="modifier_name" />
        <result property="modifiedTime" jdbcType="TIMESTAMP" column="modified_time" />
    </resultMap>

    <!-- 添加客户新冠防控记录 -->
    <insert id="addCustomerCovid19" parameterType="com.senox.user.domain.CustomerCovid19">
        INSERT INTO u_customer_covid19(
            customer_id, category, operate_date, remark, creator_id, creator_name, create_time, modifier_id, modifier_name, modified_time
        ) VALUES (
            #{customerId}, #{category}, #{operateDate}, #{remark}, #{creatorId}, #{creatorName}, NOW(), #{modifierId}, #{modifierName}, NOW()
        )
    </insert>

    <!-- 批量添加客户新冠防控记录 -->
    <insert id="batchAddCustomerCovid19" parameterType="com.senox.user.domain.CustomerCovid19">
        INSERT INTO u_customer_covid19(
            customer_id, category, operate_date, remark, creator_id, creator_name, create_time, modifier_id, modifier_name, modified_time
        ) VALUES
        <foreach collection="covid19List" item="item" separator=",">
        (
            #{item.customerId}, #{item.category}, #{item.operateDate}, #{item.remark}, #{item.creatorId}, #{item.creatorName}, NOW()
            , #{item.modifierId}, #{item.modifierName}, NOW()
        )
        </foreach>
    </insert>

    <!-- 删除客户新冠防控记录 -->
    <update id="deleteCustomerCovid19">
        DELETE FROM u_customer_covid19
        WHERE customer_id = #{customerId}
            AND id IN <foreach collection="ids" item="item" separator="," open="(" close=")">#{item}</foreach>
    </update>

    <!-- 客户新冠防控记录列表 -->
    <select id="listCustomerCovid19" resultMap="Result_CustomerCovid19">
        SELECT id, customer_id, category, operate_date, remark
        FROM u_customer_covid19
        WHERE customer_id = #{customerId}
        <if test="category != null">
            AND category = #{category}
        </if>
            AND is_disabled = 0
    </select>
</mapper>