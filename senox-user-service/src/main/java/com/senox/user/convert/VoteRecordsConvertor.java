package com.senox.user.convert;

import com.senox.user.domain.VoteRecords;
import com.senox.user.vo.VoteRecordsVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/16 11:09
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface VoteRecordsConvertor {

    VoteRecords toDo(VoteRecordsVo vo);

    List<VoteRecords> toDo(List<VoteRecordsVo> voList);

    VoteRecordsVo toVo(VoteRecords domain);

    List<VoteRecordsVo> toVo(List<VoteRecords> domainList);
}
