package com.senox.user.authcredentials.annotation;


import com.senox.user.authcredentials.handler.AuthCredentialsHandler;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @date 2023-8-28
 */
@Inherited
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface AuthCredentialsIndicate {

    /**
     * 接收参数解密
     */
    boolean paramDecrypt() default true;

    /**
     * 发送参数加密
     */
    boolean paramEncrypt() default true;

    /**
     * 解密处理器
     */
    Class<? extends AuthCredentialsHandler> decryptHandler();

}

