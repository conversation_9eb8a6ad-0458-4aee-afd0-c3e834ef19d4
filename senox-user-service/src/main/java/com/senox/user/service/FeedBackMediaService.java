package com.senox.user.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.user.domain.FeedBackMedia;
import com.senox.user.mapper.FeedBackMediaMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/6/9 16:19
 */
@Service
public class FeedBackMediaService extends ServiceImpl<FeedBackMediaMapper, FeedBackMedia> {


    @Transactional(rollbackFor = Exception.class)
    public void saveFeedBackMedia(Long feedBackId, List<String> medias) {
        if (!WrapperClassUtils.biggerThanLong(feedBackId, 0L)) {
            log.warn("Invalid Maintain media with unspecified orderId");
            return;
        }

        List<FeedBackMedia> list = medias.stream()
                .map(x -> newFeedBackMedia(feedBackId, x))
                .collect(Collectors.toList());
        DataSepDto<FeedBackMedia> sepData = compareAndSeparateFeedBackMedia(listByFeedBackId(feedBackId), list);

        if (!CollectionUtils.isEmpty(sepData.getAddList())) {
            saveBatch(sepData.getAddList());
        }
        if (!CollectionUtils.isEmpty(sepData.getRemoveList())) {
            removeByIds(sepData.getRemoveList().stream().map(FeedBackMedia::getId).collect(Collectors.toList()));
        }
    }

    /**
     * 比较区分
     *
     * @param srcList
     * @param targetList
     * @return
     */
    private DataSepDto<FeedBackMedia> compareAndSeparateFeedBackMedia(List<FeedBackMedia> srcList, List<FeedBackMedia> targetList) {
        List<FeedBackMedia> addList = null;
        List<FeedBackMedia> removeList = null;
        if (CollectionUtils.isEmpty(srcList)) {
            addList = targetList;

        } else if (CollectionUtils.isEmpty(targetList)) {
            removeList = srcList;

        } else {
            addList = targetList.stream().filter(x -> !srcList.contains(x)).collect(Collectors.toList());
            removeList = srcList.stream().filter(x -> !targetList.contains(x)).collect(Collectors.toList());
        }

        DataSepDto<FeedBackMedia> result = new DataSepDto<>();
        result.setAddList(addList);
        result.setRemoveList(removeList);
        return result;
    }

    /**
     * 根据意见反馈id查找多媒体信息
     *
     * @param feedBackId
     * @return
     */
    public List<FeedBackMedia> listByFeedBackId(Long feedBackId) {
        if (!WrapperClassUtils.biggerThanLong(feedBackId, 0L)) {
            return Collections.emptyList();
        }

        Wrapper<FeedBackMedia> wrapper = new QueryWrapper<FeedBackMedia>().lambda()
                .eq(FeedBackMedia::getFeedBackId, feedBackId);
        return getBaseMapper().selectList(wrapper);
    }

    /**
     * 多媒体信息
     *
     * @param feedBackId
     * @param media
     * @return
     */
    private FeedBackMedia newFeedBackMedia(Long feedBackId, String media) {
        FeedBackMedia result = new FeedBackMedia(feedBackId, media);
        result.setModifiedTime(LocalDateTime.now());
        return result;
    }

}
