package com.senox.user.controller;

import com.senox.common.annotation.Debounce;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.vo.PageResult;
import com.senox.user.convert.FeedBackConvertor;
import com.senox.user.convert.FeedBackReplyConvertor;
import com.senox.user.domain.FeedBack;
import com.senox.user.domain.FeedBackReply;
import com.senox.user.service.FeedBackReplyService;
import com.senox.user.service.FeedBackService;
import com.senox.user.vo.FeedBackReplyVo;
import com.senox.user.vo.FeedBackSearchVo;
import com.senox.user.vo.FeedBackVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2023/6/1 8:34
 */
@Api(tags = "建议反馈管理")
@AllArgsConstructor
@RestController
@RequestMapping("/feed/back")
public class FeedBackController extends BaseController {

    private final FeedBackService feedBackService;
    private final FeedBackConvertor feedBackConvertor;
    private final FeedBackReplyService feedBackReplyService;
    private final FeedBackReplyConvertor feedBackReplyConvertor;

    @ApiOperation("添加建议反馈")
    @PostMapping("/add")
    @Debounce(expire = 5)
    public Long addFeedBack(@Validated({Add.class}) @RequestBody FeedBackVo feedBackVo) {
        FeedBack feedBack = feedBackConvertor.toDo(feedBackVo);
        initEntityCreator(feedBack);
        initEntityModifier(feedBack);
        return feedBackService.addFeedBack(feedBack, feedBackVo.getMediaUrls());
    }

    @ApiOperation("获取意见反馈")
    @GetMapping("/get/{id}/{isDetail}")
    public FeedBackVo findFeedBackById(@PathVariable Long id, @PathVariable Boolean isDetail) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException("无效的id");
        }
        return feedBackService.getFeedBackResultById(id, isDetail);
    }

    @ApiOperation("意见反馈列表")
    @PostMapping("/list")
    public PageResult<FeedBackVo> listFeedBack(@RequestBody FeedBackSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return feedBackService.feedBackList(search);
    }

    @ApiOperation("添加建议回复")
    @PostMapping("/reply/add")
    @Debounce(expire = 5)
    public Long addFeedBackReply(@Validated({Add.class}) @RequestBody FeedBackReplyVo feedBackReplyVo) {
        FeedBackReply feedBackReply = feedBackReplyConvertor.toDo(feedBackReplyVo);
        initEntityCreator(feedBackReply);
        initEntityModifier(feedBackReply);
        return feedBackReplyService.addFeedBackReply(feedBackReply, getUserInContext());
    }

    @ApiOperation("获取建议回复")
    @GetMapping("/reply/get/{id}")
    public FeedBackReplyVo findFeedBackReplyById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException("无效的id");
        }
        return feedBackReplyService.findFeedBackReplyById(id);
    }
}
