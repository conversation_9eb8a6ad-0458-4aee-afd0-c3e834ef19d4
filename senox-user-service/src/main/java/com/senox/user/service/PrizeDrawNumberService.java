package com.senox.user.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.BusinessException;
import com.senox.user.constant.PrizeDrawNumberType;
import com.senox.user.domain.PrizeDrawNumber;
import com.senox.user.mapper.PrizeDrawNumberMapper;
import com.senox.user.vo.PrizeDrawNumberVo;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/4/15 13:34
 */
@Service
public class PrizeDrawNumberService extends ServiceImpl<PrizeDrawNumberMapper, PrizeDrawNumber> {


    /**
     * 增加抽奖次数
     * @param drawNumber
     * @return
     */
    public boolean saveDrawNumber(PrizeDrawNumber drawNumber) {
        PrizeDrawNumber dbDrawNumber = findByPrizeDrawNumber(drawNumber);
        if (dbDrawNumber != null) {
//            throw new BusinessException(PrizeDrawNumberType.fromValue(drawNumber.getType()).getName() + "已经增加过次数了！");
            return false;
        }
        drawNumber.setModifiedTime(LocalDateTime.now());
        return save(drawNumber);
    }

    /**
     * 抽奖次数总数
     * @param drawNumberVo
     * @return
     */
    public int countPrizeDrawNumber(PrizeDrawNumberVo drawNumberVo) {
        return getBaseMapper().countPrizeDrawNumber(drawNumberVo);
    }

    /**
     * 查询相同抽奖次数
     * @param drawNumber
     * @return
     */
    public PrizeDrawNumber findByPrizeDrawNumber(PrizeDrawNumber drawNumber) {
        return getOne(new QueryWrapper<PrizeDrawNumber>().lambda()
                .eq(PrizeDrawNumber::getActivityId, drawNumber.getActivityId())
                .eq(PrizeDrawNumber::getOpenid, drawNumber.getOpenid())
                .eq(PrizeDrawNumber::getType, drawNumber.getType()));
    }

}
