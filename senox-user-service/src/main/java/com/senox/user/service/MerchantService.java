package com.senox.user.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.DesensitizeUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.user.domain.Merchant;
import com.senox.user.mapper.MerchantMapper;
import com.senox.user.vo.MerchantSearchVo;
import com.senox.user.vo.MerchantVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-10-31
 */
@Service
@RequiredArgsConstructor
public class MerchantService extends ServiceImpl<MerchantMapper, Merchant> {

    /**
     * 添加
     * @param merchant 商户
     */
    public Long add(Merchant merchant) {
        if (StringUtils.isBlank(merchant.getRcSerial())) {
            merchant.setRcSerial(StringUtils.EMPTY);
        }
        if (merchant.getDuoduo() == null) {
            merchant.setDuoduo(Boolean.FALSE);
        }
        if (merchant.getBicycleAuth() == null) {
            merchant.setBicycleAuth(Boolean.FALSE);
        }
        if (merchant.getDryAuth() == null) {
            merchant.setDryAuth(Boolean.FALSE);
        }
        if (merchant.getDisabled() == null) {
            merchant.setDisabled(Boolean.FALSE);
        }

        // 判断冷藏客户是否已绑定
        checkMerchantExist(merchant);

        merchant.setCreateTime(LocalDateTime.now());
        merchant.setModifierId(merchant.getCreatorId());
        merchant.setModifierName(merchant.getCreatorName());
        merchant.setModifiedTime(LocalDateTime.now());
        save(merchant);
        return merchant.getId();
    }

    /**
     * 更新
     *
     * @param merchant 商户
     */
    public void update(Merchant merchant) {
        if (!WrapperClassUtils.biggerThanLong(merchant.getId(), 0)) {
            return;
        }
        // 判断商户是否存在
        checkMerchantExist(merchant);

        merchant.setDisabled(null);
        merchant.setCreatorId(null);
        merchant.setCreatorName(null);
        merchant.setCreateTime(null);
        merchant.setModifiedTime(LocalDateTime.now());
        updateById(merchant);
    }

    /**
     * 新增或更新商户
     * @param merchant
     * @return
     */
    public Long saveMerchant(Merchant merchant) {
        if (!WrapperClassUtils.biggerThanLong(merchant.getId(), 0L)) {
            Merchant srcMerchant = !StringUtils.isBlank(merchant.getRcSerial())
                    ? findByRcSerial(merchant.getRcSerial()) : findByName(merchant.getName());

            merchant.setId(srcMerchant == null ? null : srcMerchant.getId());
        }

        if (WrapperClassUtils.biggerThanLong(merchant.getId(), 0L)) {
            update(merchant);
        } else {
            add(merchant);
        }
        return merchant.getId();
    }

    /**
     * 批量新增商户，返回已存在的商户列表
     * @param list
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<String> batchAddMerchant(List<Merchant> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        //判断商户是否添加
        List<String> nameList = list.stream().map(Merchant::getName).distinct().collect(Collectors.toList());
        List<Merchant> existMerchants = listByName(nameList);
        List<String> existNames = existMerchants.stream().map(Merchant::getName).collect(Collectors.toList());

        List<Merchant> addList = list.stream().filter(x -> !existNames.contains(x.getName())).collect(Collectors.toList());

        //保存未添加的商户
        addList.forEach(x ->{
            x.setCreateTime(LocalDateTime.now());
            x.setModifiedTime(LocalDateTime.now());
        });
        saveBatch(addList);
        return existNames;
    }

    /**
     * 删除商户
     * @param merchant
     */
    public void delete(Merchant merchant) {
        if (!WrapperClassUtils.biggerThanLong(merchant.getId(), 0L)) {
            return;
        }

        Merchant srcMerchant = findById(merchant.getId());
        if (srcMerchant == null || BooleanUtils.isTrue(srcMerchant.getDisabled())) {
            return;
        }

        Merchant deleteMerchant = new Merchant();
        deleteMerchant.setId(merchant.getId());
        deleteMerchant.setDisabled(Boolean.TRUE);
        deleteMerchant.setName(srcMerchant.getName().concat("_").concat(merchant.getId().toString()));
        deleteMerchant.setModifierId(merchant.getModifierId());
        deleteMerchant.setModifierName(merchant.getModifierName());
        deleteMerchant.setModifiedTime(LocalDateTime.now());
        updateById(deleteMerchant);
    }

    /**
     * 根据id查询商户
     *
     * @param id id
     * @return 返回查询到的商户
     */
    public Merchant findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        return getById(id);
    }

    /**
     * 根据商户名查找商户
     * @param name
     * @return
     */
    public Merchant findByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }

        LambdaQueryWrapper<Merchant> queryWrapper = new QueryWrapper<Merchant>().lambda()
                .eq(Merchant::getName, name)
                .eq(Merchant::getDisabled, Boolean.FALSE);
        return getOne(queryWrapper);
    }

    /**
     * 根据商户名查找商户
     * @param list
     * @return
     */
    public List<Merchant> listByName(List<String> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<Merchant> queryWrapper = new QueryWrapper<Merchant>().lambda()
                .in(Merchant::getName, list)
                .eq(Merchant::getDisabled, Boolean.FALSE);
        return list(queryWrapper);
    }

    /**
     * 根据冷藏编号查找商户
     * @param rcSerial
     * @return
     */
    public Merchant findByRcSerial(String rcSerial) {
        if (StringUtils.isBlank(rcSerial)) {
            return null;
        }

        LambdaQueryWrapper<Merchant> queryWrapper = new QueryWrapper<Merchant>().lambda()
                .eq(Merchant::getRcSerial, rcSerial)
                .eq(Merchant::getDisabled, Boolean.FALSE);
        return getOne(queryWrapper);
    }

    /**
     * 根据冷藏编号集合查找商户
     * @param rcSerialList
     * @return
     */
    public List<Merchant> findByRcSerialList(List<String> rcSerialList) {
        if (CollectionUtils.isEmpty(rcSerialList)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<Merchant> queryWrapper = new QueryWrapper<Merchant>().lambda()
                .in(Merchant::getRcSerial, rcSerialList)
                .eq(Merchant::getDisabled, Boolean.FALSE);
        return list(queryWrapper);
    }

    /**
     * 根据手机号查找商户
     * @param contact
     * @return
     */
    public List<Merchant> findByContact(String contact) {
        if (StringUtils.isBlank(contact)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<Merchant> queryWrapper = new QueryWrapper<Merchant>().lambda()
                .eq(Merchant::getContact, contact)
                .eq(Merchant::getDisabled, Boolean.FALSE);
        return list(queryWrapper);
    }

    /**
     * 商户统计
     * @param search
     * @return
     */
    public int countMerchant(MerchantSearchVo search) {
        return getBaseMapper().countMerchant(search);
    }

    /**
     * 商户列表
     * @param search
     * @return
     */
    public List<Merchant> listMerchant(MerchantSearchVo search) {
        List<Merchant> resultList = getBaseMapper().listMerchant(search);
        if (BooleanUtils.isTrue(search.getDesensitized()) && !CollectionUtils.isEmpty(resultList)) {
            for (Merchant item : resultList) {
                item.setIdcard(DesensitizeUtils.desensitizeIdcard(item.getIdcard()));
                item.setContact(DesensitizeUtils.desensitizePhone(item.getContact()));
            }
        }
        return resultList;
    }

    /**
     * 商户视图列表
     * @param search
     * @return
     */
    public List<MerchantVo> listMerchantView(MerchantSearchVo search) {
        List<MerchantVo> resultList = getBaseMapper().listMerchantView(search);
        if (BooleanUtils.isTrue(search.getDesensitized()) && !CollectionUtils.isEmpty(resultList)) {
            for (MerchantVo item : resultList) {
                item.setIdcard(DesensitizeUtils.desensitizeIdcard(item.getIdcard()));
                item.setContact(DesensitizeUtils.desensitizePhone(item.getContact()));
            }
        }
        return resultList;
    }



    /**
     * 商户是否已存在
     * @return
     */
    private void checkMerchantExist(Merchant merchant) {
        // 冷藏客户是否已被其他商户关联
        if (StringUtils.isBlank(merchant.getRcSerial())) {
            return;
        }

        // 已关联商户的冷藏客户
        Merchant dbMerchant = findByRcSerial(merchant.getRcSerial());
        if (dbMerchant != null && !Objects.equals(merchant.getId(), dbMerchant.getId())) {
            throw new BusinessException("冷藏客户已存在");
        }
    }

    /**
     * 批量更新收费标准
     *
     * @param ids       id集
     * @param chargesId 收费标准id
     * @param fullData  true:全量;false:增量
     */
    public void updateChargesBatch(Collection<Long> ids, Long chargesId, Boolean fullData) {
        if (CollectionUtils.isEmpty(ids) && !BooleanUtils.isTrue(fullData)) {
            return;
        }
        baseMapper.updateChargesBatch(ids, chargesId, fullData);
    }
}
