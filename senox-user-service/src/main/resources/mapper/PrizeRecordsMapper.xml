<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.user.mapper.PrizeRecordsMapper">

    <select id="countRecords" parameterType="com.senox.user.vo.PrizeRecordsSearchVo" resultType="int">
        select count(1) from u_prize_records
        <where>
            <if test="openid != null and openid != ''">
                and openid = #{openid}
            </if>
            <if test="activityId != null">
                and activity_id = #{activityId}
            </if>
            <if test="prizeId != null">
                and prize_id = #{prizeId}
            </if>
            <if test="isWin != null">
                <choose>
                    <when test="isWin">
                        and is_win = 1
                    </when>
                    <otherwise>
                        and is_win = 0
                    </otherwise>
                </choose>
            </if>
            <if test="isVerify != null">
                <choose>
                    <when test="isVerify">
                        and is_verify = 1
                    </when>
                    <otherwise>
                        and is_verify = 0
                    </otherwise>
                </choose>
            </if>
            <if test="createTimeStart != null">
                and create_time >= #{createTimeStart}
            </if>
            <if test="createTimeEnd != null">
                and create_time <![CDATA[<=]]> #{createTimeEnd}
            </if>
            and is_disabled = 0
        </where>
    </select>

    <select id="listRecords" parameterType="com.senox.user.vo.PrizeRecordsSearchVo" resultType="com.senox.user.vo.PrizeRecordsVo">
        select pr.id
            , pr.uuid
            , pr.activity_id
            , a.name as activity_name
            , pr.prize_id
            , pr.prize_name
            , p.description as prize_description
            , p.media_url as prize_media_url
            , pr.openid
            , pr.is_win
            , pr.is_verify
            , pr.create_time
        from u_prize_records pr
        inner join u_activity a on a.id = pr.activity_id
        left join u_prize p on p.id = pr.prize_id
        <where>
            <if test="openid != null and openid != ''">
                and pr.openid = #{openid}
            </if>
            <if test="activityId != null">
                and pr.activity_id = #{activityId}
            </if>
            <if test="prizeId != null">
                and pr.prize_id = #{prizeId}
            </if>
            <if test="isWin != null">
                <choose>
                    <when test="isWin">
                        and pr.is_win = 1
                    </when>
                    <otherwise>
                        and pr.is_win = 0
                    </otherwise>
                </choose>
            </if>
            <if test="isVerify != null">
                <choose>
                    <when test="isVerify">
                        and pr.is_verify = 1
                    </when>
                    <otherwise>
                        and pr.is_verify = 0
                    </otherwise>
                </choose>
            </if>
            <if test="createTimeStart != null">
                and create_time >= #{createTimeStart}
            </if>
            <if test="createTimeEnd != null">
                and create_time <![CDATA[<=]]> #{createTimeEnd}
            </if>
            and pr.is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY pr.id DESC
            </otherwise>
        </choose>
        <if test="page">
            limit ${offset}, ${pageSize}
        </if>
    </select>

    <select id="findPrizeRecordsVoById" parameterType="java.lang.Long" resultType="com.senox.user.vo.PrizeRecordsVo">
        select pr.id
             , pr.uuid
             , pr.activity_id
             , a.name as activity_name
             , pr.prize_id
             , pr.prize_name
             , p.description as prize_description
             , p.media_url as prize_media_url
             , pr.openid
             , pr.is_win
             , pr.is_verify
             , pr.create_time
        from u_prize_records pr
                 inner join u_activity a on a.id = pr.activity_id
                 left join u_prize p on p.id = pr.prize_id
        where pr.id = #{id}
    </select>

    <select id="findPrizeRecordsVoByUuid" parameterType="java.lang.String" resultType="com.senox.user.vo.PrizeRecordsVo">
        select pr.id
             , pr.uuid
             , pr.activity_id
             , a.name as activity_name
             , pr.prize_id
             , pr.prize_name
             , p.description as prize_description
             , p.media_url as prize_media_url
             , pr.openid
             , pr.is_win
             , pr.is_verify
             , pr.create_time
        from u_prize_records pr
             inner join u_activity a on a.id = pr.activity_id
             left join u_prize p on p.id = pr.prize_id
        where pr.uuid = #{uuid}
    </select>

</mapper>
