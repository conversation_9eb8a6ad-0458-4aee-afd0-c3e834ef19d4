package com.senox.user.service;

import com.senox.user.BaseTest;
import com.senox.user.domain.CosItem;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date 2021/1/27 14:44
 */
public class CosServiceTest extends BaseTest {

    @Autowired
    private CosService cosService;

    @Test
    public void saveCos() {
        CosItem cos1 = mockCos(0L);
        Long result1 = cosService.addCos(cos1);
        Assertions.assertTrue(result1 > 0L);

        CosItem dbCos1 = cosService.findCosById(result1);
        Assertions.assertEquals(cos1, dbCos1);

        CosItem cos2 = new CosItem();
        cos2.setId(result1);
        cos2.setName(randStr(10));
        cos2.setDisplayName(randStr(20));
        cos2.setOrderNo(randInt(20, 50));
        cos2.setModifierId(randLong(1, 200));
        cos2.setModifierName(randStr(20));
        Assertions.assertTrue(cosService.updateCos(cos2));

        CosItem dbCos2 = cosService.findCosById(result1);
        Assertions.assertEquals(cos2.getName(), dbCos2.getName());
        Assertions.assertEquals(cos2.getDisplayName(), dbCos2.getDisplayName());
    }

    private CosItem mockCos(Long parentId) {
        CosItem result = new CosItem();
        result.setName(randStr(10));
        result.setDisplayName(randStr(20));
        result.setUrl(randStr(100));
        result.setParentId(parentId);
        result.setCreatorId(randLong(1, 100));
        result.setCreatorName(randStr(20));
        result.setModifierId(randLong(1, 100));
        result.setModifierName(randStr(20));
        return result;
    }
}
