server:
  port: 8081

spring:
  profiles:
    active: test
  datasource:
    hikari:
      maximum-pool-size: 50
      minimum-idle: 10
      # 连接允许在池中闲置的最长时间
      idle-timeout: 600000
      # maximum number of milliseconds that a client will wait for a connection
      connection-timeout: 30000
      # maximum lifetime in milliseconds of a connection in the pool after it is closed
      max-lifetime: 1800000
  redis:
    letture:
      pool:
        min-idle: 10
        max-idle: 50
        max-active: 500
        max-wait: 1000

senox:
  user:
    key: YqMh6PuUzKuCfQ68nnydKUX4xaQrr3Iu
    iv: '8698238295406919'
    tokenTTL: 7200
  salt:
    length: 6
  customer:
    serial:
      length: 6
      fillChar: 0

ribbon:
  ReadTimeout: 20000
  ConnectTimeout: 20000


