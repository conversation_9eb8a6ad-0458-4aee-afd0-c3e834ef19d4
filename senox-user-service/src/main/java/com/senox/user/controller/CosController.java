package com.senox.user.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.user.domain.CosItem;
import com.senox.user.service.CosService;
import com.senox.user.vo.CosVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/1/27 13:43
 */
@Api(tags = "权限项管理")
@RestController
@RequestMapping("/cos")
public class CosController extends BaseController {

    @Autowired
    private CosService cosService;

    @ApiOperation("添加权限项")
    @PostMapping("/add")
    public Long addCos(@Validated({Add.class}) @RequestBody CosVo cosVo) {
        CosItem cos = cosVo2Entity(cosVo);
        initEntityCreator(cos);
        initEntityModifier(cos);
        return cosService.addCos(cos);
    }

    @ApiOperation("更新权限项")
    @PostMapping("/update")
    public void updateCos(@Validated({Update.class}) @RequestBody CosVo cosVo) {
        if (cosVo.getId() < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        CosItem cos = cosVo2Entity(cosVo);
        initEntityModifier(cos);

        cosService.updateCos(cos);
    }

    @ApiOperation("删除权限项")
    @PostMapping("/delete/{id}")
    public void deleteCos(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        cosService.deleteCos(id);
    }

    @ApiOperation("获取权限项")
    @GetMapping("/get/{id}")
    public CosVo getCos(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        CosItem cos = cosService.findCosById(id);
        return cos == null ? null : cos2Vo(cos);
    }

    @ApiOperation("权限项列表")
    @PostMapping("/list")
    public List<CosVo> listCos() {
        List<CosItem> list = cosService.listCos();
        return list == null ? Collections.emptyList()
                : list.stream().map(this::cos2Vo).collect(Collectors.toList());
    }

    /**
     * 视图对象转实体对象
     * @param cosVo
     * @return
     */
    private CosItem cosVo2Entity(CosVo cosVo) {
        CosItem result = new CosItem();
        BeanUtils.copyProperties(cosVo, result);
        return result;
    }

    /**
     * 实体对象转视图对象
     * @param cos
     * @return
     */
    private CosVo cos2Vo(CosItem cos) {
        CosVo result = new CosVo();
        BeanUtils.copyProperties(cos, result);
        return result;
    }
}
