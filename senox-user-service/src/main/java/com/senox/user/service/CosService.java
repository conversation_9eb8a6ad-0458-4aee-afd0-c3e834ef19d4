package com.senox.user.service;

import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.user.domain.CosItem;
import com.senox.user.mapper.CosItemMapper;
import com.senox.user.mapper.RoleMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2021/1/27 13:58
 */
@Service
public class CosService {

    private static final int DEFAULT_COS_ORDER = 20;

    @Autowired
    private RoleMapper roleMapper;
    @Autowired
    private CosItemMapper cosItemMapper;

    /**
     * 添加权限
     * @param cos
     * @return
     */
    public Long addCos(CosItem cos) {
        // 校验权限名唯一性
        if (checkCosExists(null, cos.getName())) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR, "权限已存在");
        }
        prepareCos(cos);

        int result = cosItemMapper.addCosItem(cos);
        return result < 1 ? 0L : cos.getId();
    }

    /**
     * 更新权限
     * @param cos
     * @return
     */
    public boolean updateCos(CosItem cos) {
        if (!WrapperClassUtils.biggerThanLong(cos.getId(), 0L)) {
            return false;
        }
        // 校验权限名唯一性
        if (!StringUtils.isBlank(cos.getName()) && checkCosExists(cos.getId(), cos.getName())) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR, "权限已存在");
        }
        return cosItemMapper.updateCosItem(cos) > 0;
    }

    /**
     * 删除权限
     * @param cosId
     * @return
     */
    public boolean deleteCos(Long cosId) {
        if (!WrapperClassUtils.biggerThanLong(cosId, 0L)) {
            return false;
        }
        if (checkCosUsed(cosId)) {
            throw new BusinessException(ResultConst.ITEM_OCCUPIED, "权限已被引用，请先删除引用");
        }
        return cosItemMapper.deleteCosItem(cosId) > 0;
    }

    /**
     * 根据id查找权限
     * @param id
     * @return
     */
    public CosItem findCosById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return cosItemMapper.findById(id);
    }

    /**
     * 权限列表
     * @return
     */
    public List<CosItem> listCos() {
        return cosItemMapper.listAll();
    }

    /**
     * 权限是否存在
     * @param id
     * @param name
     * @return
     */
    private boolean checkCosExists(Long id, String name) {
        if (StringUtils.isBlank(name)) {
            return false;
        }
        Long dbId = cosItemMapper.findIdByName(name);
        if (!WrapperClassUtils.biggerThanLong(dbId, 0L)) {
            return false;
        }
        return !WrapperClassUtils.biggerThanLong(id, 0L) || !Objects.equals(id, dbId);
    }

    /**
     * 权限是否被使用
     * @param cosId
     * @return
     */
    private boolean checkCosUsed(Long cosId) {
        return WrapperClassUtils.biggerThanLong(cosId, 0L) && roleMapper.countRoleCos(cosId) > 0;
    }

    /**
     * cos权限初始化
     * @param cos
     */
    private void prepareCos(CosItem cos) {
        if (cos.getParentId() == null) {
            cos.setParentId(0L);
        }
        if (cos.getOrderNo() == null) {
            cos.setOrderNo(DEFAULT_COS_ORDER);
        }
    }


}
