<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.user.mapper.PrizeDrawNumberMapper">


    <select id="countPrizeDrawNumber" parameterType="com.senox.user.vo.PrizeDrawNumberVo" resultType="int">
        select count(1) from u_prize_draw_number
        <where>
            <if test="openid != null and openid != ''">
                and openid = #{openid}
            </if>
            <if test="activityId != null">
                and activity_id = #{activityId}
            </if>
            <if test="type != null">
                and `type` = #{type}
            </if>
        </where>
    </select>

</mapper>
