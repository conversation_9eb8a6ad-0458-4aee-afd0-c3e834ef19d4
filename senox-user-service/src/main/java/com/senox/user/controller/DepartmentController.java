package com.senox.user.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.user.domain.Department;
import com.senox.user.service.DepartmentService;
import com.senox.user.vo.DepartmentNode;
import com.senox.user.vo.DepartmentVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/9/15 11:54
 */
@Api(tags = "部门管理")
@RestController
@RequestMapping("/department")
public class DepartmentController extends BaseController {

    @Autowired
    private DepartmentService departmentService;

    @ApiOperation("添加部门")
    @PostMapping("/add")
    public Long addDepartment(@Validated({Add.class}) @RequestBody DepartmentVo departmentVo) {
        Department department = departmentVo2Entity(departmentVo);
        initEntityCreator(department);
        initEntityModifier(department);
        return departmentService.addDepartment(department);
    }

    @ApiOperation("更新部门")
    @PostMapping("/update")
    public void updateDepartment(@Validated({Update.class}) @RequestBody DepartmentVo departmentVo) {
        if (departmentVo.getId() < 1L) {
            throw new InvalidParameterException("无效的id");
        }

        Department department = departmentVo2Entity(departmentVo);
        initEntityModifier(department);
        departmentService.updateDepartment(department);
    }

    @ApiOperation("根据id获取部门")
    @GetMapping("/get/{id}")
    public DepartmentVo getDepartment(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        Department department = departmentService.findById(id);
        return department == null ? null : department2Vo(department);
    }

    @ApiOperation("子部门列表")
    @GetMapping("/list/{parentId}")
    public List<DepartmentVo> listDepartment(@PathVariable Long parentId) {
        if (parentId < 0L) {
            throw new InvalidParameterException("无效的父id");
        }
        List<Department> resultList = departmentService.listDepartments(parentId);
        return resultList.stream().map(this::department2Vo).collect(Collectors.toList());
    }

    @ApiOperation("部门列表")
    @PostMapping("/listAll")
    public List<DepartmentVo> listDepartment() {
        List<Department> list = departmentService.listDepartments();
        return list.stream().map(this::department2Vo).collect(Collectors.toList());
    }

    @ApiOperation("部门树")
    @PostMapping("/treeList")
    public List<DepartmentNode> listDepartmentTree() {
        return departmentService.listDepartmentNode();
    }

    /**
     * 部门视图对象转实体对象
     * @param departmentVo
     * @return
     */
    private Department departmentVo2Entity(DepartmentVo departmentVo) {
        Department result = new Department();
        BeanUtils.copyProperties(departmentVo, result);
        return result;
    }

    /**
     * 部门实体对象转视图对象
     * @param department
     * @return
     */
    private DepartmentVo department2Vo(Department department) {
        DepartmentVo result = new DepartmentVo();
        BeanUtils.copyProperties(department, result);
        return result;
    }
}
