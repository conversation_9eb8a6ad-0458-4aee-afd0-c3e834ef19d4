package com.senox.user.service;

import com.senox.common.vo.PageResult;
import com.senox.user.BaseTest;
import com.senox.user.constant.Gender;
import com.senox.user.domain.AdminUser;
import com.senox.user.vo.AdminUserSearchVo;
import com.senox.user.vo.AdminUserVo;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/12/29 14:51
 */
public class AdminUserServiceTest extends BaseTest {

    private static final Logger logger = LoggerFactory.getLogger(AdminUserServiceTest.class);

    @Autowired
    private AdminUserService adminUserService;

    @Test
    public void testAddAndUpdate() {
        AdminUser adminUser1 = mockAdminUser();
        String password1 = adminUser1.getPassword();
        List<Long> roles1 = mockLongList(randLong(1, 50), 5);
        Long result1 = adminUserService.addAdminUser(adminUser1, roles1, mockLongList(randLong(1, 50), 3));
        Assertions.assertTrue(result1 > 0);

        AdminUserVo adminUser2 = adminUserService.findById(result1);
        Assertions.assertEquals(adminUser1.getUsername(), adminUser2.getUsername());
        Assertions.assertEquals(adminUser1.getEmail(), adminUser2.getEmail());
        Assertions.assertEquals(adminUser1.getGender(), adminUser2.getGender());
        List<Long> dbRole1 = adminUserService.listAdminUserRole(result1);
        Assertions.assertEquals(roles1.size(), dbRole1.size());
        for (Long item : roles1) {
            Assertions.assertTrue(dbRole1.contains(item));
        }

        AdminUser adminUser3 = new AdminUser();
        adminUser3.setId(result1);
        adminUser3.setEmail(randStr(20));
        adminUser3.setTelephone(randStr(11));
        adminUser3.setModifierId(randLong(1, 100));
        adminUser3.setModifierName(randStr(10));
        List<Long> roles2 = mockLongList(randLong(10, 60), 5);
        Assertions.assertTrue(adminUserService.updateAdminUser(adminUser3, roles2, mockLongList(randLong(1, 50), 3)));
        List<Long> dbRole2 = adminUserService.listAdminUserRole(result1);
        Assertions.assertEquals(roles2.size(), dbRole2.size());
        for (Long item : roles2) {
            Assertions.assertTrue(dbRole2.contains(item));
        }

        AdminUserVo adminUser4 = adminUserService.findById(result1);
        Assertions.assertEquals(adminUser3.getEmail(), adminUser4.getEmail());
        Assertions.assertEquals(adminUser3.getTelephone(), adminUser4.getTelephone());

        AdminUser adminUser5 = adminUserService.findByUsernameAndPassword(adminUser1.getUsername(), password1);
        Assertions.assertNotNull(adminUser5);
        AdminUser adminUser6 = adminUserService.findByUsernameAndPassword(adminUser1.getUsername(), randStr(10));
        Assertions.assertNull(adminUser6);

        String password2 = randStr(10);
        Assertions.assertTrue(adminUserService.updateAdminUserPassword(result1, password2, randLong(1, 10), randStr(10)));
        Assertions.assertNotNull(adminUserService.findByUsernameAndPassword(adminUser1.getUsername(), password2));
    }

    @Test
    public void testListPage() {
        List<AdminUser> adminUserList = new ArrayList<>(30);
        for (int i = 0; i < 30; i++) {
            AdminUser user = mockAdminUser();
            adminUserService.addAdminUser(user, mockLongList(randLong(1, 50), 5), mockLongList(randLong(1, 50), 3));
            adminUserList.add(user);
        }

        AdminUserSearchVo searchVo = new AdminUserSearchVo();
        searchVo.setPageNo(2);
        searchVo.setPageSize(5);

        PageResult<AdminUserVo> pageResult = adminUserService.listAdminUserPage(searchVo);
        logger.info("pageResult: {}", pageResult);
        Assertions.assertEquals(searchVo.getPageSize(), pageResult.getDataList().size());

        searchVo.setOrderStr("id desc");
        pageResult = adminUserService.listAdminUserPage(searchVo);
        logger.info("pageResult: {}", pageResult);
        Assertions.assertEquals(searchVo.getPageSize(), pageResult.getDataList().size());

        searchVo.setUsername(adminUserList.get(randInt(0, adminUserList.size() - 1)).getUsername());
        searchVo.setPageNo(1);
        pageResult = adminUserService.listAdminUserPage(searchVo);
        logger.info("pageResult: {}", pageResult);
        Assertions.assertEquals(1, pageResult.getDataList().size());
    }


    private AdminUser mockAdminUser() {
        AdminUser result = new AdminUser();
        result.setUsername(randStr(10));
        result.setRealName(randStr(10));
        result.setPassword(randStr(12));
        result.setGender(Gender.fromValue(randInt(1, Gender.values().length)).getValue());
        result.setEmail(randStr(10));
        result.setTelephone(randStr(11));
        result.setAvatar(randStr(50));
        result.setCreatorId(randLong(1, 100));
        result.setCreatorName(randStr(20));
        result.setModifierId(randLong(1, 100));
        result.setModifierName(randStr(20));
        return result;
    }

    private List<Long> mockLongList(Long baseIndex, int size) {
        List<Long> result = new ArrayList<>(size);
        for (int i = 0; i < size; i++) {
            result.add(baseIndex + i);
        }
        return result;
    }
}
