package com.senox.user.controller;

import com.senox.user.BaseControllerTest;
import com.senox.user.constant.AreaCategory;
import com.senox.user.vo.AreaVo;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/12/30 15:55
 */
public class AreaControllerTest extends BaseControllerTest {

    private static final Logger logger = LoggerFactory.getLogger(AreaControllerTest.class);

    @Test
    public void testAddAndUpdate() {
        AreaVo areaVo1 = mockAreaVo(0L);
        HttpEntity<AreaVo> entity1 = new HttpEntity<>(areaVo1);
        ResponseEntity<Long> responseEntity1 = restTemplate.exchange("/area/add", HttpMethod.POST,
                entity1, new ParameterizedTypeReference<Long>() {
                });
        Long result1 = responseEntity1.getBody();
        Assertions.assertEquals(HttpStatus.OK, responseEntity1.getStatusCode());
        logger.info("result1: {}", result1);
        Assertions.assertTrue(result1 > 0);

        ResponseEntity<AreaVo> responseEntity2 = restTemplate.exchange("/area/get/" + result1,
                HttpMethod.GET, null, new ParameterizedTypeReference<AreaVo>() {
                });
        AreaVo result2 = responseEntity2.getBody();
        logger.info("result2: {}", result2);
        Assertions.assertEquals(areaVo1.getName(), result2.getName());
        Assertions.assertEquals(areaVo1.getBriefName(), result2.getBriefName());
        Assertions.assertEquals(areaVo1.getParentId(), result2.getParentId());
        Assertions.assertEquals(areaVo1.getCategory(), result2.getCategory());

        AreaVo areaVo3 = new AreaVo();
        areaVo3.setId(result1);
        areaVo3.setName(randStr(10));
        areaVo3.setBriefName(randStr(5));
        HttpEntity<AreaVo> entity3 = new HttpEntity<>(areaVo3);
        ResponseEntity<Void> responseEntity3 = restTemplate.exchange("/area/update", HttpMethod.POST,
                entity3, new ParameterizedTypeReference<Void>(){});
        logger.info("result3: {}", responseEntity3);

        ResponseEntity<AreaVo> responseEntity4 = restTemplate.exchange("/area/get/" + result1,
                HttpMethod.GET, null, new ParameterizedTypeReference<AreaVo>() {
                });
        AreaVo result4 = responseEntity4.getBody();
        logger.info("result4: {}", result4);
        Assertions.assertEquals(areaVo3.getName(), result4.getName());
        Assertions.assertEquals(areaVo3.getBriefName(), result4.getBriefName());
        Assertions.assertEquals(areaVo1.getParentId(), result4.getParentId());
        Assertions.assertEquals(areaVo1.getCategory(), result4.getCategory());
    }

    @Test
    public void listArea() {
        int randIndex = randInt(1, 16);
        Long randIndexId = 0L;
        for (int i = 1; i <= 16; i++) {
            AreaVo areaVo1 = mockAreaVo(0L);
            HttpEntity<AreaVo> entity1 = new HttpEntity<>(areaVo1);
            ResponseEntity<Long> responseEntity1 = restTemplate.exchange("/area/add", HttpMethod.POST,
                    entity1, new ParameterizedTypeReference<Long>() {
                    });
            Long result1 = responseEntity1.getBody();

            if (randIndex == i) {
                randIndexId = result1;
            }

            for (int j = 0; j < i; j++) {
                AreaVo areaVo2 = mockAreaVo(result1);
                HttpEntity<AreaVo> entity2 = new HttpEntity<>(areaVo2);
                ResponseEntity<Long> responseEntity2 = restTemplate.exchange("/area/add", HttpMethod.POST,
                        entity2, new ParameterizedTypeReference<Long>() {
                        });
            }
        }

        ResponseEntity<List<AreaVo>> responseEntity3 = restTemplate.exchange("/area/province/list", HttpMethod.POST,
                null, new ParameterizedTypeReference<List<AreaVo>>() {
                });
        List<AreaVo> result3 = responseEntity3.getBody();
        logger.info("result3: {}", responseEntity3);
        Assertions.assertTrue(result3.size() >= 16);

        final Long parentId = randIndexId;
        ResponseEntity<List<AreaVo>> responseEntity4 = restTemplate.exchange("/area/city/list/" + parentId, HttpMethod.POST,
                null, new ParameterizedTypeReference<List<AreaVo>>() {
                });
        List<AreaVo> result4 = responseEntity4.getBody();
        logger.info("result4: {}", result4);
        Assertions.assertEquals(randIndex, result4.size());
        Assertions.assertTrue(result4.stream().allMatch(x -> x.getParentId().equals(parentId)));
    }


    private AreaVo mockAreaVo(long parentId) {
        AreaVo result = new AreaVo();
        result.setSerialNo(randNumStr(20));
        result.setName(randStr(10));
        result.setBriefName(randStr(5));
        result.setParentId(parentId);
        result.setCategory(parentId == 0 ? AreaCategory.PROVINCE.getValue() : AreaCategory.CITY.getValue());
        return result;
    }

}
