package com.senox.user.authcredentials.handler;


import com.senox.user.authcredentials.dto.AuthCredentialsDto;

/**
 * <AUTHOR>
 * @date 2024-1-2
 */
public interface AuthCredentialsHandler {


    /**
     * 校验和解密
     *
     * @param authCredentials       认证凭证
     * @param content               内容
     * @param checksumHashIteration 校验和hash次数
     * @param contentHashIteration  内容hash次数
     * @return 返回解密后的内容
     */
    String verifyAndDecrypt(AuthCredentialsDto authCredentials, String content, Integer checksumHashIteration, Integer contentHashIteration);

    /**
     * 校验和
     *
     * @param authCredentials       认证凭证
     * @param appSecret             私钥
     * @param checksumHashIteration 校验和hash次数
     */
    default void checksum(AuthCredentialsDto authCredentials, String appSecret, Integer checksumHashIteration) {
    }

    /**
     * 校验sign
     *
     * @param context 内容
     * @param sign    sign
     */
    default void checkSign(String context, String sign) {
    }
}
