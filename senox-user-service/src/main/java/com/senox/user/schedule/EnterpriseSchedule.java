package com.senox.user.schedule;

import com.senox.common.utils.JsonUtils;
import com.senox.user.service.EnterpriseRealtyService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/9 15:56
 */
@Component
@RequiredArgsConstructor
public class EnterpriseSchedule {

    private final EnterpriseRealtyService enterpriseRealtyService;

    @XxlJob("fixUniqueEnterpriseRealtyAlias")
    public void fixUniqueEnterpriseRealtyAlias() {
        XxlJobHelper.log("经营户合租物业修复开始...");
        long execTime = System.currentTimeMillis();

        // list
        List<String> realtySerials = enterpriseRealtyService.listUniqueEnterpriseRealtyWithAlias();
        XxlJobHelper.log("待处理数据： {}。", JsonUtils.object2Json(realtySerials));

        // reset
        if (!CollectionUtils.isEmpty(realtySerials)) {
            enterpriseRealtyService.resetAliasByRealtySerial(realtySerials);
        }


        XxlJobHelper.log("经营户合租物业修复开完成，耗时：{}。", System.currentTimeMillis() - execTime);
    }
}
