package com.senox.user.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.senox.common.constant.ResultConst;
import com.senox.common.constant.SystemParam;
import com.senox.common.domain.SystemSetting;
import com.senox.common.exception.BusinessException;
import com.senox.common.service.SystemSettingService;
import com.senox.common.utils.*;
import com.senox.common.vo.DataSepDto;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminUserDto;
import com.senox.user.config.AppConfig;
import com.senox.user.domain.AdminUser;
import com.senox.user.domain.AdminUserRole;
import com.senox.user.domain.CosItem;
import com.senox.user.mapper.AdminUserMapper;
import com.senox.user.mapper.CosItemMapper;
import com.senox.user.vo.AdminUserChangePwdVo;
import com.senox.user.vo.AdminUserSearchVo;
import com.senox.user.vo.AdminUserVo;
import com.senox.user.vo.TollManSerialVo;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.senox.user.constant.UserConst.*;

/**
 * <AUTHOR>
 * @Date 2020/12/28 17:23
 */
@Service
@RequiredArgsConstructor
public class AdminUserService {

    private static final Logger logger = LoggerFactory.getLogger(AdminUserService.class);

    private final AppConfig appConfig;
    private final AdminUserMapper adminUserMapper;
    private final CosItemMapper cosItemMapper;
    private final SystemSettingService settingService;

    /**
     * 添加用户
     * @param adminUser
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addAdminUser(AdminUser adminUser, List<Long> roles, List<Long> departments) {
        if (isUserExists(null, adminUser.getUsername())) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR, "用户已存在");
        }
        // 密码处理
        initSaltAndPassword(adminUser);
        if (adminUser.getDepartmentId() == null) {
            adminUser.setDepartmentId(0L);
        }
        if (adminUser.getTollMan() == null) {
            adminUser.setTollMan(Boolean.FALSE);
        }
        if (adminUser.getMaintainManType() == null) {
            adminUser.setMaintainManType(0);
        }
        if (adminUser.getLoginable() == null) {
            adminUser.setLoginable(Boolean.TRUE);
        }
        adminUserMapper.addAdminUser(adminUser);

        // 保存用户部门
        if (!CollectionUtils.isEmpty(departments)) {
            adminUserMapper.batchAddUserDepartment(adminUser.getId(), departments);
        }

        // 保存用户权限
        if (!CollectionUtils.isEmpty(roles)) {
            List<AdminUserRole> userRoleList = roles.stream()
                    .map(x -> this.newUserRole(adminUser, x))
                    .collect(Collectors.toList());
            adminUserMapper.batchAddAdminUserRole(userRoleList);

            RedisUtils.del(String.format(Cache.KEY_USER_ROLES, adminUser.getId()));
        }
        return adminUser.getId();
    }

    /**
     * 更新除密码，盐外信息
     * @param adminUser
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAdminUser(AdminUser adminUser, List<Long> roles, List<Long> departments) {
        if (adminUser.getId() == null || adminUser.getId() < 1L) {
            return false;
        }
        if (!StringUtils.isEmpty(adminUser.getUsername())
                && isUserExists(adminUser.getId(), adminUser.getUsername())) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR, "用户已存在");
        }
        if (!StringUtils.isBlank(adminUser.getPassword())) {
            initSaltAndPassword(adminUser);
        } else {
            adminUser.setPassword(null);
            adminUser.setSalt(null);
        }
        boolean result = adminUserMapper.updateAdminUser(adminUser) > 0;

        // 保存用户部门
        List<Long> originUserDepartments = adminUserMapper.listUserDepartmentId(adminUser.getId());
        DataSepDto<Long> sepDepartment = sepUserLongData(originUserDepartments, departments);
        if (!CollectionUtils.isEmpty(sepDepartment.getRemoveList())) {
            adminUserMapper.batchDelUserDepartment(adminUser.getId(), sepDepartment.getRemoveList());
        }
        if (!CollectionUtils.isEmpty(sepDepartment.getAddList())) {
            adminUserMapper.batchAddUserDepartment(adminUser.getId(), sepDepartment.getAddList());
        }

        // 保存用户角色
        List<Long> originUserRoles = adminUserMapper.listAdminUserRole(adminUser.getId());
        DataSepDto<Long> sepRole = sepUserLongData(originUserRoles, roles);
        if (!CollectionUtils.isEmpty(sepRole.getRemoveList())) {
            adminUserMapper.batchDelAdminUserRole(adminUser.getId(), sepRole.getRemoveList());
        }
        if (!CollectionUtils.isEmpty(sepRole.getAddList())) {
            List<AdminUserRole> addUserRoles = sepRole.getAddList().stream()
                    .map(x -> this.newUserRole(adminUser, x))
                    .collect(Collectors.toList());
            adminUserMapper.batchAddAdminUserRole(addUserRoles);
        }

        // 删除缓存的角色
        RedisUtils.del(String.format(Cache.KEY_USER_ROLES, adminUser.getId()));
        return result;
    }

    /**
     * 修改
     * @param tollManSerial
     * @param modifier
     */
    public void modifyAdminTollInfo(TollManSerialVo tollManSerial, AdminUserDto modifier) {
        AdminUserVo user = findById(modifier.getUserId());
        if (!BooleanUtils.isTrue(user.getTollMan())) {
            throw new BusinessException("非收费员，不允许设置收据编号");
        }

        AdminUser modUser = new AdminUser();
        modUser.setId(modifier.getUserId());
        modUser.setBillSerial(tollManSerial.getBillSerial());
        modUser.setPayDeviceSn(tollManSerial.getPayDeviceSn());
        modUser.setModifierId(modifier.getUserId());
        modUser.setModifierName(modifier.getUsername());
        adminUserMapper.updateAdminUser(modUser);
    }

    /**
     * 修改密码
     * @param changePwd
     * @return
     */
    public boolean changePassword(AdminUserChangePwdVo changePwd) {
        AdminUser user = findByUsernameAndPassword(changePwd.getUsername(), changePwd.getOldPassword());
        if (user == null) {
            throw new BusinessException("用户原密码错误");
        }

        return updateAdminUserPassword(user.getId(), changePwd.getNewPassword(), changePwd.getModifierId(), changePwd.getModifierName());
    }

    /**
     * 更新用户密码
     * @param id
     * @param newPassword
     * @return
     */
    public boolean updateAdminUserPassword(Long id, String newPassword, Long modifierId, String modifierName) {
        if (id == null || id < 1L) {
            return false;
        }
        AdminUser user = new AdminUser();
        user.setId(id);
        user.setPassword(newPassword);
        user.setModifierId(modifierId);
        user.setModifierName(modifierName);
        initSaltAndPassword(user);
        return adminUserMapper.updateAdminUser(user) > 0L;
    }

    /**
     * 获取票据编号
     * @param id
     * @return
     */
    public TollManSerialVo findAdminTollInfo(Long id) {
        logger.info("Find user bil serial {}", id);
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return adminUserMapper.findBillSerial(id);
    }

    /**
     * 根据id查找用户
     * @param id
     * @return
     */
    public AdminUserVo findById(Long id) {
        return id == null || id < 1L ? null : adminUserMapper.findById(id);
    }

    /**
     * 根据用户名查找用户
     * @param username
     * @return
     */
    public AdminUser findByUsername(String username) {
        return StringUtils.isEmpty(username) ? null : adminUserMapper.findByUsername(username);
    }

    /**
     * 根据真实姓名查找用户
     * @param realName
     * @return
     */
    public AdminUser findByRealName(String realName) {
        return StringUtils.isEmpty(realName) ? null : adminUserMapper.findByRealName(realName);
    }

    /**
     * 根据手机号查找用户
     * @param telephone
     * @return
     */
    public AdminUser findByTelephone(String telephone) {
        return StringUtils.isEmpty(telephone) ? null : adminUserMapper.findByTelephone(telephone);
    }

    /**
     * 根据用户名密码查找用户
     * @param username
     * @param password
     * @return
     */
    public AdminUser findByUsernameAndPassword(String username, String password) {
        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(password)) {
            return null;
        }

        AdminUser adminUser = adminUserMapper.findByUsername(username);
        if (adminUser == null) {
            return null;
        }
        String passwordHashed = Md5Utils.encode(password, adminUser.getSalt(), appConfig.getHashIterations());
        if (!Objects.equals(adminUser.getPassword(), passwordHashed)) {
            return null;
        }
        adminUser.setPassword(null);
        adminUser.setSalt(null);
        return adminUser;
    }

    /**
     * 获取用户token
     * @param userId
     * @param username
     * @return
     */
    public String getUserToken(Long userId, String username) {
        if (userId == null || userId < 1L || StringUtils.isEmpty(username)) {
            return StringUtils.EMPTY;
        }
        String rawToken = String.format(TOKEN_AUTH, userId, System.currentTimeMillis(), username);
        return AesUtils.encrypt(rawToken, appConfig.getTokenKey(), appConfig.getTokenIv(), appConfig.getHashIterations());
    }

    /**
     * 从token获取用户
     * @param token
     * @return5
     */
    public AdminUserDto getUserFromToken(String token) {
        if (isLogoutToken(token)) {
            return null;
        }
        if (StringUtils.isEmpty(token)) {
            return null;
        }

        // load token user from cache
        String userCachedKey = String.format(Cache.KEY_USER, token);

        // get from cache
        AdminUserDto result = RedisUtils.get(userCachedKey);
        if (result != null && result.isValid()) {
            logger.info("Token validated from redis {}", token);
            // 更新缓存
            RedisUtils.set(userCachedKey, result, Cache.TTL_2H);
            return result;
        }

        // token decrypt
        String rawStr = AesUtils.decrypt(token, appConfig.getTokenKey(), appConfig.getTokenIv(), appConfig.getHashIterations());
        result = getUserFromRawToken(rawStr);
        if (result == null) {
            logger.info("无效的token {}", token);
        } else {
            result.setToken(token);
            // 更新缓存
            RedisUtils.set(userCachedKey, result, Cache.TTL_2H);
        }
        return result;
    }

    /**
     * 从微信token钟获取用户
     * @param appId
     * @param token
     * @return
     */
    public AdminUserDto getUserFromWechatToken(String appId, String token) {
        if (StringUtils.isEmpty(token)) {
            return null;
        }
        appId = StringUtils.trimToEmpty(appId);

        // load wechat token user from cache
        String cacheKey = String.format(Cache.KEY_ADMIN_WECHAT, appId, token);
        // get from cache
        AdminUserDto result = RedisUtils.get(cacheKey);
        if (result != null && result.isValid()) {
            logger.info("Wechat token validated from redis {}", token);
            // 更新缓存
            RedisUtils.set(cacheKey, result, Cache.TTL_2H);
            return result;
        }

        AdminUser user = adminUserMapper.findByOpenid(appId, token);
        if (user == null) {
            logger.info("无效的wechat token {}", token);
        } else {
            result = new AdminUserDto();
            result.setUserId(user.getId());
            result.setUsername(user.getUsername());
            result.setRealName(user.getRealName());
            result.setMpAppId(appId);
            result.setWechatToken(token);
            // 更新缓存
            RedisUtils.set(cacheKey, result, Cache.TTL_2H);
        }
        return result;
    }

    /**
     * 退出登录
     * @param token
     */
    public void removeToken(String token) {
        String userCacheKey = String.format(Cache.KEY_USER, token);
        RedisUtils.del(userCacheKey);

        // 失效token记录
        RedisUtils.set(String.format(Cache.KEY_INVALID_TOKEN, token), 1, Cache.TTL_2H);
    }

    /**
     * 从feignToken获取用户
     * @param feignToken
     * @return5
     */
    public AdminUserDto getUserFromFeignToken(String feignToken) {
        if (isInvalidFeignToken(feignToken)) {
            return null;
        }
        if (StringUtils.isEmpty(feignToken)) {
            return null;
        }

        // load token user from cache
        String userCachedKey = String.format(Cache.KEY_FEIGN_USER, feignToken);

        // get from cache
        AdminUserDto result = RedisUtils.get(userCachedKey);
        if (result != null && result.isValid()) {
            logger.info("Feign Token validated from redis {}", feignToken);
            // 更新缓存
            RedisUtils.set(userCachedKey, result, Cache.TTL_10M);
            return result;
        }
        SystemSetting key = settingService.findByParam(SystemParam.FEIGN_KEY);
        SystemSetting iv = settingService.findByParam(SystemParam.FEIGN_IV);
        // token decrypt
        String rawStr = AesUtils.decrypt(feignToken, key.getValue(), iv.getValue(), 2);
        result = getUserFromRawFeignToken(rawStr);
        if (result == null) {
            logger.info("无效的feignToken {}", feignToken);
        } else {
            result.setFeignToken(feignToken);
            // 更新缓存
            RedisUtils.set(userCachedKey, result, Cache.TTL_10M);
        }
        return result;
    }

    /**
     * 从原始rawFeignToken获取用户
     * @param rawFeignToken
     * @return
     */
    private AdminUserDto getUserFromRawFeignToken(String rawFeignToken) {
        if (StringUtils.isEmpty(rawFeignToken)) {
            return null;
        }
        LocalDateTime dateTime = DateUtils.parseDateTime(rawFeignToken.substring(5, 19), "yyyyMMddHHmmss");
        SystemSetting feignTtl = settingService.findByParam(SystemParam.FEIGN_TTL);
        int ttl =  Integer.parseInt(feignTtl.getValue());
        LocalDateTime now = LocalDateTime.now();
        if (Duration.between(dateTime, now).getSeconds() > ttl) {
            return null;
        }
        return getAdminUserDto(1L, null);
    }

    /**
     * 失效feignToken
     * @param feignToken
     */
    public void removeFeignToken(String feignToken) {
        String userCacheKey = String.format(Cache.KEY_FEIGN_USER, feignToken);
        RedisUtils.del(userCacheKey);

        // 失效feignToken记录
        RedisUtils.set(String.format(Cache.KEY_INVALID_FEIGN_TOKEN, feignToken), 1, Cache.TTL_10M);
    }

    /**
     * 管理原用户列表
     * @param searchVo
     * @return
     */
    public PageResult<AdminUserVo> listAdminUserPage(AdminUserSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }

        // 总记录数
        int totalSize = adminUserMapper.countAdminUser(searchVo);
        searchVo.prepare();

        if (totalSize <= searchVo.getOffset()) {
            return PageResult.emptyPage();
        }

        // list
        List<AdminUserVo> resultList = adminUserMapper.listAdminUser(searchVo);
        return PageUtils.resultPage(searchVo, totalSize, resultList);
    }

    /**
     * 用户部门列表
     * @param userId
     * @return
     */
    public List<Long> listAdminUserDepartment(Long userId) {
        if (!WrapperClassUtils.biggerThanLong(userId, 0L)) {
            return Collections.emptyList();
        }
        return adminUserMapper.listUserDepartmentId(userId);
    }

    /**
     * 用户角色列表
     * @param userId
     * @return
     */
    public List<Long> listAdminUserRole(Long userId) {
        if (!WrapperClassUtils.biggerThanLong(userId, 0L)) {
            return Collections.emptyList();
        }

        List<Long> resultList = null;

        // load from cache
        String cacheKey = String.format(Cache.KEY_USER_ROLES, userId);
        String cacheVal = RedisUtils.get(cacheKey);
        if (!StringUtils.isEmpty(cacheVal)) {
            resultList = JsonUtils.json2GenericObject(cacheVal, new TypeReference<List<Long>>() {});
        }

        // load from db
        if (CollectionUtils.isEmpty(resultList)) {
            resultList = adminUserMapper.listAdminUserRole(userId);

            if (!CollectionUtils.isEmpty(resultList)) {
                RedisUtils.set(cacheKey, JsonUtils.object2Json(resultList));
            }
        }
        return resultList;
    }

    /**
     * 用户角色编码列表
     * @param userId
     * @return
     */
    public List<String> listAdminUserRoleCode(Long userId) {
        return adminUserMapper.listAdminUserRoleCode(userId);
    }

    /**
     * 查找用户权限
     * @param userId
     * @return
     */
    public List<String> listAdminUserCos(Long userId) {
        return listCosByRoles(listAdminUserRole(userId));
    }

    /**
     * 角色用户列表
     *
     * @param roleId
     * @return
     */
    public List<AdminUserVo> listRoleAdminUser(Long roleId) {
        return adminUserMapper.listRoleAdminUser(roleId);
    }

    /**
     * 从原始token获取用户
     * @param rawToken
     * @return
     */
    private AdminUserDto getUserFromRawToken(String rawToken) {
        if (StringUtils.isEmpty(rawToken)) {
            return null;
        }
        String[] tokenArr = rawToken.split(TOKEN_AUTH_SPLIT_STR);
        if (tokenArr.length != 3) {
            return null;
        }

        Long userId = Long.valueOf(tokenArr[0]);
        long time = Long.parseLong(tokenArr[1]);
        String username = String.valueOf(tokenArr[2]);

        long tokenTime = (System.currentTimeMillis() - time) / 1000L;
        if (tokenTime < 1L || tokenTime > appConfig.getTokenTTL()) {
            return null;
        }

        return getAdminUserDto(userId, username);
    }

    private AdminUserDto getAdminUserDto(Long userId, String username) {
        AdminUserVo user = adminUserMapper.findById(userId);
        if (user == null) {
            return null;
        }
        AdminUserDto result = new AdminUserDto();
        result.setUserId(userId);
        result.setUsername(username == null ? user.getUsername() : username);
        result.setRealName(user.getRealName());
        result.setDepartmentId(user.getDepartmentId());
        result.setDepartmentName(user.getDepartmentName());
        result.setTollMan(BooleanUtils.isTrue(user.getTollMan()));
        result.setCosList(listAdminUserCos(userId));
        return result;
    }


    /**
     * 初始化话盐和密码
     * @param adminUser
     */
    private void initSaltAndPassword(AdminUser adminUser) {
        if (StringUtils.isEmpty(adminUser.getPassword())) {
            return;
        }

        adminUser.setSalt(StringUtils.randStr(appConfig.getSaltLength()));
        adminUser.setPassword(Md5Utils.encode(adminUser.getPassword(), adminUser.getSalt(), appConfig.getHashIterations()));
    }


    /**
     * 用户是否存在
     * @param id
     * @param username
     * @return
     */
    private boolean isUserExists(Long id, String username) {
        if (!StringUtils.isEmpty(username)) {
            return false;
        }

        AdminUser adminUser = findByUsername(username);
        if (adminUser == null) {
            return false;
        }
        return !WrapperClassUtils.biggerThanLong(id, 0L) || !Objects.equals(id, adminUser.getId());
    }

    /**
     * 比较角色权限，得出待修改数据
     * @param src
     * @param target
     * @return
     */
    private DataSepDto<Long> sepUserLongData(List<Long> src, List<Long> target) {
        DataSepDto<Long> result = new DataSepDto<>();
        if (CollectionUtils.isEmpty(target)) {
            // 目标记录为空，删掉所有原始记录
            result.setRemoveList(src);

        } else if (CollectionUtils.isEmpty(src)) {
            // 原始记录为空，新增所有目标记录
            result.setAddList(target);

        } else {
            // 新增所有在原始记录中没有的目标记录
            List<Long> addList = target.stream().filter(x -> !src.contains(x)).collect(Collectors.toList());
            // 移除所有在目标记录中没有的原始记录
            List<Long> removeList = src.stream().filter(x -> !target.contains(x)).collect(Collectors.toList());
            result.setAddList(addList);
            result.setRemoveList(removeList);
        }
        return result;
    }

    /**
     * 新建角色权限
     * @param adminUser
     * @param roleId
     * @return
     */
    private AdminUserRole newUserRole(AdminUser adminUser, Long roleId) {
        AdminUserRole result = new AdminUserRole();
        result.setUserId(adminUser.getId());
        result.setRoleId(roleId);
        result.setCreatorId(adminUser.getModifierId());
        result.setCreatorName(adminUser.getModifierName());
        result.setModifierId(adminUser.getModifierId());
        result.setModifierName(adminUser.getModifierName());
        return result;
    }

    /**
     * 根据角色查找权限
     * @param roles
     * @return
     */
    private List<String> listCosByRoles(List<Long> roles) {
        if (CollectionUtils.isEmpty(roles)) {
            return Collections.emptyList();
        }

        List<CosItem> cosList = cosItemMapper.listCosByRole(roles);
        return cosList == null ? Collections.emptyList()
                : cosList.stream().map(CosItem::getName).distinct().collect(Collectors.toList());
    }

    private boolean isLogoutToken(String token) {
        return !StringUtils.isBlank(token) && !Objects.isNull(RedisUtils.get(String.format(Cache.KEY_INVALID_TOKEN, token)));
    }

    private boolean isInvalidFeignToken(String feignToken) {
        return !StringUtils.isBlank(feignToken) && !Objects.isNull(RedisUtils.get(String.format(Cache.KEY_INVALID_FEIGN_TOKEN, feignToken)));
    }
}
