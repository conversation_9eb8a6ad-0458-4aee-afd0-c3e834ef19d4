package com.senox.user.convert;

import com.senox.user.domain.Activity;
import com.senox.user.vo.ActivityVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/16 9:11
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface ActivityConvertor {

    Activity toDo(ActivityVo vo);

    List<Activity> toDo(List<ActivityVo> voList);

    ActivityVo toVo(Activity domain);

    List<ActivityVo> toVo(List<Activity> domainList);
}
