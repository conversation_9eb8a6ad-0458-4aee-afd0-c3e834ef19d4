package com.senox.user.mapper;

import com.senox.user.domain.CustomerExt;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @Date 2021/1/19 15:18
 */
@Mapper
@Repository
public interface CustomerExtMapper {

    /**
     * 添加客户扩展信息
     * @param customerExt
     * @return
     */
    int addCustomerExt(CustomerExt customerExt);

    /**
     * 更新客户扩展信息
     * @param customerExt
     * @return
     */
    int updateCustomerExt(CustomerExt customerExt);

    /**
     * 根据id查找客户扩展信息
     * @param id
     * @return
     */
    CustomerExt findById(Long id);

    /**
     * 根据客户id获取客户扩展信息
     * @param customerId
     * @return
     */
    CustomerExt findByCustomerId(Long customerId);


}
