package com.senox.user.convert;

import com.senox.common.convert.BaseConvert;
import com.senox.user.domain.AdminRemoteAccess;
import com.senox.user.vo.AdminRemoteAccessVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023/5/26 11:02
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface AdminRemoteAccessConvertor extends BaseConvert<AdminRemoteAccess, AdminRemoteAccessVo> {

}
