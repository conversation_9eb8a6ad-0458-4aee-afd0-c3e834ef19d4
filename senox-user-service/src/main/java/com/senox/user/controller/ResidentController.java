package com.senox.user.controller;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.user.convert.ResidentConvertor;
import com.senox.user.domain.Resident;
import com.senox.user.domain.ResidentAccess;
import com.senox.user.service.ResidentAccessService;
import com.senox.user.service.ResidentService;
import com.senox.user.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/22 14:31
 */
@Api(tags = "住户")
@RestController
@RequestMapping("/resident")
@RequiredArgsConstructor
public class ResidentController extends BaseController {

    private final ResidentService residentService;
    private final ResidentConvertor residentConvertor;
    private final ResidentAccessService residentAccessService;

    @ApiOperation("添加住户")
    @PostMapping("/add")
    public String addResident(@Validated(Add.class) @RequestBody ResidentVo residentVo) {
        Resident resident = residentConvertor.toDo(residentVo);
        initEntityCreator(resident);
        initEntityModifier(resident);
        return residentService.addResident(resident);
    }

    @ApiOperation("修改住户,不修改人脸")
    @PostMapping("/update")
    public void updateResident(@Validated(Update.class) @RequestBody ResidentVo residentVo) {
        Resident resident = residentConvertor.toDo(residentVo);
        initEntityModifier(resident);
        residentService.updateResident(resident);
    }

    @ApiOperation("只修改人脸")
    @PostMapping("/update/face")
    public void updateFaceUrl(@Validated(Update.class) @RequestBody ResidentFaceUrlVo residentFaceUrlVo) {
        residentService.updateFaceUrl(residentFaceUrlVo, getUserInContext());
    }

    @ApiOperation("删除住户")
    @PostMapping("/delete/{id}")
    public void deleteResident(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        residentService.deleteResident(id);
    }

    @ApiOperation("获取住户")
    @GetMapping("/get/{id}")
    public ResidentVo findResidentById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        Resident resident = residentService.findById(id);
        return resident != null ? residentConvertor.toV(resident) : null;
    }

    @ApiOperation("根据住户编号查找住户")
    @GetMapping("/getByNo")
    public ResidentVo findResidentByResidentNo(@RequestParam String residentNo) {
        Resident resident = residentService.findByResidentNo(residentNo);
        return resident != null ? residentConvertor.toV(resident) : null;
    }

    @ApiOperation("根据身份证查找住户")
    @GetMapping("/getByIdNum")
    public ResidentVo findResidentByIdNum(@RequestParam String idNum) {
        Resident resident = residentService.findByIdNum(idNum);
        return resident != null ? residentConvertor.toV(resident) : null;
    }

    @ApiOperation("住户列表")
    @PostMapping("/list")
    public PageResult<ResidentVo> residentList(@RequestBody ResidentSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        PageResult<Resident> page = residentService.list(search);
        return PageResult.convertPage(page, residentConvertor::toV);
    }

    @ApiOperation("添加住户权限")
    @PostMapping("/access/add")
    public void addResidentAccess(@Validated(Add.class) @RequestBody ResidentAccessVo residentAccessVo) {
        if (CollectionUtils.isEmpty(residentAccessVo.getDeviceList())) {
            throw new BusinessException("设备不能为空");
        }
        List<ResidentAccess> accessList = new ArrayList<>();
        residentAccessVo.getDeviceList().forEach(x -> {
            ResidentAccess access = new ResidentAccess();
            access.setAccess(residentAccessVo.getAccess());
            access.setDeviceId(x);
            access.setResidentNo(residentAccessVo.getResidentNo());
            access.setContractNo(residentAccessVo.getContractNo());
            access.setRealtySerial(residentAccessVo.getRealtySerial());
            initEntityCreator(access);
            initEntityModifier(access);
            accessList.add(access);
        });
        residentAccessService.addResidentAccess(residentAccessVo.getResidentNo(), residentAccessVo.getRealtySerial(), accessList);
    }

    @ApiOperation("根据id列表删除住户权限")
    @PostMapping("/access/deleteById")
    public void deleteResidentAccess(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new InvalidParameterException();
        }
        residentAccessService.deleteResidentAccess(ids);
    }

    @ApiOperation("根据住户编号查询住户权限")
    @GetMapping("/access/byNo")
    public ResidentAccessResultVo residentAccessResultByNo(@RequestParam String residentNo) {
        if (StringUtils.isBlank(residentNo)) {
            throw new InvalidParameterException();
        }
        return residentAccessService.residentAccessResultByNo(residentNo);
    }

    @ApiOperation("检验住户")
    @PostMapping("/check")
    public ResidentVo checkResident(@Validated(Add.class) @RequestBody ResidentCheckVo residentCheckVo) {
        Resident resident = residentService.checkResident(residentCheckVo);
        return resident != null ? residentConvertor.toV(resident) : null;
    }

    @ApiOperation("根据合同号删除住户权限")
    @GetMapping("/access/deleteByContractNo")
    public void deleteAccessByContractNo(@RequestParam String contractNo) {
        residentAccessService.deleteAccessByContractNo(contractNo);
    }

    @ApiOperation("续签恢复用户门禁权限")
    @GetMapping("/renewal/access")
    public void renewalAccess(@RequestParam String oldContractNo, @RequestParam String newContractNo) {
        residentAccessService.renewalAccess(oldContractNo, newContractNo);
    }

    @ApiOperation("门禁权限同步")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/access/sync/{deviceId}")
    public void residentAccessSync(@PathVariable Long deviceId, @RequestParam Long targetDeviceId) {
        residentAccessService.residentAccessSync(deviceId, targetDeviceId);
    }
}
