//package com.senox.user;
//
//import com.senox.user.config.GlobalRedisAutoConfiguration;
//import com.senox.user.utils.DockerManager;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.boot.test.context.TestConfiguration;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Primary;
//import org.springframework.data.redis.connection.RedisConnectionFactory;
//import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
//import org.springframework.data.redis.core.RedisTemplate;
//import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
//import org.springframework.data.redis.serializer.StringRedisSerializer;
//import org.testcontainers.containers.GenericContainer;
//
//import javax.annotation.PreDestroy;
//
///**
// * 简化版TestcontainersManager
// */
//@TestConfiguration
//public class TestcontainersManagerSimple {
//
//    private static final Logger logger = LoggerFactory.getLogger(TestcontainersManagerSimple.class);
//
//    // 保留必要的静态变量
//    private static GenericContainer<?> GLOBAL_REDIS_CONTAINER;
//    public static RedisTemplate<String, Object> GLOBAL_REDIS_TEMPLATE;
//
//    /**
//     * 创建Redis连接工厂
//     * Spring保证单例，无需手动同步
//     */
//    @Bean
//    @Primary
//    public RedisConnectionFactory redisConnectionFactory() {
//        logger.info("=== RedisConnectionFactory被请求，开始按需初始化 ===");
//
//        // 确保Docker运行
//        DockerManager.ensureDockerRunning();
//
//        // 启动容器（Spring单例保证只执行一次）
//        if (GLOBAL_REDIS_CONTAINER == null) {
//            initializeContainer();
//        }
//
//        return createConnectionFactory();
//    }
//
//    /**
//     * 创建RedisTemplate
//     * Spring单例，无需手动管理
//     */
//    @Bean
//    @Primary
//    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
//        logger.info("创建全局RedisTemplate，使用容器连接");
//
//        RedisTemplate<String, Object> template = new RedisTemplate<>();
//        template.setConnectionFactory(connectionFactory);
//
//        // 配置序列化器
//        template.setKeySerializer(new StringRedisSerializer());
//        template.setValueSerializer(new GenericJackson2JsonRedisSerializer());
//        template.setHashKeySerializer(new StringRedisSerializer());
//        template.setHashValueSerializer(new GenericJackson2JsonRedisSerializer());
//
//        template.afterPropertiesSet();
//
//        // 保存全局引用
//        GLOBAL_REDIS_TEMPLATE = template;
//        logger.info("全局RedisTemplate创建完成");
//
//        // 触发自动配置
//        triggerAutoConfiguration();
//
//        return template;
//    }
//
//    /**
//     * 初始化容器
//     */
//    private void initializeContainer() {
//
//        // 版本不固定
//        String redisVersion ="redis:6.2.6";
//
//        GLOBAL_REDIS_CONTAINER = new GenericContainer<>(redisVersion)
//            .withExposedPorts(6379)    // 暴露6379端口，docker紧接着随机分配
//            .withReuse(true);  // 确保容器复用
//
//        GLOBAL_REDIS_CONTAINER.start();
//
//        logger.info("全局Redis容器启动成功: {}:{}",
//            GLOBAL_REDIS_CONTAINER.getHost(), GLOBAL_REDIS_CONTAINER.getMappedPort(6379));
//    }
//
//    /**
//     * 创建连接工厂
//     */
//    private RedisConnectionFactory createConnectionFactory() {
//        LettuceConnectionFactory factory = new LettuceConnectionFactory(
//            GLOBAL_REDIS_CONTAINER.getHost(),
//            // 获取docker分配的随机端口
//            GLOBAL_REDIS_CONTAINER.getMappedPort(6379)
//        );
//        factory.afterPropertiesSet();
//
//        logger.info("Redis连接工厂创建完成: {}:{}",
//            GLOBAL_REDIS_CONTAINER.getHost(), GLOBAL_REDIS_CONTAINER.getMappedPort(6379));
//
//        return factory;
//    }
//
//    /**
//     * 触发自动配置
//     * 异步执行，避免阻塞Spring启动
//     */
//    private void triggerAutoConfiguration() {
//        new Thread(() -> {
//            try {
//                Thread.sleep(1000);
//                GlobalRedisAutoConfiguration.autoConfigureRedisUtils();
//            } catch (Exception e) {
//                logger.warn("触发Redis自动配置失败: {}", e.getMessage());
//            }
//        }).start();
//    }
//
//    /**
//     * 清理资源
//     */
//    @PreDestroy
//    public void cleanupContainers() {
//        logger.info("=== 全局容器管理器：开始清理容器 ===");
//
//        if (GLOBAL_REDIS_CONTAINER != null) {
//            try {
//                GLOBAL_REDIS_CONTAINER.stop();
//                logger.info("Redis容器已停止");
//            } catch (Exception e) {
//                logger.error("停止Redis容器异常: {}", e.getMessage(), e);
//            }
//        }
//
//        // 检查是否需要关闭Docker
//        boolean autoShutdownDocker = true;
//        if (autoShutdownDocker) {
//            logger.info("执行Docker关闭...");
//            DockerManager.stopDocker();
//        }
//
//        // 清理引用
//        GLOBAL_REDIS_CONTAINER = null;
//        GLOBAL_REDIS_TEMPLATE = null;
//
//        logger.info("=== 全局容器管理器：清理完成，静态引用已清空 ===");
//    }
//}
