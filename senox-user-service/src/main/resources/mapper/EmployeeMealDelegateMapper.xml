<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.user.mapper.EmployeeMealDelegateMapper">

    <resultMap id="Result_EmployeeMealDelegator" type="com.senox.user.domain.EmployeeMealDelegate">
        <id property="id" jdbcType="BIGINT" column="id" />
        <result property="employeeId" jdbcType="BIGINT" column="employee_id" />
        <result property="delegateId" jdbcType="BIGINT" column="delegate_id" />
        <result property="disabled" jdbcType="TINYINT" column="is_disabled" />
        <result property="creatorId" jdbcType="BIGINT" column="creator_id" />
        <result property="creatorName" jdbcType="VARCHAR" column="creator_name" />
        <result property="createTime" jdbcType="TIMESTAMP" column="create_time" />
        <result property="modifierId" jdbcType="BIGINT" column="modifier_id" />
        <result property="modifierName" jdbcType="VARCHAR" column="modifier_name" />
        <result property="modifiedTime" jdbcType="TIMESTAMP" column="modified_time" />
    </resultMap>

    <resultMap id="Result_Employee" type="com.senox.user.domain.Employee">
        <id property="id" jdbcType="BIGINT" column="id" />
        <result property="username" jdbcType="VARCHAR" column="username" />
        <result property="companyName" jdbcType="VARCHAR" column="company_name" />
    </resultMap>

    <resultMap id="Result_Company" type="com.senox.user.domain.Company">
        <id property="id" jdbcType="BIGINT" column="id" />
        <result property="companyName" jdbcType="VARCHAR" column="company_name" />
    </resultMap>

    <!-- 批量添加代理人关系 -->
    <insert id="batchAddDelegate" parameterType="java.util.List">
        INSERT INTO u_employee_meal_delegate(
            employee_id, delegate_id, type, creator_id, creator_name, create_time, modifier_id, modifier_name, modified_time
        ) VALUES
        <foreach collection="delegateList" item="item" separator=",">
        (
            #{item.employeeId}, #{item.delegateId}, #{item.type}, #{item.creatorId}, #{item.creatorName}, NOW(), #{item.modifierId}, #{item.modifierName}, NOW()
        )
        </foreach>
    </insert>

    <!-- 批量删除代理关系 -->
    <delete id="batchDeleteDelegator" parameterType="java.util.List">
        DELETE FROM u_employee_meal_delegate
        WHERE id IN <foreach collection="ids" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </delete>

    <!-- 根据代理人查找代理列表 -->
    <select id="listByEmployee" parameterType="java.lang.Long" resultMap="Result_EmployeeMealDelegator">
        SELECT id, employee_id, delegate_id, type FROM u_employee_meal_delegate WHERE is_disabled = 0 AND employee_id = #{employeeId}
    </select>

    <!-- 根据代理人id查找被代理的员工信息 -->
    <select id="listDelegateEmployee" parameterType="java.lang.Long" resultMap="Result_Employee">
        SELECT e.id, e.username, e.company_name
        FROM u_employee_meal_delegate d
            LEFT JOIN u_employee e ON d.delegate_id = e.id
        WHERE d.employee_id = #{employeeId} AND d.type = 1 AND e.is_disabled = 0
    </select>

    <!-- 根据代理人id查找被代理的公司信息 -->
    <select id="findDelegateCompany" parameterType="java.lang.Long" resultMap="Result_Company">
        SELECT c.id, c.company_name
        FROM u_employee_meal_delegate d
            INNER JOIN u_company c on d.delegate_id = c.id
        WHERE d.employee_id = #{employeeId} AND d.type = 2 AND c.is_disabled = 0
        LIMIT 1
    </select>

</mapper>