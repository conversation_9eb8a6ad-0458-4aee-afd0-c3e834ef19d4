package com.senox.user.controller;

import com.senox.context.AdminUserDto;
import com.senox.user.authcredentials.dto.AccessTokenResultDto;
import com.senox.user.service.AuthCredentialsService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024-2-23
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/oauth")
public class OAuthController {
    private final AuthCredentialsService authCredentialsService;

    @GetMapping("/accessToken")
    public AccessTokenResultDto accessToken(@RequestParam String appKey, @RequestParam String appSecret) {
        return authCredentialsService.accessToken(appKey, appSecret);
    }

    @GetMapping("/userFromAccessToken")
    public AdminUserDto userFromAccessToken(@RequestParam String accessToken) {
        return authCredentialsService.userFromAccessToken(accessToken);
    }

}
