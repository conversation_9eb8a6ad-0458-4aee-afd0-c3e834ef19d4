<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.user.mapper.MerchantAuthApplyMapper">

    <!-- 商户权限申请记录数合计 -->
    <select id="countApply" parameterType="com.senox.user.vo.MerchantAuthApplySearchVo" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM u_merchant_auth_apply a
        <where>
            <if test="merchantName != null and merchantName != ''">
                AND a.merchant_name = #{merchantName}
            </if>
            <if test="rcSerial != null and rcSerial != ''">
                AND a.rc_serial = #{rcSerial}
            </if>
            <if test="rc != null">
                <choose>
                    <when test="rc">AND a.rc_serial != ''</when>
                    <otherwise>AND a.rc_serial = ''</otherwise>
                </choose>
            </if>
            <if test="bicycleAuth != null">
                <choose>
                    <when test="bicycleAuth">AND a.bicycle_auth = 1</when>
                    <otherwise>AND a.bicycle_auth = 0</otherwise>
                </choose>
            </if>
            <if test="dryAuth != null">
                <choose>
                    <when test="dryAuth">AND a.dry_auth = 1</when>
                    <otherwise>AND a.dry_auth = 0</otherwise>
                </choose>
            </if>
            <if test="statusList != null and statusList.size() > 0">
                AND a.audit_status IN <foreach collection="statusList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="createOpenid != null and createOpenid != ''">
                AND a.create_openid = #{createOpenid}
            </if>
            <if test="applyTimeStart != null">
                AND a.apply_time >= #{applyTimeStart}
            </if>
            <if test="applyTimeEnd != null">
                AND a.apply_time <![CDATA[<=]]> #{applyTimeEnd}
            </if>
            <if test="auditTimeStart != null">
                AND a.audit_time >= #{auditTimeStart}
            </if>
            <if test="auditTimeEnd != null">
                AND a.audit_time <![CDATA[<=]]> #{auditTimeEnd}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (a.merchant_name LIKE CONCAT('%', #{keyword}, '%') OR a.contact LIKE CONCAT('%', #{keyword}, '%') OR a.idcard LIKE CONCAT('%', #{idcard}, '%'))
            </if>
        </where>
    </select>

    <!-- 权限申请列表 -->
    <select id="listApply" parameterType="com.senox.user.vo.MerchantAuthApplySearchVo" resultType="com.senox.user.vo.MerchantAuthApplyListVo">
        SELECT a.id, a.merchant_id, a.merchant_name, a.contact, a.idcard, a.address, a.rc_serial, a.bicycle_auth, a.dry_auth, a.referral_code
            , a.apply_time, a.audit_status, IFNULL(au.real_name, au.username) AS audit_man
            , a.audit_time
        FROM u_merchant_auth_apply a
        LEFT JOIN u_admin_user au on au.id = a.audit_id
        <where>
            <if test="merchantName != null and merchantName != ''">
                AND a.merchant_name = #{merchantName}
            </if>
            <if test="rcSerial != null and rcSerial != ''">
                AND a.rc_serial = #{rcSerial}
            </if>
            <if test="rc != null">
                <choose>
                    <when test="rc">AND a.rc_serial != ''</when>
                    <otherwise>AND a.rc_serial = ''</otherwise>
                </choose>
            </if>
            <if test="bicycleAuth != null">
                <choose>
                    <when test="bicycleAuth">AND a.bicycle_auth = 1</when>
                    <otherwise>AND a.bicycle_auth = 0</otherwise>
                </choose>
            </if>
            <if test="dryAuth != null">
                <choose>
                    <when test="dryAuth">AND a.dry_auth = 1</when>
                    <otherwise>AND a.dry_auth = 0</otherwise>
                </choose>
            </if>
            <if test="statusList != null and statusList.size() > 0">
                AND a.audit_status IN <foreach collection="statusList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="createOpenid != null and createOpenid != ''">
                AND a.create_openid = #{createOpenid}
            </if>
            <if test="applyTimeStart != null">
                AND a.apply_time >= #{applyTimeStart}
            </if>
            <if test="applyTimeEnd != null">
                AND a.apply_time <![CDATA[<=]]> #{applyTimeEnd}
            </if>
            <if test="auditTimeStart != null">
                AND a.audit_time >= #{auditTimeStart}
            </if>
            <if test="auditTimeEnd != null">
                AND a.audit_time <![CDATA[<=]]> #{auditTimeEnd}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (a.merchant_name LIKE CONCAT('%', #{keyword}, '%') OR a.contact LIKE CONCAT('%', #{keyword}, '%') OR a.idcard LIKE CONCAT('%', #{idcard}, '%'))
            </if>
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY a.audit_status, a.audit_time DESC, a.id
            </otherwise>
        </choose>
        <if test="page">
            limit ${offset}, ${pageSize}
        </if>
    </select>

    <select id="auditCount" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM u_merchant_auth_apply where audit_status = 0
    </select>

</mapper>
