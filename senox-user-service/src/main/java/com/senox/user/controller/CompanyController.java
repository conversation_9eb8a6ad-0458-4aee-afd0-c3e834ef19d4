package com.senox.user.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.user.domain.Company;
import com.senox.user.service.CompanyService;
import com.senox.user.vo.CompanyVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/4/1 10:20
 */
@Api(tags = "企业")
@RestController
@RequestMapping("/company")
public class CompanyController extends BaseController {

    @Autowired
    public CompanyService companyService;

    @ApiOperation("添加企业")
    @PostMapping("/add")
    public Long addCompany(@Validated({Add.class}) @RequestBody CompanyVo companyVo) {
        Company company = companyVo2Entity(companyVo);
        initEntityCreator(company);
        initEntityModifier(company);
        return companyService.addCompany(company);
    }

    @ApiOperation("更新企业")
    @PostMapping("/update")
    public void updateCompany(@Validated({Update.class}) @RequestBody CompanyVo companyVo) {
        if (companyVo.getId() < 1L) {
            throw new InvalidParameterException("无效的id");
        }

        Company company = companyVo2Entity(companyVo);
        initEntityModifier(company);
        companyService.updateCompany(company);
    }

    @ApiOperation("根据id获取企业")
    @GetMapping("/get/{id}")
    public CompanyVo getCompany(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }
        Company company = companyService.findById(id);
        return company == null ? null : company2Vo(company);
    }

    @ApiOperation("企业列表")
    @PostMapping("/list")
    public List<CompanyVo> listCompany() {
        List<Company> resultList = companyService.listALl();
        return resultList == null ? Collections.emptyList()
                : resultList.stream().map(this::company2Vo).collect(Collectors.toList());
    }

    /**
     * 企业视图对象转实体对象
     * @param vo
     * @return
     */
    private Company companyVo2Entity(CompanyVo vo) {
        Company result = new Company();
        BeanUtils.copyProperties(vo, result);
        return result;
    }

    /**
     * 企业实体对象转视图对象
     * @param company
     * @return
     */
    private CompanyVo company2Vo(Company company) {
        CompanyVo result = new CompanyVo();
        BeanUtils.copyProperties(company, result);
        return result;
    }
}
