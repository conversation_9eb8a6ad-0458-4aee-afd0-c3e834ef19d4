package com.senox.user.controller;

import com.senox.common.utils.RequestUtils;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.context.AdminUserDto;
import com.senox.user.service.AuthCredentialsService;
import com.senox.user.vo.AuthCredentialsSearchVo;
import com.senox.user.vo.AuthCredentialsVo;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-8-29
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/auth/credentials")
public class AuthCredentialsController extends BaseController {
    private final AuthCredentialsService authCredentialsService;


    @GetMapping("/add/{userId}")
    public void add(@PathVariable Long userId) {
        authCredentialsService.generateByUser(userId);
    }

    @PostMapping("/list")
    public List<AuthCredentialsVo> list(@RequestBody AuthCredentialsSearchVo searchVo) {
        return authCredentialsService.list(searchVo);
    }

    @PostMapping("/list/page")
    public PageResult<AuthCredentialsVo> listPage(@RequestBody AuthCredentialsSearchVo searchVo) {
        return authCredentialsService.listPage(searchVo);
    }

    /**
     * 根据appKey查询身份验证凭证
     *
     * @return 查询到的身份验证凭证
     */
    @GetMapping("/getByAppKey")
    public AuthCredentialsVo getByAppKey() {
        //获取上下文请求
        HttpServletRequest request = RequestUtils.getRequest();
        //从请求中拿appKey
        String appKey = request.getHeader(AdminContext.HEADER_AUTH_CREDENTIALS);
        return authCredentialsService.getByAppKey(appKey);
    }


    /**
     * 获取用户
     *
     * @param appKey appKey
     * @return 用户
     */
    @GetMapping("/getUser/{appKey}")
    public AdminUserDto getUser(@PathVariable String appKey) {
        return authCredentialsService.getUserByAppKey(appKey);
    }


}
