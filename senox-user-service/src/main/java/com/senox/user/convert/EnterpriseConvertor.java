package com.senox.user.convert;

import com.senox.user.domain.Enterprise;
import com.senox.user.vo.EnterpriseEditVo;
import com.senox.user.vo.EnterpriseViewVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2024/8/13 10:36
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface EnterpriseConvertor {


    /**
     * 编辑视图转实体
     * @param vo
     * @return
     */
    Enterprise editVoToDo(EnterpriseEditVo vo);

    /**
     * 实体转视图
     * @param domain
     * @return
     */
    EnterpriseViewVo toViewVo(Enterprise domain);
}
