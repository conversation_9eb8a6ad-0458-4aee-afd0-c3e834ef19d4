package com.senox.user.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminUserDto;
import com.senox.user.domain.FeedBack;
import com.senox.user.mapper.FeedBackMapper;
import com.senox.user.vo.FeedBackReplyStateVo;
import com.senox.user.vo.FeedBackSearchVo;
import com.senox.user.vo.FeedBackVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/31 16:47
 */
@AllArgsConstructor
@Service
public class FeedBackService extends ServiceImpl<FeedBackMapper, FeedBack> {

    private final FeedBackMediaService feedBackMediaService;

    /**
     * 添加建议
     *
     * @param feedBack
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addFeedBack(FeedBack feedBack, List<String> urls) {
        checkAnonymous(feedBack);
        if (feedBack.getCreateTime() == null) {
            feedBack.setCreateTime(LocalDateTime.now());
        }
        if (feedBack.getModifiedTime() == null) {
            feedBack.setModifiedTime(LocalDateTime.now());
        }
        save(feedBack);
        if (!CollectionUtils.isEmpty(urls)) {
            feedBackMediaService.saveFeedBackMedia(feedBack.getId(), urls);
        }
        return feedBack.getId();
    }

    /**
     * 修改回复状态
     * @param stateVo
     * @param adminUserDto
     */
    public void updateFeedBackState(FeedBackReplyStateVo stateVo, AdminUserDto adminUserDto){
        FeedBack feedBack = new FeedBack();
        feedBack.setId(stateVo.getId());
        feedBack.setReplyState(stateVo.getReplyState());
        feedBack.setModifierId(adminUserDto.getUserId());
        feedBack.setModifierName(adminUserDto.getUsername());
        feedBack.setModifiedTime(LocalDateTime.now());
        updateById(feedBack);
    }

    /**
     * 查看建议及包含的回复信息
     * @param id
     * @param isDetail
     * @return
     */
    public FeedBackVo getFeedBackResultById(Long id, Boolean isDetail) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }

        return getBaseMapper().getFeedBackResultById(id,isDetail);
    }

    /**
     * 建议列表
     *
     * @param search
     * @return
     */
    public PageResult<FeedBackVo> feedBackList(FeedBackSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }

        // 总记录数
        int totalSize = getBaseMapper().countFeedBack(search);
        search.prepare();

        if (totalSize <= search.getOffset()) {
            return PageResult.emptyPage();
        }

        // list
        List<FeedBackVo> resultList = getBaseMapper().listFeedBack(search);
        return PageUtils.resultPage(search, totalSize, resultList);
    }


    /**
     * 判断是否匿名提交
     *
     * @param feedBack
     */
    public void checkAnonymous(FeedBack feedBack) {
        if (feedBack == null) {
            return;
        }
        if (BooleanUtils.isTrue(feedBack.getAnonymous())) {
            feedBack.setName("匿名");
            feedBack.setContact(StringUtils.EMPTY);
        } else {
            //不匿名提交
            if (StringUtils.isBlank(feedBack.getName()) || StringUtils.isBlank(feedBack.getContact())) {
                throw new BusinessException("非匿名需填写姓名和联系方式");
            }
        }
    }
}
