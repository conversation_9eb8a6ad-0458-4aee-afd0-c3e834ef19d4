package com.senox.user.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.user.constant.UserConst;
import com.senox.user.domain.Company;
import com.senox.user.mapper.CompanyMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/4/1 9:56
 */
@Service
public class CompanyService {

    @Autowired
    private CompanyMapper companyMapper;

    /**
     * 添加企业
     * @param company
     * @return
     */
    public Long addCompany(Company company) {
        if (StringUtils.isBlank(company.getCompanyName())) {
            return 0L;
        }
        // 校验是否重名
        if (isCompanyNameDuplicated(null, company.getCompanyName())) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR, "公司名已存在");
        }

        // 新增企业
        if (company.getOrderNo() == null) {
            company.setOrderNo(UserConst.ORDER_DF_50);
        }
        int result = companyMapper.addCompany(company);

        // clear cache
        if (result > 0) {
            RedisUtils.del(UserConst.Cache.KEY_COMPANY);
        }

        return result > 0 ? company.getId() : 0L;
    }

    /**
     * 更新企业
     * @param company
     * @return
     */
    public boolean updateCompany(Company company) {
        if (!WrapperClassUtils.biggerThanLong(company.getId(), 0L)) {
            return false;
        }

        // 删除企业
        if (BooleanUtils.isTrue(company.getDisabled())) {
            Company dbCompany = findById(company.getId());
            if (dbCompany != null) {
                company.setCompanyName(dbCompany.getCompanyName() + "_" + company.getId());
            }
        }

        // 校验是否重名
        if (isCompanyNameDuplicated(company.getId(), company.getCompanyName())) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR, "公司名已存在");
        }

        boolean result = companyMapper.updateCompany(company) > 0;

        // clear cache
        if (result) {
            RedisUtils.del(UserConst.Cache.KEY_COMPANY);
        }
        return result;
    }

    /**
     * 根据id查找企业
     * @param id
     * @return
     */
    public Company findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return companyMapper.findById(id);
    }

    /**
     * 企业列表
     * @return
     */
    public List<Company> listALl() {
        List<Company> resultList = null;

        // load from cache
        String cacheValue = RedisUtils.get(UserConst.Cache.KEY_COMPANY);
        if (!StringUtils.isBlank(cacheValue)) {
            resultList = JsonUtils.json2GenericObject(cacheValue, new TypeReference<List<Company>>() {});
        }

        // load from db
        if (CollectionUtils.isEmpty(resultList)) {
            resultList = companyMapper.listAll();

            // set cache
            if (!CollectionUtils.isEmpty(resultList)) {
                RedisUtils.set(UserConst.Cache.KEY_COMPANY, JsonUtils.object2Json(resultList), UserConst.Cache.TTL_7D);
            }
        }
        return resultList;
    }

    /**
     * 代理企业列表
     * @return
     */
    public List<Company> listDelegateCompany() {
        List<Company> resultList = companyMapper.listDelegateCompany();
        return resultList == null ? Collections.emptyList() : resultList;
    }

    /**
     * 未报餐的代理企业
     * @param mealDate
     * @return
     */
    public List<Company> listNoBookedDelegateCompany(LocalDate mealDate) {
        return companyMapper.listNoBookedDelegateCompany(mealDate);
    }

    /**
     * 判断是否重复的公司名
     * @param id
     * @param name
     * @return
     */
    private boolean isCompanyNameDuplicated(Long id, String name) {
        if (StringUtils.isBlank(name)) {
            return false;
        }

        Company company = companyMapper.findByName(name);
        if (company == null) {
            return false;
        }
        return !WrapperClassUtils.biggerThanLong(id, 0L) || !Objects.equals(id, company.getId());
    }

}
