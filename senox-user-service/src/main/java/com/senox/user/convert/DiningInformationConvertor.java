package com.senox.user.convert;

import com.senox.user.domain.DiningInformation;
import com.senox.user.vo.DiningInformationVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface DiningInformationConvertor {

    /**
     * 视图对象装实体对象
     * @param vo
     * @return
     */
    DiningInformation toDo(DiningInformationVo vo);

    /**
     * 视图对象列表装实体对象列表
     * @param voList
     * @return
     */
    List<DiningInformation> toDo(List<DiningInformationVo> voList);

    /**
     * 实例对象转视图对象
     * @param diningInformation
     * @return
     */
    DiningInformationVo toVo(DiningInformation diningInformation);
}
