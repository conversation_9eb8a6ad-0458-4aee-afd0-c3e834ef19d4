package com.senox.user.service;

import com.senox.common.utils.PageUtils;
import com.senox.common.vo.PageResult;
import com.senox.user.mapper.IcCardMapper;
import com.senox.user.vo.IcCardSearchVo;
import com.senox.user.vo.IcCardVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/3/16 10:00
 */
@Service
public class IcCardService {

    @Autowired
    private IcCardMapper icCardMapper;

    /**
     * IC卡分页列表
     * @param searchVo
     * @return
     */
    public PageResult<IcCardVo> listIcCardPage(IcCardSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }

        // 总记录数
        int totalSize = icCardMapper.countIcCard(searchVo);
        searchVo.prepare();

        if (totalSize <= searchVo.getOffset()) {
            return PageResult.emptyPage();
        }

        // list
        List<IcCardVo> resultList = icCardMapper.listIcCard(searchVo);
        return PageUtils.resultPage(searchVo, totalSize, resultList);
    }
}
