package com.senox.user.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.user.convert.MerchantConvert;
import com.senox.user.domain.Merchant;
import com.senox.user.service.MerchantService;
import com.senox.user.vo.MerchantSearchVo;
import com.senox.user.vo.MerchantVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-10-31
 */
@Api(tags = "商户")
@RequiredArgsConstructor
@RequestMapping("/merchant")
@RestController
public class MerchantController extends BaseController {

    private final MerchantService merchantService;
    private final MerchantConvert merchantConvert;

    @ApiOperation("添加")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/add")
    public Long add(@Validated @RequestBody MerchantVo merchant) {
        Merchant entity = merchantConvert.todo(merchant);
        initEntityCreator(entity);
        return merchantService.add(entity);
    }

    @ApiOperation("批量新增商户，返回已存在的商户列表")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/batchAdd")
    public List<MerchantVo> batchAddMerchant(@RequestBody List<MerchantVo> list) {
        list = list.stream().filter(x -> !StringUtils.isBlank(x.getName())).collect(Collectors.toList());
        if (list.size() != list.stream().map(MerchantVo::getName).count()) {
            throw new InvalidParameterException("存在重复的商户名");
        }

        List<Merchant> merchantList = merchantConvert.todo(list);
        for (Merchant merchant : merchantList) {
            merchant.setRcSerial(StringUtils.trimToEmpty(merchant.getRcSerial()));
            merchant.setRcTaxHeader(StringUtils.trimToEmpty(merchant.getRcTaxHeader()));
            merchant.setName(StringUtils.trimToEmpty(merchant.getName()));
            merchant.setIdcard(StringUtils.trimToEmpty(merchant.getIdcard()));
            merchant.setContact(StringUtils.trimToEmpty(merchant.getContact()));
            initEntityCreator(merchant);
            initEntityModifier(merchant);
        }
        List<String> existName = merchantService.batchAddMerchant(merchantList);
        return list.stream().filter(x -> existName.contains(StringUtils.trimToEmpty(x.getName()))).collect(Collectors.toList());
    }

    @ApiOperation("更新")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/update")
    public void update(@RequestBody MerchantVo merchant) {
        if (!WrapperClassUtils.biggerThanLong(merchant.getId(), 0L)) {
            throw new InvalidParameterException();
        }

        Merchant entity = merchantConvert.todo(merchant);
        initEntityModifier(entity);
        merchantService.update(entity);
    }

    @ApiOperation("新增或更新商户")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/save")
    public Long saveMerchant(@Validated @RequestBody MerchantVo merchant) {
        Merchant entity = merchantConvert.todo(merchant);
        initEntityCreator(entity);
        initEntityModifier(entity);

        return merchantService.saveMerchant(entity);
    }

    @ApiOperation("删除")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/delete")
    public void delete(@RequestParam Long id) {
        Merchant merchant = new Merchant();
        merchant.setId(id);
        merchant.setDisabled(Boolean.TRUE);
        initEntityModifier(merchant);
        merchantService.delete(merchant);
    }

    @ApiOperation("根据id查询商户")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/get/{id}")
    public MerchantVo findById(@PathVariable Long id){
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        Merchant merchant = merchantService.findById(id);
        return merchant == null ? null : merchantConvert.toVo(merchant);
    }

    @ApiOperation("根据商户名查询商户")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/getByName")
    public MerchantVo findByName(@RequestParam String name){
        if (StringUtils.isBlank(name)) {
            throw new InvalidParameterException();
        }

        Merchant merchant = merchantService.findByName(name);
        return merchant == null ? null : merchantConvert.toVo(merchant);
    }

    @ApiOperation("根据冷藏编号查询商户")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/getByRc")
    public MerchantVo findByRcSerial(@RequestParam String rcSerial) {
        if (StringUtils.isBlank(rcSerial)) {
            throw new InvalidParameterException();
        }

        Merchant merchant = merchantService.findByRcSerial(rcSerial);
        return merchant == null ? null : merchantConvert.toVo(merchant);
    }

    @ApiOperation("根据冷藏编号集合查询商户")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/getByRcList")
    public List<MerchantVo> findByRcSerialList(@RequestBody List<String> rcSerialList) {
        return merchantConvert.toVo(merchantService.findByRcSerialList(rcSerialList));
    }

    @ApiOperation("根据手机号查询商户")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/getByContact")
    public List<MerchantVo> findByContact(@RequestParam String contact) {
        if (StringUtils.isBlank(contact)) {
            throw new InvalidParameterException();
        }
        return merchantConvert.toVo(merchantService.findByContact(contact));
    }

    @ApiOperation("列表")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/list")
    public List<MerchantVo> list(@RequestBody MerchantSearchVo search) {
        search.setPage(false);
        List<Merchant> resultList = merchantService.listMerchant(search);
        return merchantConvert.toVo(resultList);
    }

    @ApiOperation("视图列表")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/listView")
    public List<MerchantVo> listView(@RequestBody MerchantSearchVo search) {
        search.setPage(false);
        return merchantService.listMerchantView(search);
    }

    @ApiOperation("分页列表")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/page")
    public PageResult<MerchantVo> listPage(@RequestBody MerchantSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        PageResult<Merchant> resultPage = PageUtils.commonPageResult(search,
                () -> merchantService.countMerchant(search),
                () -> merchantService.listMerchant(search));
        return PageResult.convertPage(resultPage, merchantConvert::toVo);
    }

    @ApiOperation("更新商户收费标准")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/update/charges")
    public void updateCharges(@RequestParam Collection<Long> ids, @RequestParam Long chargesId, @RequestParam Boolean fullData) {
        merchantService.updateChargesBatch(ids, chargesId, fullData);
    }




}
